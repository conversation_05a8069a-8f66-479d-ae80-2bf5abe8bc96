<svg width="1200" height="900" viewBox="0 0 1200 900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
    <marker id="arrowhead-blue" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3b82f6"/>
    </marker>
    <marker id="arrowhead-green" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#059669"/>
    </marker>
    <marker id="arrowhead-purple" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#7c3aed"/>
    </marker>
    <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#dc2626"/>
    </marker>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="900" fill="#f8fafc"/>
  
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#1a202c">
    Data Flow Architecture
  </text>
  
  <!-- External Data Sources -->
  <rect x="50" y="60" width="280" height="100" fill="#fef2f2" stroke="#dc2626" stroke-width="2" rx="8"/>
  <text x="190" y="80" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#dc2626">External Data Sources</text>
  
  <!-- File System -->
  <rect x="70" y="100" width="70" height="35" fill="#fecaca" stroke="#ef4444" stroke-width="1" rx="5"/>
  <text x="105" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f1d1d">File System</text>
  <text x="105" y="128" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f1d1d">3D Models</text>
  
  <!-- Database -->
  <rect x="150" y="100" width="70" height="35" fill="#fde68a" stroke="#f59e0b" stroke-width="1" rx="5"/>
  <text x="185" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#92400e">Database</text>
  <text x="185" y="128" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#92400e">SQL Server</text>
  
  <!-- Cesium -->
  <rect x="230" y="100" width="70" height="35" fill="#fecaca" stroke="#ef4444" stroke-width="1" rx="5"/>
  <text x="265" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7f1d1d">Cesium</text>
  <text x="265" y="128" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#7f1d1d">3D Tiles</text>
  
  <!-- Data Import Layer -->
  <rect x="50" y="180" width="280" height="80" fill="#f0f9ff" stroke="#0284c7" stroke-width="2" rx="8"/>
  <text x="190" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#0284c7">Data Import Layer</text>
  
  <!-- Import Manager -->
  <rect x="80" y="215" width="100" height="35" fill="#bae6fd" stroke="#0ea5e9" stroke-width="1" rx="5"/>
  <text x="130" y="230" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#0c4a6e">Import Manager</text>
  <text x="130" y="243" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#0c4a6e">File Processing</text>
  
  <!-- Data Validation -->
  <rect x="190" y="215" width="100" height="35" fill="#bae6fd" stroke="#0ea5e9" stroke-width="1" rx="5"/>
  <text x="240" y="230" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#0c4a6e">Data Validation</text>
  <text x="240" y="243" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#0c4a6e">Format Checking</text>
  
  <!-- Core Data Processing -->
  <rect x="370" y="60" width="400" height="200" fill="#f0fdf4" stroke="#16a34a" stroke-width="2" rx="8"/>
  <text x="570" y="80" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#16a34a">Core Data Processing</text>
  
  <!-- Main Controller -->
  <rect x="390" y="100" width="120" height="40" fill="#059669" stroke="#047857" stroke-width="2" rx="5"/>
  <text x="450" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Main Controller</text>
  <text x="450" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Central Coordinator</text>
  
  <!-- Data Managers -->
  <rect x="390" y="150" width="80" height="35" fill="#bbf7d0" stroke="#10b981" stroke-width="1" rx="5"/>
  <text x="430" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#064e3b">Material</text>
  <text x="430" y="178" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#064e3b">Manager</text>
  
  <rect x="480" y="150" width="80" height="35" fill="#bbf7d0" stroke="#10b981" stroke-width="1" rx="5"/>
  <text x="520" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#064e3b">Selection</text>
  <text x="520" y="178" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#064e3b">Manager</text>
  
  <rect x="570" y="150" width="80" height="35" fill="#bbf7d0" stroke="#10b981" stroke-width="1" rx="5"/>
  <text x="610" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#064e3b">Database</text>
  <text x="610" y="178" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#064e3b">Manager</text>
  
  <rect x="660" y="150" width="80" height="35" fill="#bbf7d0" stroke="#10b981" stroke-width="1" rx="5"/>
  <text x="700" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#064e3b">Grid</text>
  <text x="700" y="178" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#064e3b">Manager</text>
  
  <!-- Business Logic (Planned) -->
  <rect x="390" y="200" width="350" height="40" fill="#dcfce7" stroke="#16a34a" stroke-width="1" rx="5" stroke-dasharray="5,5"/>
  <text x="565" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#15803d">Business Logic Layer (Planned)</text>
  <text x="565" y="230" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#15803d">Blast Area Management • Drill Hole Generation • Project Management</text>
  
  <!-- Input Processing -->
  <rect x="50" y="290" width="280" height="120" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" rx="8"/>
  <text x="190" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#f59e0b">Input Processing</text>
  
  <!-- User Input -->
  <ellipse cx="110" cy="340" rx="40" ry="20" fill="#fed7aa" stroke="#f97316" stroke-width="1"/>
  <text x="110" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#9a3412">User Input</text>
  
  <!-- Input Manager -->
  <rect x="170" y="325" width="100" height="40" fill="#fbbf24" stroke="#f59e0b" stroke-width="2" rx="5"/>
  <text x="220" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">Input Manager</text>
  <text x="220" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">Event Processing</text>
  
  <!-- Priority Manager -->
  <rect x="80" y="375" width="90" height="25" fill="#fcd34d" stroke="#f59e0b" stroke-width="1" rx="3"/>
  <text x="125" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#92400e">Priority Manager</text>
  
  <!-- Event System -->
  <rect x="180" y="375" width="90" height="25" fill="#fcd34d" stroke="#f59e0b" stroke-width="1" rx="3"/>
  <text x="225" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#92400e">Event System</text>
  
  <!-- UI Data Flow -->
  <rect x="370" y="290" width="400" height="160" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="2" rx="8"/>
  <text x="570" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#8b5cf6">UI Data Flow</text>
  
  <!-- UIManager -->
  <rect x="390" y="330" width="120" height="40" fill="#8b5cf6" stroke="#7c3aed" stroke-width="2" rx="5"/>
  <text x="450" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">UI Manager</text>
  <text x="450" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Layout Controller</text>
  
  <!-- UI Event System -->
  <rect x="520" y="330" width="120" height="40" fill="#8b5cf6" stroke="#7c3aed" stroke-width="2" rx="5"/>
  <text x="580" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">UI Event System</text>
  <text x="580" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Message Bus</text>
  
  <!-- UI Components -->
  <rect x="390" y="380" width="70" height="30" fill="#c4b5fd" stroke="#8b5cf6" stroke-width="1" rx="3"/>
  <text x="425" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#3c1361">MenuBar</text>
  <text x="425" y="405" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#3c1361">Component</text>
  
  <rect x="470" y="380" width="70" height="30" fill="#c4b5fd" stroke="#8b5cf6" stroke-width="1" rx="3"/>
  <text x="505" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#3c1361">LeftPanel</text>
  <text x="505" y="405" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#3c1361">Component</text>
  
  <rect x="550" y="380" width="70" height="30" fill="#c4b5fd" stroke="#8b5cf6" stroke-width="1" rx="3"/>
  <text x="585" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#3c1361">RightPanel</text>
  <text x="585" y="405" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#3c1361">Component</text>
  
  <rect x="630" y="380" width="70" height="30" fill="#c4b5fd" stroke="#8b5cf6" stroke-width="1" rx="3"/>
  <text x="665" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#3c1361">TopToolbar</text>
  <text x="665" y="405" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#3c1361">Component</text>
  
  <rect x="710" y="380" width="50" height="30" fill="#c4b5fd" stroke="#8b5cf6" stroke-width="1" rx="3"/>
  <text x="735" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#3c1361">StatusBar</text>
  <text x="735" y="405" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#3c1361">Component</text>
  
  <!-- Configuration Data -->
  <rect x="390" y="420" width="180" height="25" fill="#ddd6fe" stroke="#8b5cf6" stroke-width="1" rx="3"/>
  <text x="480" y="435" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#3c1361">Configuration Data (ScriptableObjects)</text>
  
  <!-- DaisyUI Framework -->
  <rect x="580" y="420" width="180" height="25" fill="#ddd6fe" stroke="#8b5cf6" stroke-width="1" rx="3"/>
  <text x="670" y="435" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#3c1361">DaisyUI Component Framework</text>
  
  <!-- 3D Scene Data -->
  <rect x="800" y="60" width="350" height="390" fill="#f1f5f9" stroke="#475569" stroke-width="2" rx="8"/>
  <text x="975" y="80" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#475569">3D Scene Data</text>
  
  <!-- Scene Objects -->
  <rect x="820" y="100" width="120" height="40" fill="#64748b" stroke="#475569" stroke-width="2" rx="5"/>
  <text x="880" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Scene Objects</text>
  <text x="880" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">GameObjects</text>
  
  <!-- Terrain Data -->
  <rect x="950" y="100" width="120" height="40" fill="#64748b" stroke="#475569" stroke-width="2" rx="5"/>
  <text x="1010" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Terrain Data</text>
  <text x="1010" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Cesium Tiles</text>
  
  <!-- Material System -->
  <rect x="820" y="150" width="80" height="35" fill="#94a3b8" stroke="#64748b" stroke-width="1" rx="3"/>
  <text x="860" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e293b">Material</text>
  <text x="860" y="178" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e293b">Cache</text>
  
  <!-- Selection Data -->
  <rect x="910" y="150" width="80" height="35" fill="#94a3b8" stroke="#64748b" stroke-width="1" rx="3"/>
  <text x="950" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e293b">Selection</text>
  <text x="950" y="178" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e293b">Data</text>
  
  <!-- Visual Elements -->
  <rect x="1000" y="150" width="80" height="35" fill="#94a3b8" stroke="#64748b" stroke-width="1" rx="3"/>
  <text x="1040" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e293b">Visual</text>
  <text x="1040" y="178" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e293b">Elements</text>
  
  <!-- Rendering Pipeline -->
  <rect x="820" y="200" width="280" height="50" fill="#e2e8f0" stroke="#94a3b8" stroke-width="1" rx="5"/>
  <text x="960" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#1e293b">Unity Rendering Pipeline (URP)</text>
  <text x="960" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e293b">Shaders • Materials • Lighting • Post-Processing</text>
  
  <!-- Camera System -->
  <rect x="820" y="270" width="130" height="35" fill="#94a3b8" stroke="#64748b" stroke-width="1" rx="3"/>
  <text x="885" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e293b">Camera System</text>
  <text x="885" y="298" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e293b">Viewport Control</text>
  
  <!-- Grid System -->
  <rect x="960" y="270" width="130" height="35" fill="#94a3b8" stroke="#64748b" stroke-width="1" rx="3"/>
  <text x="1025" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e293b">Grid System</text>
  <text x="1025" y="298" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e293b">Infinite Grid</text>
  
  <!-- Visual Feedback -->
  <rect x="820" y="320" width="280" height="40" fill="#cbd5e1" stroke="#94a3b8" stroke-width="1" rx="3"/>
  <text x="960" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#1e293b">Visual Feedback System</text>
  <text x="960" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e293b">Selection Highlights • Interaction Indicators • Status Display</text>
  
  <!-- Business Objects (Planned) -->
  <rect x="820" y="380" width="280" height="40" fill="#f1f5f9" stroke="#94a3b8" stroke-width="1" rx="3" stroke-dasharray="5,5"/>
  <text x="960" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#475569">Business Objects (Planned)</text>
  <text x="960" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#475569">Blast Areas • Drill Holes • Mining Equipment</text>
  
  <!-- Data Storage -->
  <rect x="50" y="480" width="1100" height="100" fill="#fffbeb" stroke="#f59e0b" stroke-width="2" rx="8"/>
  <text x="600" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#f59e0b">Data Storage &amp; Persistence</text>
  
  <!-- Local Storage -->
  <rect x="80" y="520" width="100" height="40" fill="#fef3c7" stroke="#f59e0b" stroke-width="1" rx="5"/>
  <text x="130" y="535" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#92400e">Local Storage</text>
  <text x="130" y="550" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#92400e">Scene Data</text>
  
  <!-- Configuration -->
  <rect x="190" y="520" width="100" height="40" fill="#fef3c7" stroke="#f59e0b" stroke-width="1" rx="5"/>
  <text x="240" y="535" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#92400e">Configuration</text>
  <text x="240" y="550" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#92400e">ScriptableObjects</text>
  
  <!-- Material Cache -->
  <rect x="300" y="520" width="100" height="40" fill="#fef3c7" stroke="#f59e0b" stroke-width="1" rx="5"/>
  <text x="350" y="535" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#92400e">Material Cache</text>
  <text x="350" y="550" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#92400e">Runtime Materials</text>
  
  <!-- Database -->
  <rect x="410" y="520" width="100" height="40" fill="#fde68a" stroke="#f59e0b" stroke-width="1" rx="5"/>
  <text x="460" y="535" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#92400e">SQL Database</text>
  <text x="460" y="550" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#92400e">Project Data</text>
  
  <!-- File System -->
  <rect x="520" y="520" width="100" height="40" fill="#fef3c7" stroke="#f59e0b" stroke-width="1" rx="5"/>
  <text x="570" y="535" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#92400e">File System</text>
  <text x="570" y="550" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#92400e">3D Models</text>
  
  <!-- Session State -->
  <rect x="630" y="520" width="100" height="40" fill="#fef3c7" stroke="#f59e0b" stroke-width="1" rx="5"/>
  <text x="680" y="535" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#92400e">Session State</text>
  <text x="680" y="550" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#92400e">UI State</text>
  
  <!-- Preferences -->
  <rect x="740" y="520" width="100" height="40" fill="#fef3c7" stroke="#f59e0b" stroke-width="1" rx="5"/>
  <text x="790" y="535" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#92400e">Preferences</text>
  <text x="790" y="550" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#92400e">User Settings</text>
  
  <!-- Temporary Data -->
  <rect x="850" y="520" width="100" height="40" fill="#fef3c7" stroke="#f59e0b" stroke-width="1" rx="5"/>
  <text x="900" y="535" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#92400e">Temporary Data</text>
  <text x="900" y="550" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#92400e">Cache &amp; Buffers</text>
  
  <!-- Data Flow Types -->
  <rect x="50" y="610" width="1100" height="80" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="2" rx="8"/>
  <text x="600" y="630" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#0ea5e9">Data Flow Types</text>
  
  <!-- Real-time Data -->
  <rect x="80" y="650" width="130" height="30" fill="#bae6fd" stroke="#0ea5e9" stroke-width="1" rx="5"/>
  <text x="145" y="665" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#0c4a6e">Real-time Data</text>
  <text x="145" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#0c4a6e">Input • Selection • Camera</text>
  
  <!-- Event-driven Data -->
  <rect x="220" y="650" width="130" height="30" fill="#bae6fd" stroke="#0ea5e9" stroke-width="1" rx="5"/>
  <text x="285" y="665" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#0c4a6e">Event-driven Data</text>
  <text x="285" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#0c4a6e">UI Events • Actions</text>
  
  <!-- Cached Data -->
  <rect x="360" y="650" width="130" height="30" fill="#bae6fd" stroke="#0ea5e9" stroke-width="1" rx="5"/>
  <text x="425" y="665" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#0c4a6e">Cached Data</text>
  <text x="425" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#0c4a6e">Materials • Resources</text>
  
  <!-- Persistent Data -->
  <rect x="500" y="650" width="130" height="30" fill="#bae6fd" stroke="#0ea5e9" stroke-width="1" rx="5"/>
  <text x="565" y="665" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#0c4a6e">Persistent Data</text>
  <text x="565" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#0c4a6e">Database • Files</text>
  
  <!-- Configuration Data -->
  <rect x="640" y="650" width="130" height="30" fill="#bae6fd" stroke="#0ea5e9" stroke-width="1" rx="5"/>
  <text x="705" y="665" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#0c4a6e">Configuration Data</text>
  <text x="705" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#0c4a6e">Settings • Preferences</text>
  
  <!-- Streaming Data -->
  <rect x="780" y="650" width="130" height="30" fill="#bae6fd" stroke="#0ea5e9" stroke-width="1" rx="5"/>
  <text x="845" y="665" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#0c4a6e">Streaming Data</text>
  <text x="845" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#0c4a6e">3D Tiles • LOD</text>
  
  <!-- State Data -->
  <rect x="920" y="650" width="100" height="30" fill="#bae6fd" stroke="#0ea5e9" stroke-width="1" rx="5"/>
  <text x="970" y="665" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#0c4a6e">State Data</text>
  <text x="970" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#0c4a6e">Session • UI State</text>
  
  <!-- Data Flow Arrows -->
  <!-- External to Import -->
  <line x1="190" y1="160" x2="190" y2="180" stroke="#dc2626" stroke-width="3" marker-end="url(#arrowhead-red)"/>
  
  <!-- Import to Core -->
  <line x1="330" y1="230" x2="370" y2="230" stroke="#0284c7" stroke-width="3" marker-end="url(#arrowhead-blue)"/>
  <line x1="370" y1="230" x2="450" y2="140" stroke="#0284c7" stroke-width="3" marker-end="url(#arrowhead-blue)"/>
  
  <!-- Input to Core -->
  <line x1="330" y1="350" x2="370" y2="350" stroke="#f59e0b" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="370" y1="350" x2="450" y2="140" stroke="#f59e0b" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- Core to UI -->
  <line x1="510" y1="140" x2="570" y2="140" stroke="#16a34a" stroke-width="3" marker-end="url(#arrowhead-green)"/>
  <line x1="570" y1="140" x2="570" y2="290" stroke="#16a34a" stroke-width="3" marker-end="url(#arrowhead-green)"/>
  
  <!-- Core to Scene -->
  <line x1="670" y1="140" x2="800" y2="140" stroke="#16a34a" stroke-width="3" marker-end="url(#arrowhead-green)"/>
  
  <!-- UI to Scene -->
  <line x1="640" y1="350" x2="800" y2="350" stroke="#8b5cf6" stroke-width="2" marker-end="url(#arrowhead-purple)"/>
  <line x1="800" y1="350" x2="975" y2="270" stroke="#8b5cf6" stroke-width="2" marker-end="url(#arrowhead-purple)"/>
  
  <!-- Scene to Storage -->
  <line x1="975" y1="450" x2="975" y2="480" stroke="#475569" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="975" y1="480" x2="600" y2="480" stroke="#475569" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Core to Storage -->
  <line x1="570" y1="260" x2="570" y2="480" stroke="#16a34a" stroke-width="2" marker-end="url(#arrowhead-green)"/>
  
  <!-- UI to Storage -->
  <line x1="450" y1="370" x2="450" y2="480" stroke="#8b5cf6" stroke-width="2" marker-end="url(#arrowhead-purple)"/>
  
  <!-- Bidirectional arrows for data synchronization -->
  <line x1="300" y1="535" x2="410" y2="535" stroke="#f59e0b" stroke-width="2" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
  <line x1="410" y1="545" x2="300" y2="545" stroke="#f59e0b" stroke-width="2" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
  
  <line x1="520" y1="535" x2="630" y2="535" stroke="#f59e0b" stroke-width="2" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
  <line x1="630" y1="545" x2="520" y2="545" stroke="#f59e0b" stroke-width="2" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
  
  <!-- Legend -->
  <rect x="50" y="720" width="1100" height="40" fill="white" stroke="#cbd5e0" stroke-width="1" rx="5"/>
  <text x="60" y="735" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#2d3748">Data Flow Legend:</text>
  
  <line x1="70" y1="750" x2="100" y2="750" stroke="#dc2626" stroke-width="3" marker-end="url(#arrowhead-red)"/>
  <text x="110" y="754" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">External Data Import</text>
  
  <line x1="220" y1="750" x2="250" y2="750" stroke="#0284c7" stroke-width="3" marker-end="url(#arrowhead-blue)"/>
  <text x="260" y="754" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">Data Processing</text>
  
  <line x1="370" y1="750" x2="400" y2="750" stroke="#16a34a" stroke-width="3" marker-end="url(#arrowhead-green)"/>
  <text x="410" y="754" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">Core Data Flow</text>
  
  <line x1="510" y1="750" x2="540" y2="750" stroke="#8b5cf6" stroke-width="2" marker-end="url(#arrowhead-purple)"/>
  <text x="550" y="754" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">UI Communication</text>
  
  <line x1="650" y1="750" x2="680" y2="750" stroke="#f59e0b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="690" y="754" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">Input Events</text>
  
  <line x1="780" y1="750" x2="810" y2="750" stroke="#475569" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="820" y="754" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">Persistence</text>
  
  <line x1="920" y1="750" x2="950" y2="750" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
  <text x="960" y="754" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">Bidirectional Sync</text>
</svg>