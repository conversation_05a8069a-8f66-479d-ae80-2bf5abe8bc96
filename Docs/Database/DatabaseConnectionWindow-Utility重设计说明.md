# DatabaseConnectionWindow 使用 Utility.uss 重设计说明

## 重设计概述

我已经使用 Utility.uss 工具类库重新设计了 DatabaseConnectionWindow.uxml，采用了现代化的设计风格，提升了用户体验和视觉效果。

## 主要改进

### 1. 引入 Utility.uss 工具类
- 使用 Tailwind CSS 风格的工具类
- 提供一致的设计系统
- 减少自定义样式的复杂性

### 2. 现代化的颜色方案
- **主背景**: `bg-slate-50` - 浅灰色背景，更加柔和
- **卡片背景**: `bg-white` - 纯白色卡片，提供清晰的层次感
- **文本颜色**: 使用 slate 色系，提供良好的对比度
  - 标题: `text-slate-900` (深色)
  - 副标题: `text-slate-600` (中等色)
  - 标签: `text-slate-700` (标准色)

### 3. 改进的布局设计
- **间距系统**: 使用统一的间距标准 (p-2, p-3, p-4, p-6, mb-1 到 mb-6)
- **圆角设计**: 添加 `rounded` 类，使界面更加现代
- **边框优化**: 使用 `border-slate-200/300` 提供微妙的边框效果

### 4. 增强的交互体验
- **状态指示器**: 使用 `bg-blue-50` 和 `border-blue-200` 创建友好的状态提示
- **按钮设计**: 
  - 主按钮: `bg-blue-500 text-white` - 突出的蓝色主按钮
  - 次按钮: `bg-slate-200 text-slate-700` - 低调的灰色次按钮

## 文件结构

### 1. 主要文件
```
Assets/Resources/UI Toolkit/
├── DatabaseConnectionWindow.uxml (重新设计的界面)
└── DatabaseConnectionWindow-Utility.uss (补充样式)
```

### 2. 依赖文件
```
Assets/
└── Utility.uss (主要工具类库)
```

## 设计细节

### 头部区域 (Header)
```xml
<ui:VisualElement name="header-container" class="mb-6 pb-4 border-b-slate-200">
    <ui:Label name="title-label" text="数据库连接配置" class="text-slate-900 text-lg font-bold mb-1" />
    <ui:Label name="subtitle-label" text="配置并测试数据库连接参数" class="text-slate-600 text-sm" />
</ui:VisualElement>
```

**特点**:
- 使用底部边框分隔 (`border-b-slate-200`)
- 标题使用大字体和粗体 (`text-lg font-bold`)
- 副标题使用较小字体和中等颜色 (`text-sm text-slate-600`)

### 表单区域 (Form)
```xml
<ui:VisualElement name="connection-info-group" class="bg-white border-slate-200 rounded p-4 mb-5">
    <ui:Label name="connection-info-title" text="连接信息" class="text-slate-800 text-sm font-bold mb-3" />
    <!-- 表单字段 -->
</ui:VisualElement>
```

**特点**:
- 白色卡片背景 (`bg-white`)
- 圆角设计 (`rounded`)
- 统一的内边距 (`p-4`)
- 清晰的分组标题

### 输入字段
```xml
<ui:TextField name="server-field" class="bg-white border-slate-300 rounded text-slate-900 p-2" value="localhost" />
```

**特点**:
- 白色背景 (`bg-white`)
- 灰色边框 (`border-slate-300`)
- 圆角设计 (`rounded`)
- 适当的内边距 (`p-2`)

### 状态指示器
```xml
<ui:VisualElement name="status-container" class="flex-row items-center bg-blue-50 border-blue-200 rounded p-3 mb-4">
    <ui:VisualElement name="status-icon" class="w-4 h-4 bg-slate-400 rounded-full mr-2" />
    <ui:Label name="status-label" text="请配置数据库连接参数后点击测试连接" class="text-slate-700 text-xs flex-grow" />
</ui:VisualElement>
```

**特点**:
- 蓝色背景提示 (`bg-blue-50`)
- 圆形状态图标 (`rounded-full`)
- 灵活布局 (`flex-row items-center`)

### 按钮组
```xml
<ui:Button name="test-button" text="测试连接" class="bg-blue-500 text-white border-blue-500 rounded px-5 py-2 text-sm font-medium mr-2" />
<ui:Button name="cancel-button" text="取消" class="bg-slate-200 text-slate-700 border-slate-300 rounded px-5 py-2 text-sm font-medium" />
```

**特点**:
- 主按钮使用蓝色主题 (`bg-blue-500`)
- 次按钮使用灰色主题 (`bg-slate-200`)
- 统一的内边距和字体大小

## 补充样式说明

### DatabaseConnectionWindow-Utility.uss
这个文件补充了 Utility.uss 中可能缺少的样式类：

1. **尺寸类**: `min-w-540`, `min-h-480`, `w-4`, `h-4`
2. **间距类**: `p-2` 到 `p-6`, `mb-1` 到 `mb-6`, `px-5`, `py-2`
3. **布局类**: `flex-row`, `flex-col`, `flex-grow`, `justify-between`
4. **字体类**: `text-lg`, `text-sm`, `text-xs`, `font-bold`, `font-medium`
5. **颜色类**: 各种 slate 和 blue 色系的背景色和文本色
6. **交互增强**: 悬停和激活状态的样式

## 使用方法

### 1. 确保文件就位
确保以下文件在正确位置：
- `Assets/Utility.uss`
- `Assets/Resources/UI Toolkit/DatabaseConnectionWindow.uxml`
- `Assets/Resources/UI Toolkit/DatabaseConnectionWindow-Utility.uss`

### 2. 在代码中使用
```csharp
// 加载 UXML
var visualTree = Resources.Load<VisualTreeAsset>("UI Toolkit/DatabaseConnectionWindow");
var root = visualTree.Instantiate();

// 样式会自动应用
```

### 3. 状态管理
可以通过添加/移除 CSS 类来管理不同状态：

```csharp
// 成功状态
statusContainer.AddToClassList("status-success");
statusIcon.AddToClassList("status-success");

// 错误状态
statusContainer.AddToClassList("status-error");
statusIcon.AddToClassList("status-error");

// 显示/隐藏加载指示器
loadingIndicator.RemoveFromClassList("hidden");
```

## 优势总结

1. **一致性**: 使用统一的设计系统
2. **可维护性**: 减少自定义样式，使用标准化的工具类
3. **现代化**: 采用现代 UI 设计趋势
4. **可扩展性**: 易于添加新的状态和变体
5. **响应式**: 为未来的响应式设计做好准备

## 注意事项

1. **Unity USS 限制**: 某些 CSS 特性在 Unity USS 中不支持，如动画、阴影等
2. **颜色变量**: 确保 Utility.uss 中定义了所需的颜色变量
3. **字体支持**: Unity UI Toolkit 的字体支持有限，某些字体样式可能需要调整
4. **性能考虑**: 大量的工具类可能会影响性能，但通常影响很小

这个重设计版本提供了更好的用户体验，同时保持了功能的完整性和代码的可维护性。
