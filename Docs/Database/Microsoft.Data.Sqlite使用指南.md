# Microsoft.Data.Sqlite 使用指南

## 概述

本指南介绍如何在 Unity 项目中使用 Microsoft.Data.Sqlite 进行 SQLite 数据库操作。Microsoft.Data.Sqlite 是微软官方提供的现代化 SQLite 数据提供程序，支持 .NET Standard 2.0，提供了更好的性能和功能。

## 主要优势

### 相比传统方案的优势
- **官方支持**: 微软官方维护，长期支持保证
- **现代化 API**: 支持异步操作，更好的性能
- **标准兼容**: 遵循 .NET Standard 2.0，跨平台兼容
- **简化部署**: 通过 NuGet 管理，无需手动管理 DLL 文件
- **更好的错误处理**: 提供详细的异常信息和调试支持

### 与 Mono.Data.Sqlite 对比
| 特性 | Microsoft.Data.Sqlite | Mono.Data.Sqlite |
|------|----------------------|-------------------|
| 维护状态 | 活跃维护 | 逐渐淘汰 |
| 异步支持 | ✓ 完整支持 | ✗ 有限支持 |
| 性能 | 优化更好 | 较老实现 |
| 部署复杂度 | NuGet 管理 | 手动 DLL |
| Unity 兼容性 | 需要配置 | 内置但有限 |

## 安装和配置

### 1. 安装 Microsoft.Data.Sqlite

#### 方法一：使用 NuGet for Unity（推荐）
1. 安装 NuGet for Unity 插件
2. 在 NuGet 窗口中搜索 "Microsoft.Data.Sqlite"
3. 点击安装

#### 方法二：手动添加包引用
1. 在项目根目录创建或编辑 `packages.config` 文件：
```xml
<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.Data.Sqlite" version="7.0.0" targetFramework="netstandard2.0" />
</packages>
```

#### 方法三：使用 Unity Package Manager
1. 打开 Window > Package Manager
2. 点击 + 号，选择 "Add package from git URL"
3. 输入相应的 git URL（如果可用）

### 2. 配置 Unity 项目

#### 更新 Player Settings
1. 打开 Edit > Project Settings > Player
2. 在 Configuration 部分：
   - Api Compatibility Level: .NET Standard 2.1
   - Scripting Backend: Mono（推荐）或 IL2CPP

#### 处理平台兼容性
对于不同平台，可能需要额外配置：

**Windows/Mac/Linux:**
- 通常开箱即用

**Android:**
- 确保 Target API Level 兼容
- 可能需要添加 SQLite 原生库

**iOS:**
- 使用 IL2CPP 时需要额外配置
- 可能需要链接设置调整

## 使用示例

### 基本连接示例

```csharp
using Microsoft.Data.Sqlite;
using System.Threading.Tasks;

public async Task ConnectToDatabase()
{
    string connectionString = "Data Source=MyDatabase.db";
    
    using var connection = new SqliteConnection(connectionString);
    await connection.OpenAsync();
    
    Console.WriteLine("连接成功！");
}
```

### 创建表和插入数据

```csharp
public async Task CreateTableAndInsertData()
{
    string connectionString = "Data Source=MyDatabase.db";
    
    using var connection = new SqliteConnection(connectionString);
    await connection.OpenAsync();
    
    // 创建表
    string createTableSql = @"
        CREATE TABLE IF NOT EXISTS Users (
            Id INTEGER PRIMARY KEY AUTOINCREMENT,
            Name TEXT NOT NULL,
            Email TEXT UNIQUE,
            CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
    
    using var createCommand = new SqliteCommand(createTableSql, connection);
    await createCommand.ExecuteNonQueryAsync();
    
    // 插入数据
    string insertSql = "INSERT INTO Users (Name, Email) VALUES (@name, @email)";
    using var insertCommand = new SqliteCommand(insertSql, connection);
    
    insertCommand.Parameters.AddWithValue("@name", "张三");
    insertCommand.Parameters.AddWithValue("@email", "<EMAIL>");
    
    int rowsAffected = await insertCommand.ExecuteNonQueryAsync();
    Console.WriteLine($"插入了 {rowsAffected} 行数据");
}
```

### 查询数据

```csharp
public async Task QueryData()
{
    string connectionString = "Data Source=MyDatabase.db";
    
    using var connection = new SqliteConnection(connectionString);
    await connection.OpenAsync();
    
    string selectSql = "SELECT Id, Name, Email, CreatedAt FROM Users";
    using var command = new SqliteCommand(selectSql, connection);
    using var reader = await command.ExecuteReaderAsync();
    
    while (await reader.ReadAsync())
    {
        int id = reader.GetInt32("Id");
        string name = reader.GetString("Name");
        string email = reader.GetString("Email");
        DateTime createdAt = reader.GetDateTime("CreatedAt");
        
        Console.WriteLine($"ID: {id}, Name: {name}, Email: {email}, Created: {createdAt}");
    }
}
```

## 连接字符串配置

### 基本格式
```csharp
// 文件数据库
"Data Source=MyDatabase.db"

// 内存数据库
"Data Source=:memory:"

// 完整路径
"Data Source=/path/to/database.db"
```

### 高级选项
```csharp
// 启用外键约束
"Data Source=MyDatabase.db;Foreign Keys=True"

// 设置缓存模式
"Data Source=MyDatabase.db;Cache=Shared"

// 设置访问模式
"Data Source=MyDatabase.db;Mode=ReadWriteCreate"

// 组合配置
"Data Source=MyDatabase.db;Mode=ReadWriteCreate;Cache=Shared;Foreign Keys=True"
```

### Unity 特定路径
```csharp
// 使用 Unity 持久化数据路径
string dbPath = Path.Combine(Application.persistentDataPath, "game.db");
string connectionString = $"Data Source={dbPath}";

// 使用 StreamingAssets（只读）
string dbPath = Path.Combine(Application.streamingAssetsPath, "readonly.db");
string connectionString = $"Data Source={dbPath};Mode=ReadOnly";
```

## 最佳实践

### 1. 连接管理
```csharp
// 使用 using 语句确保资源释放
using var connection = new SqliteConnection(connectionString);

// 异步操作优先
await connection.OpenAsync();
```

### 2. 参数化查询
```csharp
// 正确：使用参数化查询
string sql = "SELECT * FROM Users WHERE Name = @name";
command.Parameters.AddWithValue("@name", userName);

// 错误：字符串拼接（SQL 注入风险）
string sql = $"SELECT * FROM Users WHERE Name = '{userName}'";
```

### 3. 事务处理
```csharp
using var transaction = connection.BeginTransaction();
try
{
    // 执行多个操作
    await command1.ExecuteNonQueryAsync();
    await command2.ExecuteNonQueryAsync();
    
    await transaction.CommitAsync();
}
catch
{
    await transaction.RollbackAsync();
    throw;
}
```

### 4. 错误处理
```csharp
try
{
    await connection.OpenAsync();
}
catch (SqliteException ex)
{
    // 处理 SQLite 特定错误
    Debug.LogError($"SQLite 错误: {ex.Message}");
}
catch (Exception ex)
{
    // 处理一般错误
    Debug.LogError($"数据库错误: {ex.Message}");
}
```

## 性能优化

### 1. 批量操作
```csharp
using var transaction = connection.BeginTransaction();
using var command = new SqliteCommand("INSERT INTO Users (Name) VALUES (@name)", connection);

for (int i = 0; i < 1000; i++)
{
    command.Parameters.Clear();
    command.Parameters.AddWithValue("@name", $"User{i}");
    await command.ExecuteNonQueryAsync();
}

await transaction.CommitAsync();
```

### 2. 预编译语句
```csharp
using var command = new SqliteCommand("INSERT INTO Users (Name) VALUES (@name)", connection);
await command.PrepareAsync();

// 重复使用预编译的命令
for (int i = 0; i < 1000; i++)
{
    command.Parameters.Clear();
    command.Parameters.AddWithValue("@name", $"User{i}");
    await command.ExecuteNonQueryAsync();
}
```

### 3. 连接池配置
```csharp
// Microsoft.Data.Sqlite 自动管理连接池
// 通常不需要手动配置
```

## 故障排除

### 常见问题

1. **编译错误：找不到 Microsoft.Data.Sqlite**
   - 确保正确安装了 NuGet 包
   - 检查 .NET 兼容性设置
   - 重新导入项目

2. **运行时错误：DllNotFoundException**
   - 检查目标平台支持
   - 确保 SQLite 原生库可用
   - 尝试不同的 Scripting Backend

3. **数据库文件访问错误**
   - 检查文件路径权限
   - 确保目录存在
   - 使用 Application.persistentDataPath

4. **性能问题**
   - 使用事务进行批量操作
   - 添加适当的索引
   - 考虑使用 WAL 模式

### 调试技巧

1. **启用详细日志**
```csharp
// 在连接字符串中添加日志选项（如果支持）
"Data Source=MyDatabase.db;LogLevel=Debug"
```

2. **检查 SQLite 版本**
```csharp
using var command = new SqliteCommand("SELECT sqlite_version()", connection);
string version = (string)await command.ExecuteScalarAsync();
Debug.Log($"SQLite 版本: {version}");
```

3. **监控连接状态**
```csharp
Debug.Log($"连接状态: {connection.State}");
Debug.Log($"数据库文件: {connection.DataSource}");
```

## 项目集成

### 在现有项目中集成

1. **替换现有 SQLite 实现**
   - 逐步迁移现有代码
   - 保持数据库架构兼容
   - 测试所有功能

2. **与 Unity 生命周期集成**
   - 在 Awake/Start 中初始化连接
   - 在 OnDestroy 中清理资源
   - 考虑使用单例模式

3. **多线程考虑**
   - SQLite 支持多线程读取
   - 写操作需要同步
   - 考虑使用队列模式

## 总结

Microsoft.Data.Sqlite 为 Unity 项目提供了现代化、高性能的 SQLite 数据库解决方案。通过正确的配置和使用，可以显著提升数据库操作的性能和可维护性。

关键要点：
- 使用 NuGet 管理依赖
- 优先使用异步 API
- 正确处理资源释放
- 遵循最佳实践进行性能优化
- 充分测试不同平台的兼容性
