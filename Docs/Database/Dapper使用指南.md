# Dapper ORM 在 Unity 中的使用指南

## 概述

Dapper 是一个轻量级、高性能的 .NET ORM（对象关系映射）框架，被称为"微型 ORM"。它在保持接近原生 SQL 性能的同时，提供了对象映射的便利性。

## 主要优势

### 与传统 ADO.NET 对比
| 特性 | ADO.NET | Dapper |
|------|---------|--------|
| 性能 | 最快 | 接近原生 |
| 易用性 | 复杂 | 简单 |
| 对象映射 | 手动 | 自动 |
| SQL 控制 | 完全控制 | 完全控制 |
| 学习曲线 | 陡峭 | 平缓 |

### 与 Entity Framework 对比
| 特性 | Entity Framework | Dapper |
|------|------------------|--------|
| 性能 | 较慢 | 快速 |
| 功能 | 丰富 | 精简 |
| 复杂度 | 高 | 低 |
| 包大小 | 大 | 小 |
| Unity 兼容性 | 有限 | 良好 |

## 安装和配置

### 1. 安装 Dapper NuGet 包

#### 使用 NuGet for Unity
```
Dapper
```

#### 手动添加到 packages.config
```xml
<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.Data.Sqlite" version="7.0.0" targetFramework="netstandard2.0" />
  <package id="SQLitePCLRaw.bundle_e_sqlite3" version="2.1.4" targetFramework="netstandard2.0" />
  <package id="Dapper" version="2.0.123" targetFramework="netstandard2.0" />
</packages>
```

### 2. 基本配置

确保 Unity 项目设置：
- **Api Compatibility Level**: .NET Standard 2.1
- **Scripting Backend**: Mono 或 IL2CPP

## 基本用法

### 1. 连接数据库

```csharp
using Microsoft.Data.Sqlite;
using Dapper;

string connectionString = "Data Source=MyDatabase.db";
using var connection = new SqliteConnection(connectionString);
connection.Open();
```

### 2. 定义数据模型

```csharp
public class User
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Email { get; set; }
    public int Age { get; set; }
    public DateTime CreatedAt { get; set; }
}
```

### 3. 基本 CRUD 操作

#### 查询（Query）
```csharp
// 查询所有用户
var users = connection.Query<User>("SELECT * FROM Users");

// 参数化查询
var user = connection.QuerySingle<User>(
    "SELECT * FROM Users WHERE Id = @Id", 
    new { Id = 1 });

// 查询标量值
var count = connection.QuerySingle<int>("SELECT COUNT(*) FROM Users");
```

#### 插入（Insert）
```csharp
// 单个插入
var sql = "INSERT INTO Users (Name, Email, Age) VALUES (@Name, @Email, @Age)";
var rowsAffected = connection.Execute(sql, new { 
    Name = "张三", 
    Email = "<EMAIL>", 
    Age = 25 
});

// 批量插入
var users = new[]
{
    new { Name = "李四", Email = "<EMAIL>", Age = 30 },
    new { Name = "王五", Email = "<EMAIL>", Age = 28 }
};
var insertCount = connection.Execute(sql, users);
```

#### 更新（Update）
```csharp
var updateSql = "UPDATE Users SET Age = @Age WHERE Id = @Id";
var updated = connection.Execute(updateSql, new { Age = 26, Id = 1 });
```

#### 删除（Delete）
```csharp
var deleteSql = "DELETE FROM Users WHERE Id = @Id";
var deleted = connection.Execute(deleteSql, new { Id = 1 });
```

## 高级功能

### 1. 异步操作

```csharp
// 异步查询
var users = await connection.QueryAsync<User>("SELECT * FROM Users");

// 异步执行
var rowsAffected = await connection.ExecuteAsync(
    "INSERT INTO Users (Name, Email, Age) VALUES (@Name, @Email, @Age)",
    new { Name = "异步用户", Email = "<EMAIL>", Age = 30 });
```

### 2. 事务处理

```csharp
using var transaction = connection.BeginTransaction();
try
{
    // 执行多个操作
    await connection.ExecuteAsync(sql1, parameters1, transaction);
    await connection.ExecuteAsync(sql2, parameters2, transaction);
    
    // 提交事务
    await transaction.CommitAsync();
}
catch
{
    // 回滚事务
    await transaction.RollbackAsync();
    throw;
}
```

### 3. 动态参数

```csharp
var parameters = new DynamicParameters();
parameters.Add("@Name", "张三");
parameters.Add("@Age", 25);
parameters.Add("@UserId", dbType: DbType.Int32, direction: ParameterDirection.Output);

connection.Execute("INSERT INTO Users (Name, Age) VALUES (@Name, @Age); SELECT @UserId = SCOPE_IDENTITY()", parameters);
var newUserId = parameters.Get<int>("@UserId");
```

### 4. 多结果集查询

```csharp
var sql = @"
    SELECT * FROM Users WHERE Age > @MinAge;
    SELECT * FROM Posts WHERE UserId IN (SELECT Id FROM Users WHERE Age > @MinAge);";

using var multi = connection.QueryMultiple(sql, new { MinAge = 25 });
var users = multi.Read<User>();
var posts = multi.Read<Post>();
```

### 5. 复杂对象映射

```csharp
// 一对多映射
var userPosts = connection.Query<User, Post, User>(
    @"SELECT u.*, p.* FROM Users u 
      LEFT JOIN Posts p ON u.Id = p.UserId",
    (user, post) => {
        user.Posts = user.Posts ?? new List<Post>();
        if (post != null)
            user.Posts.Add(post);
        return user;
    },
    splitOn: "Id");
```

## 性能优化

### 1. 预编译查询

```csharp
// 对于重复执行的查询，可以预编译
var sql = "SELECT * FROM Users WHERE Age > @Age";
var users = connection.Query<User>(sql, new { Age = 25 });
```

### 2. 批量操作

```csharp
// 使用事务进行批量操作
using var transaction = connection.BeginTransaction();
var sql = "INSERT INTO Users (Name, Email, Age) VALUES (@Name, @Email, @Age)";

foreach (var batch in users.Batch(1000)) // 分批处理
{
    connection.Execute(sql, batch, transaction);
}
transaction.Commit();
```

### 3. 连接管理

```csharp
// 使用连接池（Microsoft.Data.Sqlite 自动管理）
// 避免频繁开关连接
using var connection = new SqliteConnection(connectionString);
connection.Open();

// 执行多个操作...
```

## 最佳实践

### 1. 参数化查询

```csharp
// 正确：使用参数化查询
var users = connection.Query<User>(
    "SELECT * FROM Users WHERE Name = @Name", 
    new { Name = userName });

// 错误：字符串拼接（SQL 注入风险）
var users = connection.Query<User>(
    $"SELECT * FROM Users WHERE Name = '{userName}'");
```

### 2. 异常处理

```csharp
try
{
    var users = await connection.QueryAsync<User>("SELECT * FROM Users");
    return users;
}
catch (SqliteException ex)
{
    // 处理 SQLite 特定错误
    Debug.LogError($"数据库错误: {ex.Message}");
    throw;
}
catch (Exception ex)
{
    // 处理一般错误
    Debug.LogError($"查询失败: {ex.Message}");
    throw;
}
```

### 3. 资源管理

```csharp
// 使用 using 语句确保资源释放
using var connection = new SqliteConnection(connectionString);
using var transaction = connection.BeginTransaction();

// 或者在 Unity 中使用 try-finally
SqliteConnection connection = null;
try
{
    connection = new SqliteConnection(connectionString);
    connection.Open();
    // 执行操作...
}
finally
{
    connection?.Close();
}
```

### 4. 数据模型设计

```csharp
// 使用属性而不是字段
public class User
{
    public int Id { get; set; }           // 主键
    public string Name { get; set; }      // 必填字段
    public string Email { get; set; }     // 唯一字段
    public int? Age { get; set; }         // 可空字段
    public DateTime CreatedAt { get; set; } = DateTime.Now; // 默认值
}
```

## Unity 特定注意事项

### 1. 主线程操作

```csharp
// 在 Unity 主线程中更新 UI
private async void LoadUsersAsync()
{
    var users = await GetUsersFromDatabaseAsync();
    
    // 确保在主线程中更新 UI
    UnityMainThreadDispatcher.Instance().Enqueue(() => {
        UpdateUserList(users);
    });
}
```

### 2. 生命周期管理

```csharp
public class DatabaseManager : MonoBehaviour
{
    private SqliteConnection connection;
    
    void Start()
    {
        InitializeDatabase();
    }
    
    void OnDestroy()
    {
        // 清理资源
        connection?.Close();
        connection?.Dispose();
    }
}
```

### 3. 配置管理

```csharp
// 使用 ScriptableObject 管理数据库配置
[CreateAssetMenu(fileName = "DatabaseConfig", menuName = "Database/Config")]
public class DatabaseConfig : ScriptableObject
{
    public string databaseName = "GameData.db";
    public bool useMemoryDatabase = false;
    public int connectionTimeout = 30;
}
```

## 常见问题解决

### 1. 性能问题

**问题**: 查询速度慢
**解决方案**:
- 添加适当的索引
- 使用参数化查询
- 避免 N+1 查询问题
- 使用批量操作

### 2. 内存问题

**问题**: 内存占用过高
**解决方案**:
- 使用 `QueryFirst` 而不是 `Query` 获取单个结果
- 及时释放大型结果集
- 使用流式查询处理大量数据

### 3. 并发问题

**问题**: 多线程访问冲突
**解决方案**:
- SQLite 支持多读单写
- 使用连接池
- 适当的事务隔离级别

## 总结

Dapper 为 Unity 项目提供了一个完美的数据库访问解决方案，它结合了：

- **高性能**: 接近原生 ADO.NET 的性能
- **简单易用**: 最小化的 API 设计
- **灵活性**: 完全控制 SQL 查询
- **可靠性**: 成熟稳定的框架

通过合理使用 Dapper，您可以在 Unity 项目中实现高效、可维护的数据库操作。
