# src/request/ 请求参数定义模块技术规格文档

## 模块概述

请求参数定义模块是系统的数据传输层组件，负责定义各种业务操作的输入参数结构。该模块采用数据类(dataclass)模式，提供类型安全的参数定义，支持数据验证、序列化和API接口调用，确保业务服务间的数据传输规范性和一致性。

## 文件清单及功能

### 核心请求类文件

| 文件名 | 功能描述 | 业务场景 | 主要用途 |
|--------|----------|----------|----------|
| `measure_point_request.py` | 测量点请求参数 | 测量点数据传输 | 测量点创建、更新、查询 |
| `punch_request.py` | 钻孔布置请求参数 | 自动布孔算法调用 | 钻孔生成、布置优化 |

## 核心数据结构

### 1. 测量点请求 (MeasurePointRequest)

```python
@dataclass
class MeasurePointRequest:
    """测量点请求参数类"""
    
    # 基础标识
    project_id: str          # 项目ID
    id: str                  # 测量点ID
    blast_area_id: str       # 所属爆区ID
    
    # 地理坐标
    lng: float               # 经度 (WGS84坐标系)
    lat: float               # 纬度 (WGS84坐标系)
    alt: float               # 海拔高度 (米)
    
    # 序列化方法
    def to_dict(self) -> Dict[str, Any]
        """将对象转换为字典，用于JSON序列化"""
```

**字段说明**:
- `project_id`: 项目唯一标识符，关联具体项目
- `id`: 测量点唯一标识符，用于数据库关联
- `blast_area_id`: 爆破区域标识符，确定测量点所属区域
- `lng/lat/alt`: 地理坐标信息，用于3D场景定位和计算

### 2. 钻孔布置请求 (PunchRequest)

```python
@dataclass
class PunchRequest:
    """自动布孔接口请求参数类"""
    
    # 基础标识
    ProjectID: str                           # 项目ID
    BlastID: str                            # 爆破区ID
    MeasurePoints: List[MeasurePointRequest] # 测量点列表
    
    # 布孔基本参数
    Spacing: Union[int, float, str]         # 孔距 (米)
    Burden: Union[int, float, str]          # 排距 (米)
    NearFarPoint: str                       # 近远点坐标字符串
    LineType: str                           # 边界线类型定义
    
    # 钻孔物理参数
    HoleDiameter: float                     # 孔径 (米)
    HoleDepth: float                        # 孔深 (米)
    StepElevation: float                    # 台阶高度 (米)
    BottomElevation: float                  # 孔底海拔 (米)
    Over_Depth: float                       # 超深 (米)
    Angle: float                            # 钻孔角度 (度)
    HoleLength: float                       # 孔长 (米)
    
    # 布孔模式参数
    hole_pattern: str                       # 布孔模式
    hole_gradient_change: bool              # 孔距梯度变化
    depth_gradient_change: bool             # 孔深梯度变化
    
    # 高级布孔参数
    allow_adjust_row: float                 # 允许调整排距 (米)
    rear_border_line_distance: float       # 后边界线距离 (米)
    row_min_distance: float                 # 排距最小值 (米)
```

**关键字段详解**:

#### 基础标识字段
- `ProjectID`: 项目唯一标识，确保布孔操作在正确项目范围内
- `BlastID`: 爆破区域标识，指定具体的爆破作业区域
- `MeasurePoints`: 测量点集合，定义爆破区域的边界和参考点

#### 布孔几何参数
- `Spacing`: 孔距，相邻钻孔间的水平距离
- `Burden`: 排距，相邻排钻孔间的距离
- `NearFarPoint`: 近远点坐标，格式为"近点经度,近点纬度,远点经度,远点纬度"
- `LineType`: 边界线类型，格式为"类型,测量点ID列表"

#### 钻孔工程参数
- `HoleDiameter`: 钻孔直径，影响爆破效果和成本
- `HoleDepth`: 钻孔深度，从地表到孔底的垂直距离
- `StepElevation`: 台阶标高，爆破台阶的顶部高程
- `BottomElevation`: 孔底海拔，钻孔底部的绝对高程
- `Over_Depth`: 超深，超出设计深度的额外钻进距离
- `Angle`: 钻孔倾斜角度，0度为垂直，90度为水平
- `HoleLength`: 钻孔总长度，考虑角度后的实际钻进长度

#### 布孔模式参数
- `hole_pattern`: 布孔模式，支持"TRIANGULAR_HOLE"(三角孔)和"SQUARE_HOLE"(方形孔)
- `hole_gradient_change`: 孔距梯度变化，是否允许孔距动态调整
- `depth_gradient_change`: 孔深梯度变化，是否允许孔深动态调整

#### 优化约束参数
- `allow_adjust_row`: 允许调整排距的范围，用于布孔优化
- `rear_border_line_distance`: 距离后边界线的最小距离，安全约束
- `row_min_distance`: 排间最小距离，工程约束

## 接口规范

### MeasurePointRequest接口

#### 序列化接口
```python
def to_dict(self) -> Dict[str, Any]
    """
    将测量点请求对象转换为字典
    
    返回:
        包含所有字段的字典，用于JSON序列化
        
    用途:
        - API接口数据传输
        - 数据库存储准备
        - 日志记录和调试
    """
```

#### 验证接口
```python
def validate(self) -> Tuple[bool, str]
    """
    验证测量点请求参数的有效性
    
    返回:
        (是否有效, 错误信息)
        
    验证规则:
        - 坐标范围检查
        - ID格式验证
        - 必填字段检查
    """
```

### PunchRequest接口

#### 参数转换接口
```python
def normalize_numeric_fields(self) -> None
    """
    标准化数值字段类型
    
    功能:
        - 将Union类型字段转换为float
        - 处理字符串数值转换
        - 设置默认值
    """
```

#### 参数验证接口
```python
def validate_parameters(self) -> Tuple[bool, List[str]]
    """
    验证布孔请求参数
    
    返回:
        (是否有效, 错误信息列表)
        
    验证内容:
        - 数值范围检查
        - 几何约束验证
        - 工程参数合理性
        - 测量点数量和分布
    """
```

#### 坐标解析接口
```python
def parse_near_far_point(self) -> Tuple[float, float, float, float]
    """
    解析近远点坐标字符串
    
    返回:
        (近点经度, 近点纬度, 远点经度, 远点纬度)
        
    异常:
        ValueError: 坐标格式错误
    """

def parse_line_type(self) -> Tuple[int, List[str]]
    """
    解析边界线类型字符串
    
    返回:
        (线类型, 测量点ID列表)
        
    格式:
        "1,id1,id2" - 爆区边界线
        "2,id1,id2,id3" - 头排孔线
    """
```

## 业务规则

### 测量点请求规则
- **坐标有效性**: 经纬度必须在有效地理范围内
- **高程合理性**: 海拔高度应在合理范围内
- **ID唯一性**: 测量点ID在项目范围内必须唯一
- **关联完整性**: 项目ID和爆区ID必须存在且有效

### 钻孔布置请求规则
- **几何约束**: 孔距、排距必须大于0
- **工程约束**: 钻孔参数必须符合工程标准
- **安全约束**: 边界距离必须满足安全要求
- **优化约束**: 调整范围必须在合理区间内

### 参数类型规则
- **数值类型**: 支持int、float、str的自动转换
- **坐标格式**: 统一使用WGS84坐标系
- **字符串格式**: 特定格式的字符串参数有严格规范
- **列表类型**: 测量点列表不能为空

### 数据传输规则
- **序列化标准**: 使用JSON格式进行数据传输
- **编码规范**: 统一使用UTF-8编码
- **大小限制**: 单次请求的数据量有合理限制
- **超时处理**: 长时间计算的请求需要超时保护

## 关键约束条件

### 数值范围约束
```python
# 坐标范围约束
LONGITUDE_RANGE = (-180.0, 180.0)
LATITUDE_RANGE = (-90.0, 90.0)
ALTITUDE_RANGE = (-1000.0, 10000.0)

# 钻孔参数约束
HOLE_DIAMETER_RANGE = (0.05, 2.0)      # 孔径: 5cm - 2m
HOLE_DEPTH_RANGE = (0.1, 100.0)        # 孔深: 10cm - 100m
SPACING_RANGE = (0.5, 50.0)            # 孔距: 0.5m - 50m
BURDEN_RANGE = (0.5, 50.0)             # 排距: 0.5m - 50m
ANGLE_RANGE = (0.0, 90.0)              # 角度: 0° - 90°
```

### 业务逻辑约束
- 孔深不能超过台阶高度的3倍
- 孔距和排距的比值应在合理范围内
- 超深不能超过孔深的20%
- 钻孔角度应考虑地质条件

### 数据完整性约束
- 必填字段不能为空或None
- 外键关联必须有效
- 数值字段不能为负数（除特殊情况）
- 字符串字段有长度限制

### 性能约束
- 测量点数量不超过1000个
- 单次布孔请求处理时间不超过30秒
- 请求数据大小不超过10MB
- 并发请求数量有限制

## 扩展点设计

### 新请求类型扩展
```python
@dataclass
class NewRequest:
    """新请求类型"""
    # 继承通用字段
    project_id: str
    # 添加特有字段
    specific_param: float
    
    def validate(self) -> Tuple[bool, str]:
        """参数验证"""
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """序列化"""
        return asdict(self)
```

### 参数验证扩展
```python
class RequestValidator:
    """请求参数验证器"""
    
    @staticmethod
    def validate_coordinates(lng: float, lat: float, alt: float) -> bool
        """坐标验证"""
        pass
    
    @staticmethod
    def validate_engineering_params(**params) -> List[str]
        """工程参数验证"""
        pass
```

### 序列化扩展
```python
class RequestSerializer:
    """请求序列化器"""
    
    @staticmethod
    def to_json(request: Any) -> str
        """转换为JSON字符串"""
        pass
    
    @staticmethod
    def from_json(json_str: str, request_type: Type) -> Any
        """从JSON字符串创建请求对象"""
        pass
```

## 使用模式

### 基本使用模式
```python
# 创建测量点请求
measure_point = MeasurePointRequest(
    project_id="P001",
    id="MP001", 
    lng=116.404,
    lat=39.915,
    alt=50.0,
    blast_area_id="BA001"
)

# 验证和序列化
is_valid, error = measure_point.validate()
if is_valid:
    data = measure_point.to_dict()
```

### 钻孔布置请求模式
```python
# 创建钻孔布置请求
punch_request = PunchRequest(
    ProjectID="P001",
    BlastID="BA001",
    MeasurePoints=[measure_point],
    Spacing=3.0,
    Burden=2.5,
    NearFarPoint="116.404,39.915,116.405,39.916",
    LineType="1,MP001,MP002",
    HoleDiameter=0.15,
    HoleDepth=12.0,
    # ... 其他参数
)

# 参数验证和处理
punch_request.normalize_numeric_fields()
is_valid, errors = punch_request.validate_parameters()
```

### 批量处理模式
```python
# 批量创建测量点请求
measure_points = [
    MeasurePointRequest(project_id, f"MP{i}", lng, lat, alt, blast_area_id)
    for i, (lng, lat, alt) in enumerate(coordinates)
]

# 批量验证
valid_points = [
    point for point in measure_points 
    if point.validate()[0]
]
```

## 质量保证

### 类型安全
- 使用dataclass提供类型提示
- 支持Union类型的灵活参数
- 运行时类型检查和转换

### 数据验证
- 完整的参数验证机制
- 业务规则检查
- 错误信息详细明确

### 序列化支持
- 标准JSON序列化
- 自定义序列化方法
- 反序列化支持

### 可维护性
- 清晰的字段命名
- 完整的文档注释
- 统一的代码风格
