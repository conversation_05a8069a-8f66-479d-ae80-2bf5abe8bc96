# src/entity/ 业务实体类模块技术规格文档

## 模块概述

业务实体类模块是系统的领域模型核心组件，定义了爆破设计业务中的所有核心实体对象。该模块采用数据类(dataclass)模式，提供类型安全的业务对象定义，支持3D场景渲染、数据持久化和业务逻辑处理。

## 文件清单及功能

### 核心实体文件

| 文件名 | 功能描述 | 业务领域 |
|--------|----------|----------|
| `base_entity.py` | 基础实体类定义 | 通用基础设施 |
| `blast_area_entity.py` | 爆破区域实体 | 爆破区域管理 |
| `border_line_entity.py` | 边界线实体 | 区域边界定义 |
| `hole_info_entity.py` | 钻孔信息实体 | 钻孔数据管理 |
| `measure_point_entity.py` | 测量点实体 | 测量数据管理 |
| `layer_entity.py` | 图层实体 | 图层管理 |

### 辅助组件文件

| 文件名 | 功能描述 | 用途 |
|--------|----------|------|
| `blast_element_group_entity.py` | 爆破元素组实体 | 元素分组管理 |
| `blast_element_type.py` | 爆破元素类型枚举 | 元素类型定义 |
| `data_cache.py` | 数据缓存管理 | 内存数据缓存 |
| `field_names.py` | 字段名称常量 | 字段标准化定义 |
| `layer_type.py` | 图层类型枚举 | 图层分类定义 |

## 核心数据结构

### 1. 基础实体类 (BaseEntity)

```python
@dataclass
class BaseEntity:
    """基础实体类，所有业务实体的父类"""
    
    # 基础标识字段
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    code: str = ""
    
    # 时间戳字段
    create_time: datetime = field(default_factory=datetime.now)
    update_time: datetime = field(default_factory=datetime.now)
    
    # 状态字段
    remarks: str = ""
    is_deleted: bool = False
    is_changed: bool = False
    is_added: bool = False
    version: int = 1
    
    # 3D场景字段
    x: float = 0.0
    y: float = 0.0
    z: float = 0.0
    is_visible: bool = True
    is_locked: bool = False
    style: str = ""
    
    # 业务分类字段
    layerType: str = LayerType.BLAST_AREA.name
    blast_element_type: str = None
```

### 2. 爆破区域实体 (BlastAreaEntity)

```python
@dataclass
class BlastAreaEntity(BaseEntity):
    """爆破区域实体类"""
    
    # 项目关联
    project_id: str = ""
    
    # 钻孔参数
    aperture: float = 0.0              # 孔径(mm)
    hole_depth: float = 0.0            # 孔深(m)
    step_elevation_z: float = 0.0      # 台阶高度(m)
    hole_bottom_z: float = 0.0         # 孔底海拔(m)
    dril_hole_deep: float = 0.0        # 超深(m)
    angle: float = 0.0                 # 角度(°)
    hole_length: float = 0.0           # 孔长(m)
    
    # 爆破参数
    goods_type: str = ""               # 炸药类型
    hole_pattern: str = ""             # 布孔模式
    row_space: int = 0                 # 排距(m)
    hole_space: int = 0                # 孔距(m)
    
    # 高级参数
    hole_gradient_change: bool = False  # 孔距梯度变化
    depth_gradient_change: bool = False # 孔深梯度变化
    allow_adjust_row: float = 0.0      # 允许调整排距(m)
    rear_border_line_distance: float = 0.0  # 后边界线距离(m)
    row_min_distance: float = 0.0     # 排距最小距离(m)
    
    # 近远点坐标
    near_lng: float = 0.0              # 近点经度
    near_lat: float = 0.0              # 近点纬度
    near_alt: float = 0.0              # 近点海拔
    far_lng: float = 0.0               # 远点经度
    far_lat: float = 0.0               # 远点纬度
    far_alt: float = 0.0               # 远点海拔
    
    # 近远点标识
    near_point_name: str = ""          # 近点名称
    far_point_name: str = ""           # 远点名称
    near_point_id: str = ""            # 近点ID
    far_point_id: str = ""             # 远点ID
    
    # 子元素集合
    children: List = field(default_factory=list)
```

### 3. 钻孔信息实体 (HoleInfoEntity)

```python
@dataclass
class HoleInfoEntity(BaseEntity):
    """钻孔信息实体类"""
    
    # 关联字段
    blast_area_id: str = ""
    project_id: str = ""
    
    # 位置信息
    row_index: int = 0                 # 排索引
    col_index: int = 0                 # 列索引
    lng: float = 0.0                   # 经度
    lat: float = 0.0                   # 纬度
    alt: float = 0.0                   # 海拔
    
    # 钻孔参数
    aperture: float = 0.0              # 孔径(mm)
    hole_depth: float = 0.0            # 孔深(m)
    step_elevation_z: float = 0.0      # 台阶高度(m)
    hole_bottom_z: float = 0.0         # 孔底海拔(m)
    dril_hole_deep: float = 0.0        # 超深(m)
    angle: float = 0.0                 # 角度(°)
    hole_length: float = 0.0           # 孔长(m)
    goods_type: str = ""               # 炸药类型
    
    # 元素类型
    blast_element_type: str = BlastElementType.DRILL_HOLE.name
```

### 4. 测量点实体 (MeasurePointEntity)

```python
@dataclass
class MeasurePointEntity(BaseEntity):
    """测量点实体类"""
    
    # 点类型和位置
    point_type: str = ""               # 点类型
    lng: float = 0.0                   # 经度
    lat: float = 0.0                   # 纬度
    alt: float = 0.0                   # 海拔
    
    # 创建方式和关联
    create_type: str = ""              # 创建方式
    blast_area_id: str = ""            # 所属爆区ID
    project_id: str = ""               # 所属项目ID
    
    # 元素类型
    blast_element_type: str = BlastElementType.MEASUREMENT_POINT.name
```

### 5. 边界线实体 (BorderLineEntity)

```python
@dataclass
class BorderLineEntity(BaseEntity):
    """边界线实体类"""
    
    # 边界线类型
    type: str = ""                     # 边界线类型
    
    # 关联点信息
    start_point_id: str = ""           # 起始点ID
    end_point_id: str = ""             # 终止点ID
    
    # 关联区域
    blast_area_id: str = ""            # 所属爆区ID
    project_id: str = ""               # 所属项目ID
    
    # 起始点坐标
    start_lng: float = 0.0             # 起始点经度
    start_lat: float = 0.0             # 起始点纬度
    start_alt: float = 0.0             # 起始点海拔
    
    # 终止点坐标
    end_lng: float = 0.0               # 终止点经度
    end_lat: float = 0.0               # 终止点纬度
    end_alt: float = 0.0               # 终止点海拔
    
    # 元素类型
    blast_element_type: str = BlastElementType.BOUNDARY_LINE.name
```

## 辅助数据结构

### 爆破元素组实体 (BlastElementGroupEntity)

```python
@dataclass
class BlastElementGroupEntity:
    """爆破元素组实体类"""
    
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    layerType: str = LayerType.BLAST_AREA.name
    blast_element_type: str = LayerType.BLAST_AREA.name
    children: List = field(default_factory=list)
    blast_area_id: str = ""
    is_visible: bool = True
    is_locked: bool = False
```

### 数据缓存管理 (DataCache)

```python
class DataCache:
    """数据缓存类 - 单例模式"""
    
    # 缓存数据
    layers: List[BlastAreaEntity] = []     # 图层列表
    projectModel: ProjectInfo = None       # 项目模型
    has_save: Optional[bool] = None        # 保存状态
    max_measure_point_id: int = 0          # 测量点最大ID
    current_select_blast: BlastAreaEntity = None  # 当前选择爆区
```

## 接口规范

### 数据缓存接口

```python
class DataCache:
    @staticmethod
    def get_instance() -> "DataCache"
        """获取单例实例"""
    
    # 图层管理
    def set_layers(self, layers: List) -> None
    def get_layers(self) -> List
    def add_layer(self, layer) -> None
    def remove_layer(self, layer_id) -> None
    def clear_layer(self) -> None
    
    # 项目管理
    def set_project_model(self, projectModel: ProjectInfo) -> None
    def get_project_model(self) -> ProjectInfo
    
    # 状态管理
    def set_has_save(self, has_save: bool) -> None
    def get_has_save(self) -> Optional[bool]
    
    # 爆区管理
    def set_current_select_blast(self, blast: BlastAreaEntity) -> None
    def get_current_select_blast(self) -> BlastAreaEntity
    
    # 测量点管理
    def set_max_measure_point_id(self, max_id: int) -> None
    def get_max_measure_point_id(self) -> int
    
    # 地理参考
    def get_reference(self) -> GeoReference
```

### 字段名称常量接口

```python
class BaseFields:
    """基础字段名称常量"""
    
    # 字段常量
    ID = "id"
    NAME = "name"
    # ... 其他字段常量
    
    # 显示标签映射
    LABELS = {
        ID: "ID",
        NAME: "名称",
        # ... 其他标签映射
    }
    
    # 可编辑字段配置
    EDITABLE_FIELDS = {
        LABELS[ID]: False,
        LABELS[NAME]: True,
        # ... 其他编辑配置
    }

class DrillHoleFields(BaseFields):
    """钻孔字段扩展"""
    # 继承并扩展基础字段

class MeasurePointFields(BaseFields):
    """测量点字段扩展"""
    # 继承并扩展基础字段

class BoundaryLineFields(BaseFields):
    """边界线字段扩展"""
    # 继承并扩展基础字段
```

## 业务规则

### 实体关系规则
- **项目-爆区关系**: 一个项目包含多个爆区
- **爆区-元素关系**: 一个爆区包含测量点、边界线、钻孔等元素
- **测量点-边界线关系**: 边界线连接两个测量点
- **爆区-钻孔关系**: 钻孔属于特定爆区，继承爆区参数

### 坐标系统规则
- **地理坐标**: 使用经纬度(lng/lat)表示地理位置
- **高程坐标**: 使用海拔(alt)表示垂直位置
- **世界坐标**: 使用笛卡尔坐标(x/y/z)表示3D场景位置
- **坐标转换**: 支持地理坐标与世界坐标的双向转换

### 数据状态规则
- **变更跟踪**: 通过is_changed、is_added标记数据变更状态
- **软删除**: 通过is_deleted标记删除状态，不物理删除
- **版本控制**: 通过version字段支持数据版本管理
- **可见性控制**: 通过is_visible控制3D场景显示状态

### 缓存管理规则
- **单例模式**: DataCache采用单例模式，全局唯一
- **内存缓存**: 数据优先从缓存读取，提高访问性能
- **延迟加载**: 支持数据的按需加载和缓存
- **缓存同步**: 数据变更时同步更新缓存

## 关键约束条件

### 数据完整性约束
- 主键ID必须唯一，使用UUID生成
- 外键关联必须有效，如blast_area_id必须存在
- 坐标数据必须在有效范围内
- 时间戳字段自动维护，不可手动修改

### 业务逻辑约束
- 钻孔参数必须符合工程标准
- 测量点必须在爆区范围内
- 边界线必须形成封闭区域
- 近远点必须在合理距离范围内

### 3D场景约束
- 世界坐标必须在场景范围内
- 可见性状态影响渲染性能
- 锁定状态影响交互行为
- 样式字符串必须符合格式规范

### 性能约束
- 缓存数据量不能过大，避免内存溢出
- 实体对象创建要控制频率
- 大批量数据操作需要分批处理
- 复杂计算要考虑异步处理

## 扩展点设计

### 新实体类型扩展
```python
@dataclass
class NewEntity(BaseEntity):
    """新实体类型"""
    # 继承基础字段
    # 添加特有字段
    specific_field: str = ""
```

### 字段扩展
```python
class NewEntityFields(BaseFields):
    """新实体字段定义"""
    SPECIFIC_FIELD = "specific_field"
    
    LABELS = {
        **BaseFields.LABELS,
        SPECIFIC_FIELD: "特有字段"
    }
```

### 缓存扩展
```python
# 在DataCache中添加新的缓存项
new_cache_data: List = []

def set_new_cache_data(self, data: List) -> None
def get_new_cache_data(self) -> List
```

## 质量保证

### 类型安全
- 使用dataclass提供类型提示
- 字段默认值确保数据完整性
- 枚举类型确保值的有效性

### 数据一致性
- 统一的字段命名规范
- 标准化的数据结构定义
- 一致的业务规则实现

### 可维护性
- 清晰的继承层次结构
- 模块化的组件设计
- 完整的文档和注释
