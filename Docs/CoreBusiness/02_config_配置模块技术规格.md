# src/config/ 配置模块技术规格文档

## 模块概述

配置模块是系统的核心基础设施组件，负责管理应用程序的所有配置信息，包括数据库连接、日志设置、API配置、主题设置等。该模块采用分层配置策略，支持默认配置、主配置文件和本地配置文件的层次化管理。

## 文件清单及功能

### 核心文件

| 文件名 | 功能描述 | 关键职责 |
|--------|----------|----------|
| `__init__.py` | 模块初始化和配置加载 | 配置系统初始化、目录创建、全局配置访问 |
| `config.py` | 配置管理核心类 | 配置加载、保存、访问、验证 |
| `tool_config.py` | 工具栏配置定义 | 工具按钮配置、图标映射、操作绑定 |

### 配置文件

| 文件名 | 类型 | 用途 |
|--------|------|------|
| `config/config.yaml` | 主配置文件 | 全局配置设置 |
| `config/.local_config.yaml` | 本地配置文件 | 环境特定配置覆盖 |
| `config/.local_config.yaml.example` | 配置模板 | 本地配置示例 |

## 核心数据结构

### 配置层次结构

```yaml
# 配置文件结构
logging:                    # 日志配置
  version: 1
  disable_existing_loggers: false
  formatters:              # 日志格式器
    detailed: {...}
    standard: {...}
  handlers:                # 日志处理器
    console: {...}
    file: {...}
    error_file: {...}
  loggers: {...}

sqlite:                    # SQLite数据库配置
  path: string            # 数据库目录路径
  filename_pattern: string # 文件名模式
  timestamp_format: string # 时间戳格式
  full_path: string       # 完整数据库路径
  sql_print: boolean      # SQL语句打印开关

sqlserver:                 # SQL Server配置
  enabled: boolean        # 启用状态
  server: string         # 服务器地址
  port: string           # 端口号
  database: string       # 数据库名
  username: string       # 用户名
  password: string       # 密码

ftp:                      # FTP服务配置
  enabled: boolean        # 启用状态
  server: string         # FTP服务器
  port: string           # 端口
  username: string       # 用户名
  password: string       # 密码
  remote_dir: string     # 远程目录

api:                      # API配置
  drill_hole:             # 钻孔API配置
    base_url: string      # 基础URL
    api_key: string       # API密钥
    timeout: integer      # 超时时间
    retry_count: integer  # 重试次数
    retry_delay: integer  # 重试延迟
    verify_ssl: boolean   # SSL验证
    max_connections: integer # 最大连接数

system:                   # 系统配置
  theme:                  # 主题配置
    default: string       # 默认主题
    follow_system: boolean # 跟随系统主题
  logging:                # 系统日志配置
    level: string         # 日志级别
    path: string          # 日志路径
    format: string        # 日志格式
```

### 工具配置结构

```python
# 工具按钮配置结构
ToolButtonConfig = {
    "name": string,           # 工具名称
    "icon": string,           # 图标资源路径
    "type": string,           # 工具类型 ("tool", "toggle", "dropdown")
    "tooltip": string,        # 工具提示
    "enabled": boolean,       # 启用状态
    "action": string,         # 回调方法名
    "open_selected": boolean, # 是否默认选中
    "shortcut": string,       # 快捷键 (可选)
    "group": string          # 工具组 (可选)
}
```

## 接口规范

### Config类核心接口

#### 配置访问接口
```python
# 获取配置值
def get(key_path: str, default: Any = None) -> Any
    """
    参数:
        key_path: 点分隔的配置路径 (如 "sqlite.path")
        default: 默认值
    返回:
        配置值或默认值
    """

# 获取完整配置
def get_config() -> Dict[str, Any]
    """
    返回:
        完整配置字典
    """
```

#### 专用配置获取接口
```python
# 获取数据库配置
def get_database_config() -> Dict[str, Any]
    """
    返回:
        数据库配置字典，包含type、连接参数等
    """

# 获取数据库路径
def get_database_path() -> str
    """
    返回:
        数据库文件完整路径
    """

# 获取主题设置
def get_theme_settings() -> Dict[str, Any]
    """
    返回:
        主题配置字典
    """

# 获取FTP配置
def get_ftp_config() -> Dict[str, Any]
    """
    返回:
        FTP连接配置
    """

# 获取API配置
def get_api_config(api_name: str) -> Dict[str, Any]
    """
    参数:
        api_name: API名称 (如 "drill_hole")
    返回:
        API配置字典
    """
```

#### 配置管理接口
```python
# 保存配置
def save_config(config: Dict[str, Any]) -> bool
    """
    参数:
        config: 要保存的配置字典
    返回:
        保存是否成功
    """

# 创建本地配置模板
def create_local_config_template() -> bool
    """
    返回:
        创建是否成功
    """

# 获取绝对路径
def get_abs_path(relative_path: str) -> str
    """
    参数:
        relative_path: 相对路径
    返回:
        绝对路径
    """
```

### 工具配置接口

```python
# 获取工具按钮配置
def get_tool_buttons(category: str = None) -> List[Dict]
    """
    参数:
        category: 工具类别 ("file", "base", "drilling_process", "drilling")
    返回:
        工具按钮配置列表
    """

# 获取特定按钮配置
def get_button_config(button_name: str) -> Dict
    """
    参数:
        button_name: 按钮名称
    返回:
        按钮配置字典
    """
```

### 初始化接口

```python
# 初始化配置系统
def initialize_config() -> None
    """
    功能:
        - 加载配置文件
        - 创建必要目录
        - 验证配置完整性
        - 设置全局配置访问
    """

# 创建本地配置模板
def create_local_config_template() -> bool
    """
    功能:
        创建本地配置文件示例
    """
```

## 业务规则

### 配置加载优先级
1. **默认配置** (代码中定义) - 最低优先级
2. **主配置文件** (config.yaml) - 中等优先级  
3. **本地配置文件** (.local_config.yaml) - 最高优先级

### 数据库配置规则
- 支持SQLite和SQL Server双数据库模式
- SQLite为默认数据库，SQL Server可选启用
- 数据库路径自动规范化为绝对路径
- 自动创建数据库目录

### 日志配置规则
- 支持控制台、文件、错误文件三种输出
- 文件日志按天轮转，保留指定天数
- 支持详细和标准两种格式
- 自动创建日志目录

### 工具配置规则
- 工具按功能分类：文件、基础、钻孔流程、钻孔操作
- 支持工具启用/禁用状态管理
- 图标资源统一管理
- 操作回调方法名约定

## 关键约束条件

### 配置文件约束
- 配置文件必须为有效的YAML格式
- 本地配置文件不纳入版本控制
- 配置路径使用点分隔符表示层次结构
- 敏感信息优先从环境变量获取

### 路径处理约束
- 所有路径统一转换为绝对路径
- 自动创建不存在的目录
- 路径分隔符跨平台兼容

### 数据类型约束
- 端口号必须为字符串类型
- 布尔值严格类型检查
- 数值类型自动转换和验证

### 错误处理约束
- 配置加载失败时使用默认配置
- 配置保存失败时返回错误状态
- 关键配置缺失时抛出异常

## 扩展点设计

### 新配置类型扩展
- 在default_config中添加新配置节
- 实现对应的get_xxx_config()方法
- 更新配置验证逻辑

### 新工具类别扩展
- 在tool_config.py中定义新工具组
- 更新get_tool_buttons()方法
- 添加对应的图标资源

### 配置源扩展
- 支持环境变量配置源
- 支持远程配置服务
- 支持加密配置文件

## 性能考虑

### 配置缓存
- 配置加载后缓存在内存中
- 避免重复文件读取操作
- 配置更新时同步更新缓存

### 延迟加载
- 非关键配置支持延迟加载
- 大型配置文件分段加载
- 按需加载工具配置

### 文件监控
- 支持配置文件变更监控
- 热重载配置更新
- 配置变更事件通知

## 安全考虑

### 敏感信息保护
- 密码等敏感信息不记录日志
- 支持配置文件加密存储
- 环境变量优先级高于配置文件

### 配置验证
- 配置值类型和范围验证
- 路径安全性检查
- 配置完整性校验

### 访问控制
- 配置修改权限控制
- 敏感配置访问限制
- 配置审计日志
