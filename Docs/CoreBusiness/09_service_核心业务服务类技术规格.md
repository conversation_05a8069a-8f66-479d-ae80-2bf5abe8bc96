# src/service/ 核心业务服务类模块技术规格文档

## 模块概述

核心业务服务类模块是系统的业务逻辑层核心组件，负责实现爆破设计的所有核心业务功能。该模块采用服务导向架构(SOA)模式，提供高内聚、低耦合的业务服务，支持数据管理、钻孔生成、布孔算法等关键业务流程，确保业务逻辑的集中管理和复用。

## 文件清单及功能

### 核心服务类文件

| 文件名 | 功能描述 | 业务领域 | 主要职责 |
|--------|----------|----------|----------|
| `data_manage_service.py` | 数据管理服务 | 数据持久化管理 | 项目数据CRUD、缓存管理、数据同步 |
| `dril_hole_service.py` | 钻孔业务服务 | 钻孔管理 | 钻孔生成、API调用、业务协调 |
| `genrate_hole_service.py` | 布孔生成服务 | 布孔算法 | 自动布孔、几何计算、坐标转换 |

## 核心数据结构

### 1. 数据管理服务 (DataManageService)

```python
class DataManageService:
    """数据管理服务类"""
    
    def __init__(self):
        self.logger = Logger().get_logger(__name__)
    
    # 核心业务方法
    def save_project(self) -> bool
    def get_data_layers_cache(self) -> List[BlastAreaEntity]
    def clear_data_cache(self) -> None
    def update_max_measure_point_id(self) -> None
    
    # 数据查询方法
    def get_project_list(self) -> List[ProjectInfo]
    def get_blast_area_all(self) -> List[BlastArea]
    def get_measure_points_all(self, blast_area_id: str) -> List[MeasurePoint]
    def get_border_lines_all(self, blast_area_id: str) -> List[BorderLine]
    def get_hole_info_all(self, blast_area_id: str) -> List[HoleInfo]
```

### 2. 钻孔业务服务 (DrillHoleService)

```python
class DrillHoleService:
    """钻孔服务类，负责与第三方系统进行交互"""
    
    def __init__(self):
        self.logger = Logger().get_logger(__name__)
        # API配置
        api_conf = config.get_api_config("drill_hole")
        self.base_url = api_conf["base_url"]
        self.api_key = api_conf["api_key"]
        self.timeout = api_conf["timeout"]
        self.verify_ssl = api_conf["verify_ssl"]
        
        # HTTP会话配置
        self.session = requests.Session()
        self.headers = {"Content-Type": "application/json"}
    
    # 核心业务方法
    def create_drill_holes(self) -> Optional[List[Dict]]
    def generate_drill_holes(self, punch_request: PunchRequest) -> PunchResponseEntity
    def call_third_party_api(self, punch_request: PunchRequest) -> Optional[PunchResponseEntity]
```

### 3. 布孔生成服务 (GenrateHoleService)

```python
class GenrateHoleService:
    """布孔生成服务类"""
    
    def __init__(self):
        self.transformer_to_utm = None
        self.transformer_to_wgs84 = None
    
    # 核心算法方法
    def arrange_holes(self, 
                     ProjectID: str,
                     BlastID: str, 
                     MeasurePoints: List[MeasurePointRequest],
                     Spacing: float,
                     Burden: float,
                     NearFarPoint: str,
                     LineType: str,
                     HoleDiameter: float,
                     HoleDepth: float,
                     StepElevation: float,
                     BottomElevation: float,
                     Over_Depth: float,
                     Angle: float,
                     HoleLength: float) -> PunchResponseEntity
    
    # 辅助计算方法
    def _init_projection(self, center_lat: float, center_lon: float) -> None
    def _to_utm(self, lon: float, lat: float) -> Tuple[float, float]
    def _to_wgs84(self, x: float, y: float) -> Tuple[float, float]
    def _is_point_in_polygon(self, point: Tuple[float, float], 
                           polygon_points: List[Tuple[float, float]]) -> bool
```

## 接口规范

### DataManageService接口

#### 项目管理接口
```python
def save_project(self) -> bool
    """
    保存项目数据到数据库
    
    功能:
        - 遍历缓存中的图层数据
        - 根据数据状态执行增删改操作
        - 更新缓存保存状态
        
    返回:
        保存是否成功
        
    异常:
        ValueError: 项目数据或图层数据为空
        Exception: 保存过程中发生错误
    """

def get_data_layers_cache(self) -> List[BlastAreaEntity]
    """
    获取数据层缓存
    
    功能:
        - 优先从缓存获取数据
        - 缓存为空时从数据库加载
        - 包含完整的关联数据
        
    返回:
        爆区实体列表
        
    异常:
        Exception: 数据库操作失败
    """

def clear_data_cache(self) -> None
    """
    清空数据缓存
    
    功能:
        - 清空图层缓存
        - 重置保存状态
        - 重置项目模型
        - 重置选择状态
    """
```

#### 数据查询接口
```python
def get_project_list(self) -> List[ProjectInfo]
    """获取所有项目列表"""

def get_blast_area_all(self) -> List[BlastArea]
    """获取所有爆破区域"""

def get_measure_points_all(self, blast_area_id: str) -> List[MeasurePoint]
    """根据爆区ID获取所有测量点"""

def get_border_lines_all(self, blast_area_id: str) -> List[BorderLine]
    """根据爆区ID获取所有边界线"""

def get_hole_info_all(self, blast_area_id: str) -> List[HoleInfo]
    """根据爆区ID获取所有钻孔信息"""
```

#### 事件处理接口
```python
def on_drill_hole_generated(self, event: EventParam) -> None
    """
    处理钻孔生成事件
    
    参数:
        event: 包含钻孔列表和爆区ID的事件参数
        
    功能:
        - 验证事件数据有效性
        - 将生成的钻孔添加到图层
        - 更新缓存状态
        
    异常:
        ValueError: 事件数据无效
    """
```

### DrillHoleService接口

#### 钻孔生成接口
```python
def create_drill_holes(self) -> Optional[List[Dict]]
    """
    创建钻孔的主要业务方法
    
    功能:
        - 验证爆区选择状态
        - 检查近远点设置
        - 收集测量点和边界线
        - 构建布孔请求
        - 调用布孔算法
        
    返回:
        创建成功的钻孔信息列表，失败返回None
        
    业务规则:
        - 必须选择爆区
        - 必须设置近远点
        - 必须有有效的测量点
    """

def generate_drill_holes(self, punch_request: PunchRequest) -> PunchResponseEntity
    """
    生成钻孔核心方法
    
    参数:
        punch_request: 钻孔请求参数
        
    返回:
        钻孔响应实体
        
    功能:
        - 参数验证和类型转换
        - 调用布孔生成服务
        - 结果封装和返回
    """
```

#### API调用接口
```python
def call_third_party_api(self, punch_request: PunchRequest) -> Optional[PunchResponseEntity]
    """
    调用第三方API进行布孔计算
    
    参数:
        punch_request: 钻孔请求参数
        
    返回:
        API响应结果，失败返回None
        
    功能:
        - 构建API请求
        - 发送HTTP请求
        - 解析响应结果
        - 错误处理和重试
    """
```

### GenrateHoleService接口

#### 布孔算法接口
```python
def arrange_holes(self, ProjectID: str, BlastID: str, 
                 MeasurePoints: List[MeasurePointRequest],
                 Spacing: float, Burden: float, NearFarPoint: str,
                 LineType: str, HoleDiameter: float, HoleDepth: float,
                 StepElevation: float, BottomElevation: float,
                 Over_Depth: float, Angle: float, 
                 HoleLength: float) -> PunchResponseEntity
    """
    自动布孔核心算法
    
    参数:
        ProjectID: 项目ID
        BlastID: 爆区ID
        MeasurePoints: 测量点列表
        Spacing: 孔距(米)
        Burden: 排距(米)
        NearFarPoint: 近远点坐标字符串
        LineType: 边界线类型
        其他: 钻孔物理参数
        
    返回:
        布孔响应实体
        
    算法流程:
        1. 参数验证和预处理
        2. 坐标系统初始化
        3. 坐标转换(WGS84 -> UTM)
        4. 计算布孔方向和范围
        5. 生成孔位坐标
        6. 边界检查和过滤
        7. 坐标转换(UTM -> WGS84)
        8. 结果封装返回
    """
```

#### 坐标转换接口
```python
def _init_projection(self, center_lat: float, center_lon: float) -> None
    """
    初始化坐标投影系统
    
    参数:
        center_lat: 中心纬度
        center_lon: 中心经度
        
    功能:
        - 计算合适的UTM投影带
        - 初始化坐标转换器
        - 设置投影参数
    """

def _to_utm(self, lon: float, lat: float) -> Tuple[float, float]
    """
    WGS84坐标转UTM坐标
    
    参数:
        lon: 经度
        lat: 纬度
        
    返回:
        (UTM_X, UTM_Y) 坐标元组
    """

def _to_wgs84(self, x: float, y: float) -> Tuple[float, float]
    """
    UTM坐标转WGS84坐标
    
    参数:
        x: UTM X坐标
        y: UTM Y坐标
        
    返回:
        (经度, 纬度) 坐标元组
    """
```

#### 几何计算接口
```python
def _is_point_in_polygon(self, point: Tuple[float, float], 
                        polygon_points: List[Tuple[float, float]]) -> bool
    """
    判断点是否在多边形内
    
    参数:
        point: 待检查的点坐标
        polygon_points: 多边形顶点列表
        
    返回:
        点是否在多边形内
        
    算法:
        使用射线投射算法进行点在多边形内的判断
    """
```

## 业务规则

### 数据管理服务规则
- **缓存优先**: 优先从内存缓存读取数据，提高访问性能
- **延迟加载**: 数据按需从数据库加载到缓存
- **状态跟踪**: 跟踪数据的增删改状态，支持批量保存
- **事务安全**: 保存操作使用数据库事务确保一致性

### 钻孔业务服务规则
- **前置检查**: 执行业务操作前进行完整性检查
- **参数验证**: 严格验证输入参数的有效性和合理性
- **错误处理**: 完善的错误处理和用户提示机制
- **API集成**: 支持第三方API调用和本地算法双模式

### 布孔生成服务规则
- **坐标精度**: 使用UTM投影确保计算精度
- **边界约束**: 生成的钻孔必须在指定边界内
- **几何规律**: 支持方形和三角形(梅花形)布孔模式
- **参数适应**: 根据地形和工程要求调整布孔参数

### 服务协作规则
- **事件驱动**: 服务间通过事件系统进行解耦通信
- **依赖注入**: 服务依赖通过配置系统管理
- **异常传播**: 底层异常向上层服务传播并处理
- **日志记录**: 关键业务操作记录详细日志

## 关键约束条件

### 性能约束
- 单次布孔操作不超过30秒
- 内存使用不超过1GB
- 支持最多10,000个钻孔的生成
- 数据库操作响应时间不超过5秒

### 精度约束
- 坐标计算精度达到厘米级
- 角度计算精度0.1度
- 距离计算精度1厘米
- 面积计算精度0.01平方米

### 业务约束
- 钻孔间距不小于0.5米
- 排距不小于0.5米
- 钻孔深度不超过50米
- 钻孔角度不超过90度

### 系统约束
- API调用超时时间30秒
- 重试次数不超过3次
- 并发请求数不超过10个
- 缓存数据量不超过100MB

## 扩展点设计

### 新服务类扩展
```python
class NewBusinessService:
    """新业务服务类"""
    
    def __init__(self):
        self.logger = Logger().get_logger(__name__)
    
    def execute_business_logic(self, params) -> Result:
        """执行业务逻辑"""
        pass
```

### 算法扩展
```python
class AdvancedHoleArrangement:
    """高级布孔算法"""
    
    def optimize_hole_layout(self, constraints) -> Layout:
        """优化布孔布局"""
        pass
    
    def calculate_blast_efficiency(self, holes) -> float:
        """计算爆破效率"""
        pass
```

### API集成扩展
```python
class ExternalAPIService:
    """外部API服务"""
    
    def call_optimization_api(self, data) -> Result:
        """调用优化API"""
        pass
    
    def call_simulation_api(self, params) -> Simulation:
        """调用仿真API"""
        pass
```

## 使用模式

### 数据管理模式
```python
# 获取数据管理服务
data_service = DataManageService()

# 获取缓存数据
layers = data_service.get_data_layers_cache()

# 保存项目数据
success = data_service.save_project()

# 清空缓存
data_service.clear_data_cache()
```

### 钻孔生成模式
```python
# 创建钻孔服务
drill_service = DrillHoleService()

# 生成钻孔
holes = drill_service.create_drill_holes()

# 使用请求对象生成
punch_request = PunchRequest(...)
response = drill_service.generate_drill_holes(punch_request)
```

### 布孔算法模式
```python
# 创建布孔服务
hole_service = GenrateHoleService()

# 执行布孔算法
result = hole_service.arrange_holes(
    ProjectID="P001",
    BlastID="BA001", 
    MeasurePoints=measure_points,
    Spacing=3.0,
    Burden=2.5,
    # ... 其他参数
)
```

## 质量保证

### 错误处理
- 完整的异常捕获和处理
- 详细的错误信息记录
- 用户友好的错误提示
- 系统状态恢复机制

### 日志记录
- 关键业务操作日志
- 性能监控日志
- 错误和异常日志
- 调试信息日志

### 测试支持
- 单元测试覆盖
- 集成测试支持
- 性能测试验证
- 边界条件测试

### 可维护性
- 清晰的代码结构
- 完整的文档注释
- 标准化的接口设计
- 模块化的组件划分
