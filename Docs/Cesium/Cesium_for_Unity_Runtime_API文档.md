# Cesium for Unity Runtime API 文档

## 概述

Cesium for Unity Runtime提供了在Unity中集成Cesium功能的核心API。该运行时库使用Unity引擎6.1和UI Toolkit技术栈，支持大规模地理空间数据的可视化和交互。

## 命名空间

所有API都在`CesiumForUnity`命名空间中定义。

## 核心组件

### 1. 地理参考系统 (Georeference System)

#### CesiumGeoreference
控制全球地理空间坐标如何映射到Unity场景坐标。

**主要功能：**
- 管理地理参考系统的原点和坐标转换
- 支持经纬度高度和ECEF坐标系统间的转换
- 提供椭球体定义和坐标系统变换

**核心属性：**
- `originPlacement`: 原点放置策略
- `originAuthority`: 原点权威坐标类型
- `latitude`/`longitude`/`height`: 经纬度高度坐标
- `ecefX`/`ecefY`/`ecefZ`: ECEF坐标
- `scale`: 地理参考系统缩放比例
- `ellipsoid`: 椭球体定义

**主要方法：**
```csharp
// 设置原点坐标
void SetOriginEarthCenteredEarthFixed(double x, double y, double z)
void SetOriginLongitudeLatitudeHeight(double longitude, double latitude, double height)

// 坐标转换
double3 TransformUnityPositionToEarthCenteredEarthFixed(double3 unityPosition)
double3 TransformEarthCenteredEarthFixedPositionToUnity(double3 earthCenteredEarthFixed)

// 方向转换
double3 TransformUnityDirectionToEarthCenteredEarthFixed(double3 unityDirection)
double3 TransformEarthCenteredEarthFixedDirectionToUnity(double3 earthCenteredEarthFixedDirection)

// 锚点管理
void AddGlobeAnchor(CesiumGlobeAnchor globeAnchor)
void RemoveGlobeAnchor(CesiumGlobeAnchor globeAnchor)
```

#### CesiumGlobeAnchor
将游戏对象锚定到地球表面，保证对象在地理参考系统变化时保持正确位置。

**主要功能：**
- 高精度地理定位
- 自动方向调整以适应地球曲率
- 支持Transform变更检测

**核心属性：**
- `adjustOrientationForGlobeWhenMoving`: 是否根据地球曲率调整方向
- `detectTransformChanges`: 是否检测Transform变更
- `localToGlobeFixedMatrix`: 本地到ECEF的变换矩阵
- `longitudeLatitudeHeight`: 经纬度高度坐标
- `positionGlobeFixed`: ECEF坐标位置

**主要方法：**
```csharp
void Sync() // 同步Transform和地理坐标
void Restart() // 重新初始化锚点
```

#### CesiumEllipsoid
表示地球椭球体的数学模型。

**主要功能：**
- 椭球体几何参数管理
- 坐标系统转换
- 表面法线计算

**核心方法：**
```csharp
// 椭球体参数
double3 GetRadii()
void SetRadii(double3 newRadii)
double GetMaximumRadius()
double GetMinimumRadius()

// 坐标转换
double3 LongitudeLatitudeHeightToCenteredFixed(double3 longitudeLatitudeHeight)
double3 CenteredFixedToLongitudeLatitudeHeight(double3 ellipsoidCenteredEllipsoidFixed)

// 几何计算
double3? ScaleToGeodeticSurface(double3 ellipsoidCenteredEllipsoidFixed)
double3 GeodeticSurfaceNormal(double3 ellipsoidCenteredEllipsoidFixed)
```

### 2. 3D瓦片系统 (3D Tiles System)

#### Cesium3DTileset
管理3D Tiles格式的瓦片集合，支持大规模3D地理空间数据的流式加载。

**主要功能：**
- 支持从Cesium Ion或URL加载3D瓦片
- 级别细节(LOD)管理
- 瓦片缓存和加载优化
- 材质和渲染参数控制

**核心属性：**
- `tilesetSource`: 瓦片源类型（Ion/URL/Ellipsoid）
- `ionAssetID`: Cesium Ion资产ID
- `ionAccessToken`: Ion访问令牌
- `url`: 瓦片集URL
- `maximumScreenSpaceError`: 最大屏幕空间误差
- `maximumCachedBytes`: 最大缓存字节数
- `maximumSimultaneousTileLoads`: 最大同时加载瓦片数

**主要方法：**
```csharp
void RecreateTileset() // 重新创建瓦片集
void FocusTileset() // 聚焦瓦片集
float ComputeLoadProgress() // 计算加载进度
Task<CesiumSampleHeightResult> SampleHeightMostDetailed(params double3[] longitudeLatitudeHeightPositions) // 采样高度

// 事件
event Action<GameObject> OnTileGameObjectCreated // 瓦片GameObject创建事件
static event TilesetLoadFailureDelegate OnCesium3DTilesetLoadFailure // 加载失败事件
```

#### CesiumRasterOverlay
栅格叠加层的基类，用于在3D瓦片上叠加2D图像。

**主要功能：**
- 材质键管理
- 纹理参数控制
- 缓存优化

**核心属性：**
- `materialKey`: 材质键
- `showCreditsOnScreen`: 是否在屏幕上显示版权信息
- `maximumScreenSpaceError`: 最大屏幕空间误差
- `maximumTextureSize`: 最大纹理尺寸
- `maximumSimultaneousTileLoads`: 最大同时加载瓦片数

**主要方法：**
```csharp
void AddToTileset() // 添加到瓦片集
void RemoveFromTileset() // 从瓦片集移除
void Refresh() // 刷新叠加层
```

### 3. 相机控制系统 (Camera Control System)

#### CesiumCameraController
专为地球浏览优化的相机控制器。

**主要功能：**
- 全球导航控制
- 动态速度调整
- 动态剪切平面调整
- 支持键盘和鼠标输入

**核心属性：**
- `enableMovement`: 是否启用移动
- `enableRotation`: 是否启用旋转
- `enableDynamicSpeed`: 是否启用动态速度
- `defaultMaximumSpeed`: 默认最大速度
- `enableDynamicClippingPlanes`: 是否启用动态剪切平面

#### CesiumFlyToController
平滑飞行控制器，支持自定义飞行路径。

**主要功能：**
- 平滑飞行动画
- 自定义飞行轨迹
- 高度配置文件控制

**核心属性：**
- `flyToAltitudeProfileCurve`: 飞行高度曲线
- `flyToProgressCurve`: 飞行进度曲线
- `flyToMaximumAltitudeCurve`: 最大高度曲线
- `flyToDuration`: 飞行持续时间

**主要方法：**
```csharp
void FlyToLocationEarthCenteredEarthFixed(double3 destination, float yawAtDestination, float pitchAtDestination, bool canInterruptByMoving)
void FlyToLocationLongitudeLatitudeHeight(double3 destination, float yawAtDestination, float pitchAtDestination, bool canInterruptByMoving)

// 事件
event CompletedFlightDelegate OnFlightComplete // 飞行完成事件
event InterruptedFlightDelegate OnFlightInterrupted // 飞行中断事件
```

### 4. 性能优化系统 (Performance Optimization System)

#### CesiumOriginShift
自动原点偏移组件，通过保持坐标值较小来提高渲染精度。

**主要功能：**
- 自动原点偏移
- 子场景管理
- 物理系统同步

**核心属性：**
- `distance`: 最大偏移距离阈值

#### CesiumSubScene
子场景管理，用于在特定位置定义相对正常的Unity场景。

### 5. 元数据和特征系统 (Metadata and Feature System)

#### CesiumFeature（已弃用）
访问瓦片特征的元数据。

**主要方法：**
```csharp
// 获取各种类型的属性值
sbyte GetInt8(string property, sbyte defaultValue)
byte GetUInt8(string property, byte defaultValue)
Int16 GetInt16(string property, Int16 defaultValue)
// ... 其他类型的getter方法

// 获取数组属性值
sbyte GetComponentInt8(string property, int index, sbyte defaultValue)
// ... 其他类型的数组getter方法

// 元数据信息
int GetComponentCount(string property)
MetadataType GetComponentType(string property)
MetadataType GetMetadataType(string property)
bool IsNormalized(string property)
```

#### CesiumMetadata（已弃用）
提供对瓦片集特征元数据的访问。

**主要方法：**
```csharp
CesiumFeature[] GetFeatures(Transform transform, int triangleIndex)
```

### 6. 几何工具 (Geometry Tools)

#### CesiumCartographicPolygon
基于样条的地理多边形，用于在3D瓦片上栅格化2D多边形。

**主要功能：**
- 基于Unity Splines的多边形定义
- 地理坐标转换
- 需要Unity 2022.2+和Splines包

**内部方法：**
```csharp
List<double2> GetCartographicPoints(Matrix4x4 worldToTileset)
```

### 7. 版权和信用系统 (Credit System)

#### CesiumCreditSystem
管理瓦片集和栅格叠加层的版权信息。

**主要功能：**
- 版权信息收集和显示
- 图片加载和管理
- HTML版权信息解析

**内部类：**
- `CesiumCredit`: 代表HTML版权信息
- `CesiumCreditComponent`: 代表版权组件

**主要方法：**
```csharp
static CesiumCreditSystem GetDefaultCreditSystem()
```

**内部事件：**
```csharp
internal event CreditsUpdateDelegate OnCreditsUpdate
```

### 8. 配置和设置系统 (Configuration System)

#### CesiumIonServer
定义Cesium Ion服务器配置。

**主要功能：**
- 服务器URL配置
- OAuth2认证设置
- 访问令牌管理

**核心属性：**
- `serverUrl`: 服务器URL
- `apiUrl`: API端点URL
- `oauth2ApplicationID`: OAuth2应用ID
- `defaultIonAccessTokenId`: 默认访问令牌ID
- `defaultIonAccessToken`: 默认访问令牌

**静态属性：**
```csharp
static CesiumIonServer defaultServer // 默认服务器
static CesiumIonServer serverForNewObjects // 新对象的服务器
```

#### CesiumRuntimeSettings
运行时设置的单例类。

**主要功能：**
- 缓存设置
- 访问令牌管理（已弃用）

**核心属性：**
```csharp
static int requestsPerCachePrune // 缓存清理请求数
static ulong maxItems // 最大缓存项数
```

## 枚举类型

### CesiumDataSource
瓦片集数据源类型：
- `FromCesiumIon`: 来自Cesium Ion
- `FromUrl`: 来自URL
- `FromEllipsoid`: 来自椭球体

### CesiumGeoreferenceOriginPlacement
地理参考原点放置策略：
- `TrueOrigin`: 真实原点
- `CartographicOrigin`: 地理原点

### CesiumGeoreferenceOriginAuthority
原点权威坐标类型：
- `LongitudeLatitudeHeight`: 经纬度高度
- `EarthCenteredEarthFixed`: 地心地固坐标

### MetadataType
元数据类型：
- `None`, `Int8`, `UInt8`, `Int16`, `UInt16`
- `Int32`, `UInt32`, `Int64`, `UInt64`
- `Float`, `Double`, `Boolean`, `String`, `Array`

## 事件系统

### 全局事件
- `Cesium3DTileset.OnCesium3DTilesetLoadFailure`: 瓦片集加载失败
- `CesiumRasterOverlay.OnCesiumRasterOverlayLoadFailure`: 栅格叠加层加载失败

### 组件事件
- `CesiumGeoreference.OnGeoreferenceOriginChanged`: 地理参考原点改变
- `Cesium3DTileset.OnTileGameObjectCreated`: 瓦片GameObject创建
- `CesiumFlyToController.OnFlightComplete`: 飞行完成
- `CesiumFlyToController.OnFlightInterrupted`: 飞行中断

## 使用示例

### 基本场景设置
```csharp
// 创建地理参考系统
CesiumGeoreference georeference = gameObject.AddComponent<CesiumGeoreference>();
georeference.SetOriginLongitudeLatitudeHeight(-105.25737, 39.736401, 2250.0);

// 创建3D瓦片集
GameObject tilesetObject = new GameObject("Tileset");
Cesium3DTileset tileset = tilesetObject.AddComponent<Cesium3DTileset>();
tileset.tilesetSource = CesiumDataSource.FromCesiumIon;
tileset.ionAssetID = 1;
```

### 相机控制设置
```csharp
// 添加相机控制器
CesiumCameraController cameraController = camera.AddComponent<CesiumCameraController>();
cameraController.enableMovement = true;
cameraController.enableRotation = true;
cameraController.enableDynamicSpeed = true;

// 添加飞行控制器
CesiumFlyToController flyController = camera.AddComponent<CesiumFlyToController>();
flyController.flyToDuration = 5.0;
```

### 全球锚点设置
```csharp
// 为对象添加全球锚点
CesiumGlobeAnchor anchor = gameObject.AddComponent<CesiumGlobeAnchor>();
anchor.longitudeLatitudeHeight = new double3(-105.25737, 39.736401, 2250.0);
anchor.adjustOrientationForGlobeWhenMoving = true;
```

## 设计原则分析

### ✅ 符合的设计原则

1. **单一职责原则 (SRP)**
   - 每个组件都有明确的职责：`CesiumGeoreference`负责坐标转换，`Cesium3DTileset`负责瓦片管理，`CesiumCameraController`负责相机控制

2. **开放封闭原则 (OCP)**
   - `CesiumRasterOverlay`作为抽象基类，可以扩展不同类型的栅格叠加层

3. **高内聚，低耦合**
   - 组件间通过事件系统和明确的接口进行通信

4. **最少知识原则 (LoD)**
   - 组件只暴露必要的公共接口

### ❌ 需要改进的设计问题

1. **YAGNI 原则违反**
   - 部分组件包含过多配置选项，可能存在过度设计

2. **DRY 原则违反**
   - 多个组件中存在相似的坐标转换逻辑

3. **部分类过于庞大**
   - `Cesium3DTileset`类超过600行，建议使用partial关键字进行功能拆分

## 总结

Cesium for Unity Runtime提供了完整的地理空间数据可视化解决方案，支持大规模3D瓦片数据的加载、渲染和交互。API设计总体上遵循了良好的面向对象设计原则，但在一些细节方面还有改进空间。

该运行时库特别适合用于：
- 数字地球应用
- 地理信息系统(GIS)
- 城市规划和建筑可视化
- 虚拟旅游和教育应用
- 仿真和训练系统 