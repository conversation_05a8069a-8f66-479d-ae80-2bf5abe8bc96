# Cesium交互球体功能说明

## 功能概述

新增了Cesium倾斜摄影模型的鼠标射线交点检测功能，当Cesium地形启用时，会显示一个球体跟随鼠标指针与Cesium模型的交点位置实时移动。这个功能可以帮助用户直观地看到鼠标在Cesium模型上的精确位置。

## 功能特性

### 1. 实时交点检测
- **射线检测**: 使用鼠标位置发射射线，检测与Cesium倾斜摄影模型的交点
- **实时跟随**: 球体实时跟随鼠标指针在Cesium模型表面的位置
- **智能显示**: 只有当鼠标指向Cesium模型时才显示球体

### 2. 自动状态管理
- **联动控制**: 与Cesium地形的启用/禁用状态联动
- **自动创建**: 首次检测到交点时自动创建球体
- **自动隐藏**: 当Cesium禁用或鼠标离开模型时自动隐藏球体

### 3. 可自定义设置
- **球体大小**: 可调整球体的显示大小
- **球体颜色**: 可自定义球体的颜色和透明度
- **图层掩码**: 可配置检测的Cesium模型图层
- **显示开关**: 可以完全禁用此功能

## 设置方法

### 1. 基本设置
1. 确保场景中有Cesium倾斜摄影模型（通常在CesiumGeoreference下）
2. 确保Cesium模型启用了物理碰撞器（Create Physics Meshes选项）
3. 在InputManager的Inspector中配置相关参数

### 2. 参数配置

#### Cesium交互球体设置
- **Cesium Interaction Sphere**: 球体对象引用（自动创建，通常无需手动设置）
- **Cesium Layer Mask**: Cesium模型所在的图层掩码（默认为-1，检测所有图层）
- **Sphere Size**: 球体大小（默认0.2）
- **Sphere Color**: 球体颜色（默认红色）
- **Show Cesium Interaction**: 是否显示交互球体（默认启用）

## 使用方法

### 基本操作
1. **启用Cesium**: 按 `T` 键启用Cesium地形
2. **移动鼠标**: 将鼠标移动到Cesium模型表面
3. **观察球体**: 红色球体会跟随鼠标在模型表面移动
4. **禁用功能**: 按 `T` 键禁用Cesium地形，球体会自动隐藏

### 测试功能
1. 将`CesiumInteractionTester.cs`脚本添加到场景中的任意GameObject
2. 运行场景后可以看到：
   - **F1键**: 切换调试信息显示
   - **F3键**: 执行交互功能测试
   - **屏幕左上角**: 实时状态显示

## 技术实现

### 射线检测原理
```csharp
// 从鼠标位置发射射线
Ray ray = mainCamera.ScreenPointToRay(mousePosition);

// 检测与Cesium模型的交点
if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, cesiumLayerMask))
{
    // 更新球体位置到交点
    sphere.transform.position = hit.point;
}
```

### 自动管理机制
- **状态检查**: 每帧检查Cesium地形是否启用
- **延迟创建**: 只有在需要时才创建球体对象
- **内存管理**: 自动清理不需要的球体对象
- **性能优化**: 避免不必要的射线检测计算

## 故障排除

### 常见问题及解决方案

1. **球体不显示**
   - 检查Cesium地形是否已启用（按T键）
   - 确认`Show Cesium Interaction`选项已启用
   - 验证鼠标是否指向Cesium模型表面
   - 检查Cesium模型是否有物理碰撞器

2. **球体位置不准确**
   - 检查`Cesium Layer Mask`设置是否正确
   - 确认Cesium模型的碰撞器设置正确
   - 验证相机设置是否正常

3. **性能问题**
   - 调整`Cesium Layer Mask`以减少不必要的检测
   - 考虑禁用`Show Debug Raycast`选项
   - 检查是否有多个重叠的射线检测

### 调试方法
1. 启用`Show Debug Raycast`选项查看射线检测过程
2. 使用CesiumInteractionTester查看详细状态信息
3. 在Console中观察射线检测日志

## API接口

### 公共方法
```csharp
// 获取InputManager实例
InputManager inputManager = FindObjectOfType<InputManager>();

// 销毁交互球体
inputManager.DestroyCesiumInteractionSphere();

// 设置球体显示状态
inputManager.showCesiumInteraction = true/false;

// 设置球体属性
inputManager.sphereSize = 0.3f;
inputManager.sphereColor = Color.blue;
inputManager.cesiumLayerMask = LayerMask.GetMask("Cesium");
```

### 配置属性
- `cesiumInteractionSphere`: 球体对象引用
- `cesiumLayerMask`: Cesium图层掩码
- `sphereSize`: 球体大小
- `sphereColor`: 球体颜色
- `showCesiumInteraction`: 显示开关

## 扩展建议

### 可能的增强功能
1. **多种指示器**: 支持不同形状的指示器（立方体、箭头等）
2. **交互信息**: 显示交点的坐标信息或高程数据
3. **点击交互**: 支持点击交点进行进一步操作
4. **轨迹记录**: 记录鼠标在模型上的移动轨迹
5. **材质效果**: 添加发光、闪烁等视觉效果

### 自定义开发
如果需要更复杂的交互功能，可以：
1. 扩展现有的射线检测系统
2. 创建自定义的交互指示器
3. 集成到项目的UI系统中
4. 添加更多的视觉反馈效果

## 兼容性

- **Unity版本**: 兼容当前项目使用的Unity版本
- **Cesium版本**: 兼容Cesium for Unity插件
- **渲染管线**: 支持URP渲染管线
- **平台支持**: 支持所有Cesium支持的平台

## 总结

Cesium交互球体功能为用户提供了直观的Cesium模型交互体验，通过实时的视觉反馈帮助用户精确定位鼠标在倾斜摄影模型上的位置。功能设计考虑了性能优化和用户体验，提供了灵活的配置选项和完整的API接口，适合各种应用场景。
