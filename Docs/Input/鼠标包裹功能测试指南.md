# 鼠标包裹功能测试指南

## 功能概述

鼠标包裹功能模仿了Unity Scene视图的行为，当鼠标在旋转视角时到达屏幕边缘，会自动跳转到屏幕对面，实现无限旋转的效果。

## 测试步骤

### 1. 基本测试

1. **启动场景**
   - 确保场景中有InputManager组件
   - 确保鼠标包裹功能已启用（默认启用）

2. **测试旋转包裹**
   - 按住鼠标中键开始旋转视角
   - 将鼠标拖动到屏幕左边缘，观察鼠标是否跳转到右边缘
   - 将鼠标拖动到屏幕右边缘，观察鼠标是否跳转到左边缘
   - 将鼠标拖动到屏幕上边缘，观察鼠标是否跳转到下边缘
   - 将鼠标拖动到屏幕下边缘，观察鼠标是否跳转到上边缘

3. **测试其他模式**
   - 按住Shift+中键进行平移，确认鼠标包裹不会触发
   - 按住Ctrl+中键进行平滑缩放，确认鼠标包裹不会触发

### 2. 使用测试脚本

1. **添加测试脚本**
   - 将`MouseWrappingTester.cs`脚本添加到场景中的任意GameObject上

2. **测试功能**
   - 运行场景
   - 按F1键切换鼠标包裹功能的启用/禁用状态
   - 观察屏幕左上角的调试信息

### 3. 参数调整测试

1. **调整包裹边缘距离**
   - 在InputManager的Inspector中找到"鼠标包裹设置"
   - 调整`Wrap Border Size`参数（默认50像素）
   - 测试不同值对包裹触发时机的影响

2. **调整安全区域**
   - 调整`Wrap Dead Zone`参数（默认20像素）
   - 这个参数控制包裹后的安全区域，防止立即再次触发

3. **调整冷却时间**
   - 调整`Wrap Cooldown`参数（默认0.1秒）
   - 控制两次包裹之间的最小间隔时间

4. **建议的参数值**
   - 小屏幕（1920x1080以下）：
     - Border Size: 30-50像素
     - Dead Zone: 15-25像素
     - Cooldown: 0.05-0.1秒
   - 大屏幕（2K/4K）：
     - Border Size: 50-100像素
     - Dead Zone: 20-30像素
     - Cooldown: 0.1-0.15秒
   - 超宽屏：
     - Border Size: 100-150像素
     - Dead Zone: 25-35像素
     - Cooldown: 0.1-0.2秒

5. **运行时调整**（使用MouseWrappingTester脚本）
   - 数字键盘+/-：调整边缘触发距离
   - [/]键：调整安全区域大小

## 预期行为

### 正常情况
- 鼠标到达边缘时平滑跳转到对面
- 视角旋转连续，无明显跳跃
- 只在旋转模式下生效
- **无缝旋转**：包裹发生时旋转保持连续性，无视角跳变

### 异常情况排查
- **鼠标跳转不平滑**：检查帧率是否稳定，调整`wrapCooldown`参数
- **包裹不触发**：检查`enableMouseWrapping`是否为true
- **包裹过于敏感**：增大`wrapBorderSize`值或增大`wrapCooldown`
- **包裹不够敏感**：减小`wrapBorderSize`值
- **鼠标来回跳动**：增大`wrapDeadZone`值，确保安全区域足够大
- **包裹后立即再次触发**：检查`wrapDeadZone`设置，应该大于`wrapBorderSize`的一半
- **旋转跳变（已解决）**：新版本已优化，包裹时保持旋转连续性

## API使用示例

```csharp
public class CameraController : MonoBehaviour
{
    private InputManager inputManager;

    void Start()
    {
        inputManager = FindObjectOfType<InputManager>();

        // 根据屏幕尺寸自动调整参数
        AdjustWrappingParameters();
    }

    void Update()
    {
        // 按M键切换鼠标包裹
        if (Input.GetKeyDown(KeyCode.M))
        {
            inputManager.ToggleMouseWrapping();
        }

        // 检查当前状态
        if (inputManager.IsMouseWrappingEnabled())
        {
            // 鼠标包裹已启用
        }
    }

    void AdjustWrappingParameters()
    {
        // 根据屏幕尺寸调整参数
        float screenDiagonal = Mathf.Sqrt(Screen.width * Screen.width + Screen.height * Screen.height);

        if (screenDiagonal < 2000) // 小屏幕
        {
            inputManager.SetWrapBorderSize(40f);
            inputManager.SetWrapDeadZone(20f);
            inputManager.SetWrapCooldown(0.08f);
        }
        else if (screenDiagonal < 3000) // 中等屏幕
        {
            inputManager.SetWrapBorderSize(60f);
            inputManager.SetWrapDeadZone(25f);
            inputManager.SetWrapCooldown(0.1f);
        }
        else // 大屏幕
        {
            inputManager.SetWrapBorderSize(80f);
            inputManager.SetWrapDeadZone(30f);
            inputManager.SetWrapCooldown(0.12f);
        }
    }
}
```

## 注意事项

1. **性能影响**：鼠标包裹功能对性能影响极小
2. **兼容性**：支持所有Unity支持的平台
3. **多显示器**：在多显示器环境下可能需要特殊处理
4. **全屏模式**：在全屏模式下效果最佳

## 故障排除

### 常见问题

1. **鼠标包裹不工作**
   - 检查`enableMouseWrapping`是否为true
   - 确认正在使用旋转模式（鼠标中键）
   - 检查是否有其他脚本干扰鼠标输入

2. **包裹过于频繁或卡顿**
   - 增大`wrapCooldown`参数（推荐0.1-0.2秒）
   - 增大`wrapBorderSize`参数
   - 检查帧率是否稳定

3. **鼠标来回跳动**
   - 增大`wrapDeadZone`参数
   - 确保`wrapDeadZone`大于`wrapBorderSize`的一半
   - 检查屏幕分辨率设置

4. **视角跳跃（已优化）**
   - 新版本已解决包裹时的视角跳跃问题
   - 如果仍有问题，确保没有其他脚本同时控制相机
   - 检查旋转灵敏度设置

5. **包裹位置不准确**
   - 检查多显示器设置
   - 确认游戏运行在正确的显示器上
   - 调整`wrapDeadZone`参数

### 调试信息

使用`MouseWrappingTester`脚本可以查看：
- 当前鼠标位置
- 屏幕尺寸
- 鼠标包裹状态
- 是否正在进行视角控制

## 性能优化建议

1. 鼠标包裹检测只在旋转模式下进行，不影响其他操作
2. 包裹计算非常轻量，每帧开销可忽略不计
3. 建议在移动设备上根据需要禁用此功能
