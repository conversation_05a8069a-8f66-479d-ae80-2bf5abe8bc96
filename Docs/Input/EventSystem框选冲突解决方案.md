# EventSystem与框选功能冲突解决方案

## 问题描述

当Unity场景运行时，会自动创建EventSystem组件，其上的InputSystemUIInputModule会导致框选功能失效。这是因为：

1. **InputSystemUIInputModule消费输入事件**：该模块会拦截鼠标输入事件，导致`Mouse.current.leftButton.wasPressedThisFrame`等检测失效
2. **UI优先级高于3D交互**：EventSystem的设计优先处理UI交互，会阻止3D场景中的选择操作
3. **输入事件冲突**：新的Input System与传统的Input Manager在事件处理上存在冲突

## 解决方案

### 1. EventSystemManager组件

创建了专门的`EventSystemManager`组件来管理EventSystem与框选功能的协调：

**核心功能：**
- 自动管理EventSystem和InputSystemUIInputModule的创建
- 在框选操作期间临时禁用UI输入模块
- 提供精确的UI检测功能
- 支持调试和状态监控

**关键方法：**
```csharp
// 开始选择时禁用UI输入模块
public void BeginSelection()

// 结束选择时恢复UI输入模块  
public void EndSelection()

// 检查鼠标是否在UI上
public bool IsPointerOverUI()
```

### 2. InputManager集成

修改了`InputManager`来使用`EventSystemManager`：

**主要改进：**
- 在`StartSelection()`时调用`eventSystemManager.BeginSelection()`
- 在`EndSelection()`时调用`eventSystemManager.EndSelection()`
- 使用`eventSystemManager.IsPointerOverUI()`进行UI检测
- 自动创建和管理EventSystemManager实例

### 3. 工作流程

```
1. 用户按下左键
   ↓
2. InputManager检测到输入
   ↓
3. 检查是否在UI上 (通过EventSystemManager)
   ↓
4. 如果不在UI上，开始选择操作
   ↓
5. EventSystemManager临时禁用InputSystemUIInputModule
   ↓
6. 执行框选逻辑 (单选或框选)
   ↓
7. 用户释放左键
   ↓
8. EventSystemManager重新启用InputSystemUIInputModule
   ↓
9. 选择操作完成
```

## 使用方法

### 1. 自动设置

在场景中添加`InputManager`组件，它会自动：
- 创建`EventSystemManager`
- 配置EventSystem和InputSystemUIInputModule
- 启用框选功能

### 2. 手动配置

如果需要手动控制，可以：

```csharp
// 获取EventSystemManager实例
EventSystemManager esm = EventSystemManager.Instance;

// 手动开始/结束选择
esm.BeginSelection();
// ... 执行选择逻辑 ...
esm.EndSelection();

// 检查UI状态
bool overUI = esm.IsPointerOverUI();
```

### 3. 配置选项

在`InputManager`中：
- `useEventSystemManager`: 是否使用EventSystemManager (默认true)

在`EventSystemManager`中：
- `autoManageEventSystem`: 是否自动管理EventSystem (默认true)
- `allowSelectionWhenUIActive`: 是否允许在UI激活时选择 (默认false)
- `showDebugInfo`: 是否显示调试信息 (默认false)

## 测试验证

### 1. EventSystemTest组件

提供了`EventSystemTest`组件用于测试：
- 自动创建测试对象和UI
- 提供快捷键控制
- 显示系统状态信息

**快捷键：**
- `F1`: 切换EventSystemManager
- `F2`: 切换InputModule
- `F3`: 打印系统状态

### 2. 测试场景

1. 运行场景
2. 尝试框选3D对象 - 应该正常工作
3. 点击UI按钮 - 应该正常响应
4. 在UI区域尝试框选 - 应该被阻止
5. 使用快捷键测试不同配置

## 技术细节

### 1. 输入事件处理

```csharp
// 传统方式 (可能失效)
if (Input.GetMouseButtonDown(0))

// 新Input System方式 (推荐)
if (Mouse.current.leftButton.wasPressedThisFrame)
```

### 2. UI检测改进

```csharp
// 基础检测
EventSystem.current.IsPointerOverGameObject()

// 改进检测 (通过EventSystemManager)
eventSystemManager.IsPointerOverUI()
```

### 3. 模块管理

```csharp
// 临时禁用UI输入模块
inputModule.enabled = false;

// 恢复UI输入模块
inputModule.enabled = true;
```

## 注意事项

1. **性能影响**：频繁的启用/禁用操作对性能影响很小
2. **UI响应**：在框选期间UI按钮不会响应，这是预期行为
3. **兼容性**：与现有的选择系统完全兼容
4. **扩展性**：可以轻松添加更多的输入模式和UI检测逻辑

## 故障排除

### 问题1：框选仍然不工作
- 检查EventSystemManager是否正确创建
- 确认`useEventSystemManager`设置为true
- 查看控制台是否有错误信息

### 问题2：UI不响应
- 检查InputSystemUIInputModule是否被意外禁用
- 确认EventSystemManager正确调用了EndSelection()
- 验证Canvas和GraphicRaycaster设置

### 问题3：调试信息
- 启用`showDebugInfo`查看详细状态
- 使用EventSystemTest组件进行测试
- 检查控制台日志输出

## 总结

这个解决方案通过智能管理EventSystem组件，成功解决了InputSystemUIInputModule与框选功能的冲突问题，同时保持了UI系统的正常功能。该方案具有良好的扩展性和兼容性，可以适应各种复杂的UI和3D交互场景。
