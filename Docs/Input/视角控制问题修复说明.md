# 视角控制问题修复说明

## 修复概述

本次修复解决了两个关键问题：
1. **智能旋转中心计算** - 鼠标中键旋转/缩放时以鼠标位置射线与基面的交点为中心
2. **UI焦点冲突修复** - 第一人称模式下WASD移动时避免UI Toolkit焦点移动

## 问题1: 智能旋转中心计算

### 问题描述
- 原先的鼠标中键旋转和缩放操作使用固定的旋转中心点
- 用户期望能够以鼠标指向的位置作为旋转/缩放中心，类似Blender的操作方式

### 解决方案

#### 1. 新增射线检测系统 (`InputManager.RaycastUtils.cs`)

**核心功能**:
- `SmartCalculateRotationCenter()`: 智能计算旋转中心点
- `RaycastToGridPlane()`: 检测GridPlane组件
- `RaycastToGroundPlane()`: 数学平面相交计算
- `DebugRaycast()`: 可视化调试射线检测

**检测优先级**:
1. **GridPlane组件** - 优先检测场景中的GridPlane
2. **物理基面** - 使用Physics.Raycast检测指定图层的物体
3. **数学平面** - 与Y=groundPlaneY的水平面相交
4. **世界原点** - 最后的回退选项

#### 2. 修改视角控制逻辑 (`InputManager.ViewportControl.cs`)

**关键改动**:
```csharp
// 旋转模式 - 传入鼠标位置
StartRotating(lastMousePosition);

// 缩放模式 - 传入鼠标位置  
StartSmoothZooming(lastMousePosition);

// 在开始操作时计算新的旋转中心
Vector3 newRotationCenter = SmartCalculateRotationCenter(mousePosition);
SetRotationCenter(newRotationCenter);
```

#### 3. 可配置参数

**新增设置**:
- `groundLayerMask`: 基面图层掩码 (默认: Default层)
- `groundPlaneY`: 默认基面Y坐标 (默认: 0)
- `usePhysicsRaycast`: 是否使用物理射线检测 (默认: true)
- `showDebugRaycast`: 是否显示调试信息 (默认: false)

## 问题2: UI焦点冲突修复

### 问题描述
- 在第一人称模式下按WASD键移动时，会触发UI Toolkit的焦点移动
- 导致UI界面上的元素意外获得焦点，影响用户体验

### 解决方案

#### 1. 第一人称模式输入隔离 (`InputManager.FirstPersonMode.cs`)

**进入第一人称模式时**:
```csharp
// 确保处于Player输入模式
if (isUIActive) EnablePlayerInput();

// 临时禁用UI ActionMap
if (uiActionMap != null) uiActionMap.Disable();

// 通知EventSystemManager阻止UI事件
if (eventSystemManager != null) eventSystemManager.SetFirstPersonMode(true);
```

**退出第一人称模式时**:
```csharp
// 通知EventSystemManager恢复UI事件
if (eventSystemManager != null) eventSystemManager.SetFirstPersonMode(false);

// 恢复UI输入状态
if (isUIActive && uiActionMap != null) uiActionMap.Enable();
```

#### 2. EventSystem管理增强 (`EventSystemManager.cs`)

**新增功能**:
- `SetFirstPersonMode(bool enabled)`: 设置第一人称模式状态
- `IsFirstPersonMode()`: 获取第一人称模式状态
- 在第一人称模式下，`IsPointerOverUI()`始终返回false
- 临时禁用`InputSystemUIInputModule`以阻止UI事件处理

#### 3. 输入模式管理改进 (`InputManager.InputMode.cs`)

**智能模式切换**:
```csharp
public void EnableUIInput()
{
    // 如果处于第一人称模式，先退出
    if (isFirstPersonMode) ForceExitFirstPersonMode();
    
    // 然后切换到UI模式
    // ...
}
```

## 新增文件

### 1. `InputManager.RaycastUtils.cs`
- 射线检测和基面交点计算的完整实现
- 支持多种检测方法和调试功能

### 2. `ViewportControlTest.cs`
- 专门用于测试视角控制功能的测试脚本
- 实时显示射线检测信息和状态
- 提供调试控制界面

## 修改的文件

### 1. `InputManager.ViewportControl.cs`
- 修改`StartRotating()`和`StartSmoothZooming()`方法
- 添加鼠标位置参数，实现动态旋转中心计算

### 2. `InputManager.FirstPersonMode.cs`
- 增强进入/退出第一人称模式的逻辑
- 添加UI输入隔离机制

### 3. `EventSystemManager.cs`
- 添加第一人称模式支持
- 增强UI事件阻止机制

### 4. `InputManager.UI.cs`
- 更新状态显示，包含第一人称模式信息
- 添加新的操作提示

## 使用方法

### 基本操作
1. **智能旋转**: 鼠标中键拖动，自动以鼠标位置为旋转中心
2. **智能缩放**: Ctrl+鼠标中键拖动，自动以鼠标位置为缩放中心
3. **第一人称漫游**: 按住鼠标右键，WASD移动不会影响UI焦点

### 调试功能
1. **启用射线调试**: `inputManager.SetDebugRaycast(true)`
2. **查看检测信息**: 使用`ViewportControlTest`脚本
3. **Scene视图调试**: 射线和交点会在Scene视图中可视化

### 参数调整
```csharp
// 设置基面Y坐标
inputManager.SetGroundPlaneY(0f);

// 设置基面图层
inputManager.SetGroundLayerMask(LayerMask.GetMask("Ground"));

// 启用/禁用物理检测
inputManager.SetUsePhysicsRaycast(true);
```

## 测试验证

### 测试检查点

#### 智能旋转中心
- ✅ 鼠标指向GridPlane时，以GridPlane交点为旋转中心
- ✅ 鼠标指向其他物体时，以物体表面为旋转中心
- ✅ 鼠标指向空白区域时，以数学平面交点为旋转中心
- ✅ 射线无交点时，使用世界原点作为回退

#### UI焦点冲突
- ✅ 第一人称模式下WASD移动不触发UI焦点变化
- ✅ 第一人称模式下UI元素不会意外获得焦点
- ✅ 退出第一人称模式后UI功能正常恢复
- ✅ Tab键切换UI模式时自动退出第一人称模式

### 性能考虑
- 射线检测仅在开始旋转/缩放时执行一次，不影响运行时性能
- UI输入隔离通过ActionMap禁用实现，开销极小
- 调试功能可选择性启用，不影响发布版本性能

## 兼容性

### 向后兼容
- 所有现有功能保持不变
- 新功能为可选增强，不影响原有操作
- 参数设置提供合理默认值

### 扩展性
- 射线检测系统支持自定义检测方法
- EventSystem管理支持更多UI框架
- 调试系统支持自定义可视化

## 总结

本次修复显著提升了视角控制的用户体验：

1. **更直观的操作** - 旋转和缩放以鼠标指向位置为中心
2. **更流畅的第一人称体验** - 完全避免UI焦点冲突
3. **更强的调试能力** - 完整的射线检测可视化和状态监控
4. **更好的兼容性** - 与现有系统无缝集成，支持多种UI框架

这些改进使得3D场景导航更加符合用户的直觉操作习惯，同时保持了系统的稳定性和扩展性。
