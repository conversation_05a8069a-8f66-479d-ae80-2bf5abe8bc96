# UI布局和事件系统简化重构测试指南

## 概述

本文档提供了UI布局和场景交互事件系统简化重构后的详细测试指南，确保新的简化架构正确工作。

## 重构内容总结

### 1. UI布局简化
- **移除复杂布局**: 删除了#main-content层级结构和center-panel元素
- **浮动面板**: 左右面板改为绝对定位，浮动在3D场景之上
- **透明背景**: 3D场景直接作为背景显示，无遮罩层

### 2. 事件系统简化
- **二元判断**: 将事件优先级简化为UI事件 vs 场景事件
- **移除复杂检测**: 删除了center-panel相关的复杂检测逻辑
- **性能优化**: 简化的检测机制提供更好的性能

### 3. 简化的事件优先级层次
1. **UI事件** (高优先级): 鼠标在任何UI元素上时阻断场景事件
2. **场景事件** (低优先级): 鼠标不在UI元素上时允许场景交互

## 测试准备

### 1. 组件设置
确保场景中包含以下组件：
- InputManager (启用useEventPriorityManager)
- UIManager (启用enableUIEventBlocking)
- EventSystemManager (简化版)
- InputEventPriorityManager (自动创建或手动添加)
- EventSystemTester (可选，用于调试)

### 2. 测试环境
- 确保UI界面正常显示，左右面板浮动在场景之上
- 确保3D场景直接作为背景显示，无遮罩层
- 确保左右浮动面板包含ScrollView等可滚动元素

## 详细测试场景

### 测试场景1: UI滚轮事件阻断
**目标**: 验证在UI元素上的滚轮操作不会触发场景缩放

**测试步骤**:
1. 将鼠标移动到右侧面板的ScrollView上
2. 滚动鼠标滚轮
3. 观察场景相机是否缩放

**预期结果**:
- ScrollView内容正常滚动
- 场景相机不应该缩放
- EventSystemTester显示"阻断场景事件: 是"

**测试要点**:
- 测试不同的UI元素：ScrollView、DropdownField、Slider等
- 测试左侧面板和右侧面板
- 测试顶部工具栏和底部状态栏

### 测试场景2: 场景区域交互
**目标**: 验证在非UI区域的滚轮操作正常触发场景缩放

**测试步骤**:
1. 将鼠标移动到3D场景区域（非浮动面板区域）
2. 滚动鼠标滚轮
3. 观察场景相机缩放效果

**预期结果**:
- 场景相机正常缩放
- EventSystemTester显示"事件优先级: 场景事件"
- EventSystemTester显示"阻断场景事件: 否"

### 测试场景3: 视角控制操作
**目标**: 验证中键拖拽等视角控制在不同区域的行为

**测试步骤**:
1. 在浮动面板UI元素上按住鼠标中键拖拽
2. 在3D场景区域按住鼠标中键拖拽
3. 观察相机旋转/平移行为

**预期结果**:
- 浮动面板UI元素上：不应该触发相机操作
- 3D场景区域：正常触发相机旋转/平移

### 测试场景4: 选择功能
**目标**: 验证框选功能在不同区域的行为

**测试步骤**:
1. 在浮动面板UI元素上尝试左键拖拽框选
2. 在3D场景区域进行左键拖拽框选
3. 观察选择框的显示和对象选择

**预期结果**:
- 浮动面板UI元素上：不应该显示选择框
- 3D场景区域：正常显示选择框并选择对象

### 测试场景5: 第一人称模式
**目标**: 验证第一人称模式下的输入响应

**测试步骤**:
1. 按右键进入第一人称模式
2. 在UI元素上移动鼠标
3. 使用WASD移动
4. 观察相机响应

**预期结果**:
- UI焦点不应该干扰第一人称控制
- WASD移动正常工作
- 鼠标视角控制正常工作

## 调试工具使用

### EventSystemTester使用
1. 在场景中添加EventSystemTester组件
2. 启用showDebugGUI查看实时状态
3. 启用logEventDetails查看详细日志
4. 使用GUI按钮控制调试选项

### 调试信息解读
- **事件优先级**: 显示当前鼠标位置的事件优先级
- **阻断场景事件**: 是否阻断场景交互事件
- **统计数据**: 各种事件类型的检测次数

## 常见问题排查

### 问题1: UI滚轮事件仍然触发场景缩放
**可能原因**:
- InputEventPriorityManager未正确初始化
- UI事件阻断未正确设置
- 事件优先级检查被跳过

**排查步骤**:
1. 检查InputManager的useEventPriorityManager是否启用
2. 检查UIManager的enableUIEventBlocking是否启用
3. 查看控制台日志确认组件初始化状态

### 问题2: 中央面板检测不准确
**可能原因**:
- center-panel元素未正确找到
- UI Toolkit坐标转换失败
- 回退到屏幕坐标估算

**排查步骤**:
1. 检查MainLayout.uxml中center-panel元素是否存在
2. 启用EventSystemManager的showDebugInfo
3. 查看控制台中的检测方法日志

### 问题3: 第一人称模式受到影响
**可能原因**:
- 事件优先级检查干扰了第一人称输入
- UI焦点管理问题

**排查步骤**:
1. 检查第一人称模式下的事件优先级判断
2. 确认EventSystemManager的第一人称模式处理
3. 验证InputManager的第一人称模式检查

## 性能考虑

### 优化建议
1. 避免每帧进行复杂的UI检测
2. 使用缓存减少重复计算
3. 只在有输入事件时进行优先级检查

### 监控指标
- 事件检测频率
- UI元素查找性能
- 内存使用情况

## 扩展测试

### 压力测试
1. 快速移动鼠标测试响应性
2. 同时进行多种输入操作
3. 长时间运行稳定性测试

### 兼容性测试
1. 不同分辨率下的UI检测
2. 不同UI布局的适应性
3. 动态UI元素的处理

## 总结

通过以上测试场景，可以全面验证事件系统重构的效果。重点关注：
1. UI事件正确阻断场景交互
2. 中央面板场景交互正常工作
3. 现有功能不受影响
4. 性能表现良好

如果发现问题，请参考排查步骤进行调试，或查看相关组件的调试日志获取更多信息。
