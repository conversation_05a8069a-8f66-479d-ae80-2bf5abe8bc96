# USS样式生成功能使用指南

## 概述

IconManagerEditor现在支持自动生成USS样式文件，可以根据项目中的图标文件自动创建分类的样式定义，并集成到现有的UI系统中。

## 功能特性

### 1. 自动分类生成
- **GeneratedIcons-Common.uss**: 通用图标样式
- **GeneratedIcons-MenuBar.uss**: 菜单栏图标样式  
- **GeneratedIcons-Toolbar.uss**: 工具栏图标样式

### 2. Unity UI Toolkit兼容
- 使用正确的USS语法（非CSS）
- 支持Unity的project://database/路径格式
- 遵循kebab-case命名规范

### 3. 自动集成
- 通过@import语句集成到现有USS文件
- 自动清理冗余的图标样式定义
- 保持样式加载顺序正确

## 使用方法

### 1. 打开图标管理器
```
菜单: BlastingDesign > UI > Icon Manager
```

### 2. 生成USS样式
1. 点击"生成CSS样式"按钮
2. 等待生成进度完成
3. 查看生成结果对话框

### 3. 验证生成结果
生成的文件位置：
```
Assets/Resources/UI/Generated/
├── GeneratedIcons-Common.uss
├── GeneratedIcons-MenuBar.uss
└── GeneratedIcons-Toolbar.uss
```

## 生成的样式格式

### 工具栏图标样式示例
```css
/* 工具栏图标基础样式 */
.tool-icon {
    width: 16px;
    height: 16px;
    background-color: transparent;
    margin-bottom: 2px;
    flex-shrink: 0;
    -unity-background-scale-mode: scale-to-fit;
}

.tool-button.selected .tool-icon {
    -unity-background-image-tint-color: rgb(255, 255, 255);
}

.tool-button:hover .tool-icon {
    -unity-background-image-tint-color: rgb(220, 220, 220);
}

/* 具体图标样式 */
.select-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/Toolbar/select-icon.png');
}
```

### 菜单栏图标样式示例
```css
/* 菜单栏图标基础样式 */
.menu-item-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    background-color: transparent;
    flex-shrink: 0;
    -unity-background-scale-mode: scale-to-fit;
}

/* 具体图标样式 */
.file-new-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/MenuBar/file-new.png');
}
```

## 自动集成说明

### 1. @import集成
生成功能会自动在相关USS文件中添加@import语句：

**Toolbar.uss**:
```css
@import url("../Generated/GeneratedIcons-Toolbar.uss");
```

**MenuBar.uss**:
```css
@import url("../Generated/GeneratedIcons-MenuBar.uss");
```

**Common.uss**:
```css
@import url("project://database/Assets/Resources/UI/Generated/GeneratedIcons-Common.uss");
```

### 2. 冗余样式清理
- 自动删除原有USS文件中的重复图标样式定义
- 保留基础样式类（如.tool-icon, .menu-item-icon）
- 添加注释说明样式来源

## 图标命名规范

### 1. 文件命名
- 使用kebab-case格式：`select-icon.png`
- 避免特殊字符和空格
- 建议使用描述性名称

### 2. 图标文件要求
- **背景透明**: 图标PNG文件必须有透明背景
- **颜色建议**: 图标内容建议使用白色或浅色（便于着色效果）
- **尺寸规范**: 建议使用16x16、24x24或32x32像素
- **格式要求**: 支持PNG格式，建议使用32位PNG（带Alpha通道）

### 3. CSS类名生成规则
- 文件名转换为小写
- 下划线和空格转换为连字符
- 自动添加-icon后缀（如果不存在）

示例：
```
select-icon.png → .select-icon
move_tool.png → .move-tool-icon
File New.png → .file-new-icon
```

## 注意事项

### 1. 文件覆盖
- 每次生成会覆盖现有的生成文件
- 手动修改的生成文件会丢失
- 建议通过修改源图标文件来更新样式

### 2. 路径依赖
- @import路径基于相对路径计算
- 移动USS文件可能需要手动调整@import路径
- 确保图标文件路径正确

### 3. 样式优先级
- 生成的样式通过@import在文件开头加载
- 后续定义的样式可以覆盖生成的样式
- 建议避免样式冲突

## 故障排除

### 1. 生成失败
- 检查图标文件是否存在
- 确保有写入权限
- 查看Unity Console错误信息

### 2. 样式不生效
- 检查@import路径是否正确
- 确认图标文件路径有效
- 验证CSS类名拼写

### 3. 重新生成
- 可以多次运行生成功能
- 会自动覆盖现有文件
- 不会重复添加@import语句

## 扩展功能

### 1. 自定义样式
可以在生成后手动添加自定义样式：
```css
/* 自定义图标变体 */
.select-icon.large {
    width: 24px;
    height: 24px;
}

.select-icon.disabled {
    opacity: 0.5;
}
```

### 2. 主题支持
可以为不同主题创建样式变体：
```css
/* 深色主题 */
.dark-theme .tool-icon {
    background-color: rgb(160, 160, 160);
}

/* 高对比度主题 */
.high-contrast .tool-icon {
    background-color: rgb(255, 255, 255);
}
```
