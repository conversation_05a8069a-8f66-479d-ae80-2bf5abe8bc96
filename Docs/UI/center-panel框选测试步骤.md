# Center-Panel 框选功能测试步骤

## 修复内容总结

我已经修复了 `NullReferenceException` 错误，并改进了center-panel检测功能：

### 主要修复：

1. **添加了空值检查**：
   - 检查 `centerPanel` 是否为 null
   - 检查 `centerPanel.panel` 是否为 null
   - 添加了 try-catch 异常处理

2. **实现了备用检测方案**：
   - 当UI Toolkit检测失败时，使用屏幕区域估算
   - 基于典型的编辑器布局（左右侧边栏300px，顶部工具栏32px）

3. **改进了初始化流程**：
   - 在 `Awake` 中尝试初始化
   - 在 `Start` 中重试初始化（如果失败）
   - 添加了详细的调试信息

## 测试步骤

### 1. 在Unity编辑器中设置

1. **打开场景**：
   - 打开 `Assets/Scenes/Main.unity`

2. **添加调试脚本**：
   - 在场景中找到任意GameObject（比如Main）
   - 添加 `UISelectionTester` 组件
   - 确保 `Enable Debug Info` 被勾选

3. **检查EventSystemManager设置**：
   - 找到场景中的EventSystemManager组件
   - 确保以下选项被勾选：
     - `Auto Manage Event System` ✓
     - `Enable Center Panel Selection` ✓
     - `Show Debug Info` ✓（可选，用于调试）

### 2. 运行测试

1. **启动Play模式**

2. **观察控制台输出**：
   - 应该看到 "UISelectionTester 已启动" 消息
   - 如果有错误，会显示详细的错误信息

3. **测试不同区域的点击**：

   **在center-panel区域（中间透明区域）点击**：
   - ✅ 应该显示：`在center-panel区域内，应该允许选择操作`
   - ✅ 框选功能应该正常工作

   **在左侧面板点击**：
   - ❌ 应该显示：`在其他UI区域，应该阻止选择操作`
   - ❌ 框选功能应该被阻止

   **在右侧面板点击**：
   - ❌ 应该显示：`在其他UI区域，应该阻止选择操作`
   - ❌ 框选功能应该被阻止

   **在顶部工具栏点击**：
   - ❌ 应该显示：`在其他UI区域，应该阻止选择操作`
   - ❌ 框选功能应该被阻止

### 3. 调试信息

1. **屏幕显示**：
   - 左下角会显示实时的鼠标位置和UI检测状态
   - 绿色表示允许选择，红色表示阻止选择

2. **控制台输出**：
   - 每次左键点击都会输出详细的检测结果
   - 如果有错误，会显示具体的错误信息

3. **切换调试信息**：
   - 按 `F1` 键可以切换调试信息的显示

## 故障排除

### 如果仍然出现 NullReferenceException：

1. **检查控制台错误信息**：
   - 查看具体是哪一行出错
   - 查看错误的详细堆栈信息

2. **检查UI Toolkit设置**：
   - 确认场景中有UIDocument组件
   - 确认MainLayout.uxml文件正确加载
   - 确认center-panel元素存在且name正确

3. **使用备用方案**：
   - 如果UI Toolkit检测失败，系统会自动使用屏幕区域估算
   - 这应该能在大多数情况下正常工作

### 如果框选仍然不工作：

1. **检查EventSystemManager设置**：
   - 确保 `enableCenterPanelSelection` 为 true

2. **检查InputManager设置**：
   - 确保 `useEventSystemManager` 为 true
   - 确保 `enableSelection` 为 true

3. **查看调试输出**：
   - 观察 `IsPointerOverUI()` 的返回值
   - 观察 `IsPointerOverCenterPanel()` 的返回值

## 预期结果

修复后，您应该能够：

- ✅ 在center-panel区域正常进行框选操作
- ✅ 在center-panel区域正常进行单选操作
- ✅ 在其他UI区域时，选择操作被正确阻止
- ✅ 不再出现 NullReferenceException 错误
- ✅ 有详细的调试信息帮助排查问题

如果测试过程中遇到任何问题，请查看控制台的详细错误信息，这将帮助进一步诊断问题。
