# Utility USS 使用文档

## 项目概述

Utility USS 是一个为 Unity UI Toolkit 设计的工具类样式库，灵感来源于 Tailwind CSS。它提供了大量预定义的 USS（Unity Style Sheets）类，让开发者能够快速构建美观、一致的 Unity UI 界面。

### 主要特点

- 🎨 **完整的颜色系统** - 包含 20+ 种颜色，每种颜色 11 个色阶
- 🔧 **丰富的工具类** - 涵盖布局、间距、颜色、变换等各个方面
- 🚀 **开箱即用** - 无需配置，直接引入即可使用
- 📱 **Unity 优化** - 专为 Unity UI Toolkit 设计和优化
- 🎯 **类 Tailwind 语法** - 熟悉的命名规范，易于上手

## 安装和使用

### 1. 下载文件
从 [GitHub 仓库](https://github.com/robbyklein/utility-uss) 下载 `dist/Utility.uss` 文件到你的 Unity 项目中。

### 2. 引入样式表
在你的 UXML 文件中引入样式表：

```xml
<ui:UXML xmlns:ui="UnityEngine.UIElements">
    <Style src="project://database/Assets/Utility.uss" />
    
    <!-- 你的 UI 内容 -->
    <ui:VisualElement class="w-full h-full bg-slate-300 items-center justify-center">
        <ui:Label text="Hello World!" class="text-2xl text-slate-900" />
    </ui:VisualElement>
</ui:UXML>
```

## 颜色系统

Utility USS 提供了完整的颜色调色板，每种颜色都有从 50（最浅）到 950（最深）的 11 个色阶。

### 可用颜色

| 颜色名称 | 描述 | 示例类 |
|---------|------|--------|
| `slate` | 冷灰色 | `bg-slate-500`, `text-slate-700` |
| `gray` | 中性灰色 | `bg-gray-100`, `border-gray-300` |
| `zinc` | 现代灰色 | `bg-zinc-800`, `text-zinc-200` |
| `neutral` | 纯灰色 | `bg-neutral-50`, `text-neutral-900` |
| `stone` | 暖灰色 | `bg-stone-200`, `border-stone-400` |
| `red` | 红色 | `bg-red-500`, `text-red-600` |
| `orange` | 橙色 | `bg-orange-400`, `border-orange-500` |
| `amber` | 琥珀色 | `bg-amber-300`, `text-amber-700` |
| `yellow` | 黄色 | `bg-yellow-200`, `text-yellow-800` |
| `lime` | 青柠色 | `bg-lime-400`, `border-lime-500` |
| `green` | 绿色 | `bg-green-500`, `text-green-700` |
| `emerald` | 翡翠绿 | `bg-emerald-400`, `text-emerald-600` |
| `teal` | 青色 | `bg-teal-500`, `border-teal-600` |
| `cyan` | 青蓝色 | `bg-cyan-300`, `text-cyan-700` |
| `sky` | 天空蓝 | `bg-sky-400`, `text-sky-600` |
| `blue` | 蓝色 | `bg-blue-500`, `text-blue-700` |
| `indigo` | 靛蓝色 | `bg-indigo-600`, `text-indigo-200` |
| `violet` | 紫罗兰 | `bg-violet-400`, `text-violet-700` |
| `purple` | 紫色 | `bg-purple-500`, `text-purple-200` |
| `fuchsia` | 紫红色 | `bg-fuchsia-400`, `text-fuchsia-700` |
| `pink` | 粉色 | `bg-pink-300`, `text-pink-600` |
| `rose` | 玫瑰色 | `bg-rose-400`, `text-rose-700` |

### 特殊颜色
- `bg-white` - 纯白色背景
- `bg-black` - 纯黑色背景

## 主要功能模块

### 1. 布局和对齐

#### 内容对齐 (align-content)
```css
.content-center     /* align-content: center */
.content-start      /* align-content: flex-start */
.content-end        /* align-content: flex-end */
.content-stretch    /* align-content: stretch */
.content-auto       /* align-content: auto */
```

#### 项目对齐 (align-items)
```css
.items-start        /* align-items: flex-start */
.items-end          /* align-items: flex-end */
.items-center       /* align-items: center */
.items-auto         /* align-items: auto */
.items-stretch      /* align-items: stretch */
```

#### 自身对齐 (align-self)
```css
.self-auto          /* align-self: auto */
.self-start         /* align-self: flex-start */
.self-end           /* align-self: flex-end */
.self-center        /* align-self: center */
.self-stretch       /* align-self: stretch */
```

### 2. 背景样式

#### 背景颜色
使用 `bg-{color}-{shade}` 格式：
```css
.bg-slate-50        /* 最浅的 slate 色 */
.bg-blue-500        /* 中等的蓝色 */
.bg-red-900         /* 最深的红色 */
```

#### 背景位置
```css
.bg-left-top        /* background-position: left top */
.bg-center          /* background-position: center */
.bg-right-bottom    /* background-position: right bottom */
.bg-x-center        /* background-position-x: center */
.bg-y-top           /* background-position-y: top */
```

#### 背景重复
```css
.bg-repeat          /* background-repeat: repeat */
.bg-no-repeat       /* background-repeat: no-repeat */
.bg-repeat-x        /* background-repeat: repeat-x */
.bg-repeat-y        /* background-repeat: repeat-y */
```

#### 背景大小
```css
.bg-auto            /* background-size: auto */
.bg-cover           /* background-size: cover */
.bg-contain         /* background-size: contain */
```

### 3. 边框样式

#### 边框颜色
使用 `border-{color}-{shade}` 格式：
```css
.border-gray-300    /* 浅灰色边框 */
.border-blue-500    /* 蓝色边框 */
.border-red-600     /* 深红色边框 */
```

#### 方向性边框颜色
```css
.border-t-red-500   /* 上边框红色 */
.border-r-blue-400  /* 右边框蓝色 */
.border-b-green-300 /* 下边框绿色 */
.border-l-gray-200  /* 左边框灰色 */
.border-x-slate-400 /* 左右边框 slate 色 */
.border-y-zinc-300  /* 上下边框 zinc 色 */
```

### 4. 变换 (Transform)

#### 平移变换
使用 `translate-{x}-{y}` 格式，支持像素值和百分比：

```css
.translate-4-8       /* translate: 16px 32px */
.translate-12-0      /* translate: 48px 0px */
.translate-one-half-one-quarter  /* translate: 50% 25% */
```

支持的数值：
- 像素值：`0, px, 1-96` (对应 0px, 1px, 4px-384px)
- 百分比：`one-half` (50%), `one-third` (33.33%), `two-thirds` (66.67%), `one-quarter` (25%), `three-quarters` (75%), `full` (100%)

## 实际使用示例

### 示例 1：卡片布局
```xml
<ui:VisualElement class="bg-white border-gray-200 items-center">
    <ui:Label text="标题" class="text-xl text-gray-900" />
    <ui:Label text="描述文本" class="text-gray-600" />
    <ui:Button text="按钮" class="bg-blue-500 text-white" />
</ui:VisualElement>
```

### 示例 2：导航栏
```xml
<ui:VisualElement class="bg-slate-800 items-center">
    <ui:Label text="应用名称" class="text-white text-lg" />
    <ui:VisualElement class="items-center">
        <ui:Button text="首页" class="text-slate-300" />
        <ui:Button text="设置" class="text-slate-300" />
    </ui:VisualElement>
</ui:VisualElement>
```

### 示例 3：状态指示器
```xml
<ui:VisualElement class="items-center">
    <ui:VisualElement class="bg-green-100 border-green-300">
        <ui:Label text="成功" class="text-green-800" />
    </ui:VisualElement>
    <ui:VisualElement class="bg-red-100 border-red-300">
        <ui:Label text="错误" class="text-red-800" />
    </ui:VisualElement>
</ui:VisualElement>
```

## 与 Tailwind CSS 的差异

1. **不支持半数值** - 如 `mt-0.5`, `px-3.5` 等
2. **分数转换为单词** - `h-1/3` 变为 `h-one-third`
3. **字体样式合并** - `font-bold` 和 `font-italic` 合并为 `font-bold-italic`
4. **Unity 特定优化** - 针对 Unity UI Toolkit 的特性进行了优化

## 最佳实践

### 1. 颜色使用建议
- 使用 50-100 作为背景色
- 使用 600-900 作为文本色
- 使用 300-500 作为边框色
- 保持颜色的一致性和对比度

### 2. 布局建议
- 优先使用 flexbox 相关的对齐类
- 合理使用间距和内边距
- 注意响应式设计原则

### 3. 性能优化
- 避免过度嵌套样式类
- 合理使用颜色变量
- 定期清理未使用的样式

## 开发和构建

如果你想要自定义或扩展这个工具库：

```bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 构建生产版本
npm run build
```

## 许可证

MIT License - 详见项目的 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

---

*注意：本项目与 Tailwind CSS 官方无关，仅是受其启发的 Unity UI Toolkit 工具库。*
