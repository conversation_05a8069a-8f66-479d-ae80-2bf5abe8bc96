# Center-Panel 框选功能修复说明

## 问题描述

之前的框选功能被 `IsPointerOverUI()` 方法阻止，该方法会检测到鼠标在任何UI元素上（包括透明的 `center-panel`），导致无法在中间区域进行框选操作。

## 解决方案

### 1. 修改 EventSystemManager.cs

#### 新增功能：
- **enableCenterPanelSelection**: 新增配置选项，控制是否允许在center-panel区域进行选择
- **IsPointerOverCenterPanel()**: 新方法，专门检测鼠标是否在center-panel区域内
- **智能UI检测**: 修改 `IsPointerOverUI()` 方法，当鼠标在center-panel区域内时返回false，允许选择操作

#### 核心逻辑：
```csharp
public bool IsPointerOverUI()
{
    // 检查是否在UI上
    bool isOverUI = eventSystem.IsPointerOverGameObject();
    
    // 如果启用了center-panel选择功能，检查是否在center-panel区域内
    if (isOverUI && enableCenterPanelSelection && IsPointerOverCenterPanel())
    {
        return false; // 在center-panel区域内，允许选择操作
    }

    return isOverUI;
}
```

### 2. UI Toolkit 集成

#### 新增组件：
- **uiDocument**: 引用UIDocument组件
- **centerPanel**: 引用center-panel VisualElement

#### 坐标转换：
使用 `RuntimePanelUtils.ScreenToPanel()` 将屏幕坐标转换为UI坐标，然后检查是否在center-panel的边界内。

### 3. 调试功能

#### 新增调试脚本：
- **UISelectionTester.cs**: 专门用于测试UI选择功能的调试脚本
- 实时显示鼠标位置、UI检测状态、center-panel检测状态
- 支持F1键切换调试信息显示

#### 调试信息：
- 鼠标位置
- 是否在UI上
- 是否在center-panel区域内
- 选择操作是否应该被允许

## 使用方法

### 1. 启用功能
在EventSystemManager组件中：
- 确保 `enableCenterPanelSelection` 为 true
- 可选择启用 `showDebugInfo` 查看详细状态

### 2. 测试功能
1. 添加 `UISelectionTester` 组件到场景中的任意GameObject
2. 运行场景
3. 按F1键切换调试信息显示
4. 在不同区域点击鼠标左键，观察控制台输出

### 3. 预期行为
- **在center-panel区域**: 允许框选和单选操作
- **在左侧面板**: 阻止选择操作（UI交互优先）
- **在右侧面板**: 阻止选择操作（UI交互优先）
- **在顶部工具栏**: 阻止选择操作（UI交互优先）

## 技术细节

### UI Toolkit 坐标系统
- 使用 `RuntimePanelUtils.ScreenToPanel()` 进行坐标转换
- 使用 `VisualElement.worldBound` 获取元素的世界边界
- 使用 `Rect.Contains()` 检查点是否在矩形内

### 性能考虑
- 只在需要时进行center-panel检测
- 缓存UI元素引用，避免重复查找
- 调试信息可以完全禁用

### 兼容性
- 保持与现有选择系统的完全兼容
- 不影响其他UI交互功能
- 支持运行时动态切换

## 故障排除

### 常见问题：

1. **center-panel未找到**
   - 检查UIDocument组件是否存在
   - 确认UXML文件中center-panel元素的name属性正确

2. **坐标转换失败**
   - 确保UI Toolkit正确初始化
   - 检查panel是否为null

3. **选择仍然被阻止**
   - 确认 `enableCenterPanelSelection` 为true
   - 启用调试信息查看详细状态
   - 检查center-panel的CSS样式是否正确

### 调试步骤：
1. 启用 `showDebugInfo` 
2. 添加 `UISelectionTester` 组件
3. 观察控制台输出和屏幕调试信息
4. 验证鼠标位置和UI检测状态

## 总结

通过这个修改，框选功能现在可以在center-panel区域正常工作，同时保持在其他UI区域的正确行为。这提供了更好的用户体验，让用户可以在3D场景视图区域自由进行选择操作，而不会被透明的UI元素干扰。
