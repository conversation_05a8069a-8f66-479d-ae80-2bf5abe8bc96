# DaisyUI到Unity UI Toolkit迁移方案

## 📋 项目概述

本文档详细描述了将DaisyUI设计系统迁移到Unity UI Toolkit的完整方案，旨在为3D爆破设计应用构建现代化、高效的UI组件库。

### 目标
- 将DaisyUI的设计理念和组件架构迁移到Unity UI Toolkit
- 提供语义化的组件类名和链式调用API
- 建立完整的主题系统和样式变量
- 保持Unity UI Toolkit的原生性能优势

## 🏗️ 架构设计

### 1. 文件结构

```
Assets/
├── Scripts/
│   └── UI/
│       ├── Daisy/                    # DaisyUI组件库主目录
│       │   ├── Core/                 # 核心系统
│       │   │   ├── DaisyComponent.cs # 组件基类
│       │   │   ├── DaisyTheme.cs     # 主题系统
│       │   │   └── DaisyUtilities.cs # 工具类
│       │   ├── Components/           # 组件实现
│       │   │   ├── Actions/          # 按钮、下拉菜单等
│       │   │   ├── DataDisplay/      # 卡片、徽章等
│       │   │   ├── Navigation/       # 导航、标签页等
│       │   │   ├── DataInput/        # 输入框、选择器等
│       │   │   └── Layout/           # 布局组件
│       │   └── Builders/             # 组件构建器
│       └── Extensions/               # 扩展方法
├── Resources/
│   └── DaisyUI/                      # DaisyUI资源
│       ├── Themes/                   # 主题资源
│       │   ├── light.uss
│       │   ├── dark.uss
│       │   └── themes.uss
│       ├── Components/               # 组件样式
│       │   ├── Actions/
│       │   ├── DataDisplay/
│       │   ├── Navigation/
│       │   ├── DataInput/
│       │   └── Layout/
│       ├── Utilities/                # 工具类样式
│       │   ├── colors.uss
│       │   ├── spacing.uss
│       │   ├── typography.uss
│       │   └── effects.uss
│       └── Templates/                # UXML模板
└── UI Toolkit/
    └── DaisyUI/                      # DaisyUI UXML模板
```

### 2. 核心组件基类

```csharp
// DaisyComponent.cs
using UnityEngine;
using UnityEngine.UIElements;

namespace BlastingDesign.UI.Daisy.Core
{
    public abstract class DaisyComponent : VisualElement
    {
        // 组件基础属性
        public string ComponentType { get; protected set; }
        public string[] ModifierClasses { get; protected set; }
        public DaisyTheme Theme { get; protected set; }
        
        // 组件状态
        public bool IsDisabled { get; set; }
        public string Size { get; set; } = "md";
        public string Variant { get; set; } = "default";
        
        // 构造函数
        protected DaisyComponent(string componentType)
        {
            ComponentType = componentType;
            AddToClassList($"daisy-{componentType}");
            Theme = DaisyTheme.Current;
            Initialize();
        }
        
        // 初始化组件
        protected virtual void Initialize()
        {
            ApplyDefaultStyles();
            SetupEventHandlers();
        }
        
        // 应用默认样式
        protected virtual void ApplyDefaultStyles()
        {
            AddToClassList($"daisy-{ComponentType}");
            AddToClassList($"daisy-{ComponentType}-{Size}");
            AddToClassList($"daisy-{ComponentType}-{Variant}");
        }
        
        // 设置修饰符
        public virtual void SetModifier(string modifier, bool enabled = true)
        {
            string className = $"daisy-{ComponentType}-{modifier}";
            if (enabled)
                AddToClassList(className);
            else
                RemoveFromClassList(className);
        }
        
        // 链式调用支持
        public DaisyComponent WithSize(string size)
        {
            RemoveFromClassList($"daisy-{ComponentType}-{Size}");
            Size = size;
            AddToClassList($"daisy-{ComponentType}-{Size}");
            return this;
        }
        
        public DaisyComponent WithVariant(string variant)
        {
            RemoveFromClassList($"daisy-{ComponentType}-{Variant}");
            Variant = variant;
            AddToClassList($"daisy-{ComponentType}-{Variant}");
            return this;
        }
        
        protected virtual void SetupEventHandlers() { }
    }
}
```

### 3. 主题系统

```csharp
// DaisyTheme.cs
using UnityEngine;
using UnityEngine.UIElements;

namespace BlastingDesign.UI.Daisy.Core
{
    [CreateAssetMenu(fileName = "DaisyTheme", menuName = "DaisyUI/Theme")]
    public class DaisyTheme : ScriptableObject
    {
        public static DaisyTheme Current { get; private set; }
        
        [Header("Theme Configuration")]
        public string themeName = "light";
        public bool isDark = false;
        
        [Header("Color Palette")]
        public Color primary = Color.blue;
        public Color secondary = Color.gray;
        public Color accent = Color.cyan;
        public Color neutral = Color.white;
        public Color baseColor = Color.white;
        public Color info = Color.cyan;
        public Color success = Color.green;
        public Color warning = Color.yellow;
        public Color error = Color.red;
        
        [Header("Typography")]
        public int baseFontSize = 14;
        public FontDefinition primaryFont;
        public FontDefinition secondaryFont;
        
        [Header("Spacing")]
        public float baseSpacing = 4f;
        public float[] spacingScale = { 2, 4, 8, 12, 16, 24, 32, 48, 64 };
        
        [Header("Border Radius")]
        public float borderRadius = 4f;
        public float borderRadiusLg = 8f;
        public float borderRadiusXl = 12f;
        
        public void Apply(VisualElement root)
        {
            // 应用主题CSS变量
            root.style.setProperty("--primary", ColorToHex(primary));
            root.style.setProperty("--secondary", ColorToHex(secondary));
            root.style.setProperty("--accent", ColorToHex(accent));
            root.style.setProperty("--neutral", ColorToHex(neutral));
            root.style.setProperty("--base-100", ColorToHex(baseColor));
            root.style.setProperty("--info", ColorToHex(info));
            root.style.setProperty("--success", ColorToHex(success));
            root.style.setProperty("--warning", ColorToHex(warning));
            root.style.setProperty("--error", ColorToHex(error));
            
            // 应用字体和间距
            root.style.setProperty("--base-font-size", $"{baseFontSize}px");
            root.style.setProperty("--base-spacing", $"{baseSpacing}px");
            root.style.setProperty("--border-radius", $"{borderRadius}px");
            
            Current = this;
        }
        
        private string ColorToHex(Color color)
        {
            return $"#{ColorUtility.ToHtmlStringRGBA(color)}";
        }
    }
}
```

## 🧩 组件实现

### 1. 按钮组件

```csharp
// DaisyButton.cs
using UnityEngine.UIElements;

namespace BlastingDesign.UI.Daisy.Components.Actions
{
    public class DaisyButton : DaisyComponent
    {
        private Button _button;
        
        public DaisyButton(string text = "Button") : base("btn")
        {
            _button = new Button();
            _button.text = text;
            Add(_button);
        }
        
        // 静态工厂方法
        public static DaisyButton Create(string text = "Button")
        {
            return new DaisyButton(text);
        }
        
        // 链式调用方法
        public DaisyButton Primary() => (DaisyButton)WithVariant("primary");
        public DaisyButton Secondary() => (DaisyButton)WithVariant("secondary");
        public DaisyButton Accent() => (DaisyButton)WithVariant("accent");
        public DaisyButton Ghost() => (DaisyButton)WithVariant("ghost");
        public DaisyButton Link() => (DaisyButton)WithVariant("link");
        
        public DaisyButton Large() => (DaisyButton)WithSize("lg");
        public DaisyButton Small() => (DaisyButton)WithSize("sm");
        public DaisyButton ExtraSmall() => (DaisyButton)WithSize("xs");
        
        public DaisyButton Outline() => (DaisyButton)SetModifier("outline");
        public DaisyButton Wide() => (DaisyButton)SetModifier("wide");
        public DaisyButton Block() => (DaisyButton)SetModifier("block");
        public DaisyButton Circle() => (DaisyButton)SetModifier("circle");
        public DaisyButton Square() => (DaisyButton)SetModifier("square");
        
        public DaisyButton Loading() => (DaisyButton)SetModifier("loading");
        public DaisyButton Disabled() => (DaisyButton)SetModifier("disabled");
        
        public DaisyButton OnClick(System.Action callback)
        {
            _button.clicked += callback;
            return this;
        }
        
        public DaisyButton WithText(string text)
        {
            _button.text = text;
            return this;
        }
    }
}
```

### 2. 卡片组件

```csharp
// DaisyCard.cs
using UnityEngine.UIElements;

namespace BlastingDesign.UI.Daisy.Components.DataDisplay
{
    public class DaisyCard : DaisyComponent
    {
        private VisualElement _cardBody;
        private VisualElement _cardActions;
        
        public DaisyCard() : base("card")
        {
            _cardBody = new VisualElement();
            _cardBody.AddToClassList("daisy-card-body");
            Add(_cardBody);
        }
        
        public static DaisyCard Create() => new DaisyCard();
        
        public DaisyCard WithTitle(string title)
        {
            var titleElement = new Label(title);
            titleElement.AddToClassList("daisy-card-title");
            _cardBody.Add(titleElement);
            return this;
        }
        
        public DaisyCard WithContent(VisualElement content)
        {
            _cardBody.Add(content);
            return this;
        }
        
        public DaisyCard WithActions(params DaisyButton[] actions)
        {
            if (_cardActions == null)
            {
                _cardActions = new VisualElement();
                _cardActions.AddToClassList("daisy-card-actions");
                Add(_cardActions);
            }
            
            foreach (var action in actions)
            {
                _cardActions.Add(action);
            }
            return this;
        }
        
        public DaisyCard Compact() => (DaisyCard)SetModifier("compact");
        public DaisyCard Bordered() => (DaisyCard)SetModifier("bordered");
        public DaisyCard Glass() => (DaisyCard)SetModifier("glass");
    }
}
```

### 3. 输入框组件

```csharp
// DaisyInput.cs
using UnityEngine.UIElements;

namespace BlastingDesign.UI.Daisy.Components.DataInput
{
    public class DaisyInput : DaisyComponent
    {
        private TextField _textField;
        
        public DaisyInput(string placeholder = "") : base("input")
        {
            _textField = new TextField();
            _textField.value = placeholder;
            Add(_textField);
        }
        
        public static DaisyInput Create(string placeholder = "")
        {
            return new DaisyInput(placeholder);
        }
        
        public DaisyInput WithType(string type)
        {
            // 根据类型设置不同的输入行为
            switch (type.ToLower())
            {
                case "password":
                    _textField.isPasswordField = true;
                    break;
                case "email":
                    AddToClassList("daisy-input-email");
                    break;
                case "number":
                    AddToClassList("daisy-input-number");
                    break;
            }
            return this;
        }
        
        public DaisyInput WithPlaceholder(string placeholder)
        {
            // Unity UI Toolkit的占位符实现
            _textField.value = placeholder;
            return this;
        }
        
        public DaisyInput Bordered() => (DaisyInput)SetModifier("bordered");
        public DaisyInput Ghost() => (DaisyInput)SetModifier("ghost");
        public DaisyInput Error() => (DaisyInput)SetModifier("error");
        public DaisyInput Success() => (DaisyInput)SetModifier("success");
        
        public DaisyInput OnValueChanged(System.Action<string> callback)
        {
            _textField.RegisterValueChangedCallback(evt => callback?.Invoke(evt.newValue));
            return this;
        }
    }
}
```

## 🎨 样式系统

### 1. 主题变量

```css
/* themes.uss */
:root {
    /* 基础颜色 */
    --primary: #3B82F6;
    --primary-content: #FFFFFF;
    --primary-focus: #2563EB;
    
    --secondary: #6B7280;
    --secondary-content: #FFFFFF;
    --secondary-focus: #4B5563;
    
    --accent: #06B6D4;
    --accent-content: #FFFFFF;
    --accent-focus: #0891B2;
    
    --neutral: #374151;
    --neutral-content: #FFFFFF;
    --neutral-focus: #1F2937;
    
    --base-100: #FFFFFF;
    --base-200: #F3F4F6;
    --base-300: #E5E7EB;
    --base-content: #1F2937;
    
    /* 状态颜色 */
    --info: #0EA5E9;
    --success: #10B981;
    --warning: #F59E0B;
    --error: #EF4444;
    
    /* 组件变量 */
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    
    --btn-padding-x: 16px;
    --btn-padding-y: 8px;
    --btn-font-size: 14px;
    --btn-font-weight: 500;
    --btn-line-height: 1.25;
    --btn-min-height: 40px;
    --btn-gap: 8px;
    
    --card-padding: 24px;
    --card-border-radius: var(--border-radius);
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    --input-padding-x: 12px;
    --input-padding-y: 8px;
    --input-font-size: 14px;
    --input-border-width: 1px;
    --input-border-color: var(--base-300);
}

/* 暗色主题 */
.theme-dark {
    --primary: #3B82F6;
    --primary-content: #FFFFFF;
    --primary-focus: #2563EB;
    
    --secondary: #6B7280;
    --secondary-content: #FFFFFF;
    --secondary-focus: #4B5563;
    
    --base-100: #1F2937;
    --base-200: #374151;
    --base-300: #4B5563;
    --base-content: #F9FAFB;
    
    --input-border-color: var(--base-300);
}
```

### 2. 按钮样式

```css
/* buttons.uss */
.daisy-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    padding: var(--btn-padding-y) var(--btn-padding-x);
    font-size: var(--btn-font-size);
    font-weight: var(--btn-font-weight);
    line-height: var(--btn-line-height);
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    white-space: nowrap;
    user-select: none;
    text-align: center;
    min-height: var(--btn-min-height);
    gap: var(--btn-gap);
}

/* 按钮变体 */
.daisy-btn-primary {
    background-color: var(--primary);
    color: var(--primary-content);
    border-color: var(--primary);
}

.daisy-btn-primary:hover {
    background-color: var(--primary-focus);
    border-color: var(--primary-focus);
}

.daisy-btn-secondary {
    background-color: var(--secondary);
    color: var(--secondary-content);
    border-color: var(--secondary);
}

.daisy-btn-accent {
    background-color: var(--accent);
    color: var(--accent-content);
    border-color: var(--accent);
}

.daisy-btn-ghost {
    background-color: transparent;
    border-color: transparent;
    color: var(--base-content);
}

.daisy-btn-link {
    background-color: transparent;
    border-color: transparent;
    color: var(--primary);
    text-decoration: underline;
}

/* 按钮尺寸 */
.daisy-btn-xs {
    --btn-padding-x: 8px;
    --btn-padding-y: 4px;
    --btn-font-size: 12px;
    --btn-min-height: 24px;
}

.daisy-btn-sm {
    --btn-padding-x: 12px;
    --btn-padding-y: 6px;
    --btn-font-size: 14px;
    --btn-min-height: 32px;
}

.daisy-btn-lg {
    --btn-padding-x: 24px;
    --btn-padding-y: 12px;
    --btn-font-size: 18px;
    --btn-min-height: 48px;
}

/* 按钮修饰符 */
.daisy-btn-outline {
    background-color: transparent;
    border-color: currentColor;
}

.daisy-btn-wide {
    padding-left: 32px;
    padding-right: 32px;
}

.daisy-btn-block {
    width: 100%;
}

.daisy-btn-circle {
    border-radius: 50%;
    width: var(--btn-min-height);
    padding: 0;
}

.daisy-btn-square {
    border-radius: var(--border-radius);
    width: var(--btn-min-height);
    padding: 0;
}

.daisy-btn-loading {
    pointer-events: none;
    opacity: 0.8;
}

.daisy-btn-disabled {
    pointer-events: none;
    opacity: 0.6;
}
```

### 3. 卡片样式

```css
/* cards.uss */
.daisy-card {
    background-color: var(--base-100);
    border-radius: var(--card-border-radius);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.daisy-card-body {
    padding: var(--card-padding);
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.daisy-card-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--base-content);
    line-height: 1.25;
}

.daisy-card-actions {
    padding: 0 var(--card-padding) var(--card-padding);
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
    justify-content: flex-end;
}

/* 卡片修饰符 */
.daisy-card-compact .daisy-card-body {
    padding: 16px;
}

.daisy-card-bordered {
    border: 1px solid var(--base-300);
}

.daisy-card-glass {
    background-color: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}
```

### 4. 输入框样式

```css
/* inputs.uss */
.daisy-input {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.daisy-input TextField {
    padding: var(--input-padding-y) var(--input-padding-x);
    font-size: var(--input-font-size);
    border: var(--input-border-width) solid var(--input-border-color);
    border-radius: var(--border-radius);
    background-color: var(--base-100);
    color: var(--base-content);
    transition: border-color 0.2s ease-in-out;
}

.daisy-input TextField:focus {
    border-color: var(--primary);
    outline: none;
}

/* 输入框修饰符 */
.daisy-input-bordered TextField {
    border-color: var(--base-300);
}

.daisy-input-ghost TextField {
    border-color: transparent;
    background-color: transparent;
}

.daisy-input-error TextField {
    border-color: var(--error);
}

.daisy-input-success TextField {
    border-color: var(--success);
}

/* 输入框尺寸 */
.daisy-input-xs TextField {
    --input-padding-x: 8px;
    --input-padding-y: 4px;
    --input-font-size: 12px;
}

.daisy-input-sm TextField {
    --input-padding-x: 10px;
    --input-padding-y: 6px;
    --input-font-size: 14px;
}

.daisy-input-lg TextField {
    --input-padding-x: 16px;
    --input-padding-y: 12px;
    --input-font-size: 18px;
}
```

## 🏗️ 构建器模式

### 1. 主构建器

```csharp
// DaisyBuilder.cs
using UnityEngine.UIElements;
using BlastingDesign.UI.Daisy.Components.Actions;
using BlastingDesign.UI.Daisy.Components.DataDisplay;
using BlastingDesign.UI.Daisy.Components.DataInput;

namespace BlastingDesign.UI.Daisy.Builders
{
    public static class DaisyBuilder
    {
        // 按钮构建器
        public static DaisyButton Button(string text = "Button")
        {
            return DaisyButton.Create(text);
        }
        
        // 卡片构建器
        public static DaisyCard Card()
        {
            return DaisyCard.Create();
        }
        
        // 输入框构建器
        public static DaisyInput Input(string placeholder = "")
        {
            return DaisyInput.Create(placeholder);
        }
        
        // 容器构建器
        public static VisualElement Container(params VisualElement[] children)
        {
            var container = new VisualElement();
            container.AddToClassList("daisy-container");
            
            foreach (var child in children)
            {
                container.Add(child);
            }
            
            return container;
        }
        
        // 网格构建器
        public static VisualElement Grid(int columns, params VisualElement[] children)
        {
            var grid = new VisualElement();
            grid.AddToClassList("daisy-grid");
            grid.style.setProperty("--grid-columns", columns.ToString());
            
            foreach (var child in children)
            {
                grid.Add(child);
            }
            
            return grid;
        }
        
        // 复合组件构建器
        public static VisualElement LoginForm()
        {
            var container = Container();
            
            var card = Card()
                .WithTitle("用户登录")
                .WithContent(Container(
                    Input("请输入邮箱").WithType("email").Bordered(),
                    Input("请输入密码").WithType("password").Bordered()
                ))
                .WithActions(
                    Button("取消").Ghost(),
                    Button("登录").Primary()
                );
            
            container.Add(card);
            return container;
        }
        
        // 项目仪表板构建器
        public static VisualElement ProjectDashboard()
        {
            return Container(
                // 头部卡片
                Card()
                    .WithTitle("项目仪表板")
                    .WithContent(new Label("欢迎使用3D爆破设计系统"))
                    .Bordered(),
                
                // 操作按钮组
                Container(
                    Button("新建项目").Primary().Large(),
                    Button("导入数据").Secondary().Large(),
                    Button("系统设置").Ghost().Large()
                ).AddToClassList("daisy-btn-group"),
                
                // 统计卡片网格
                Grid(3,
                    Card().WithTitle("活跃项目").WithContent(new Label("12")).Compact(),
                    Card().WithTitle("总孔数").WithContent(new Label("1,234")).Compact(),
                    Card().WithTitle("完成率").WithContent(new Label("89%")).Compact()
                )
            );
        }
    }
}
```

### 2. 扩展方法

```csharp
// DaisyExtensions.cs
using UnityEngine.UIElements;
using BlastingDesign.UI.Daisy.Core;

namespace BlastingDesign.UI.Daisy.Extensions
{
    public static class DaisyExtensions
    {
        // 链式调用扩展
        public static T With<T>(this T element, System.Action<T> action) where T : VisualElement
        {
            action?.Invoke(element);
            return element;
        }
        
        // 样式扩展
        public static T AddDaisyClass<T>(this T element, string className) where T : VisualElement
        {
            element.AddToClassList($"daisy-{className}");
            return element;
        }
        
        // 主题扩展
        public static T WithTheme<T>(this T element, DaisyTheme theme) where T : VisualElement
        {
            theme.Apply(element);
            return element;
        }
        
        // 动画扩展
        public static T WithAnimation<T>(this T element, string animationClass) where T : VisualElement
        {
            element.AddToClassList($"daisy-animate-{animationClass}");
            return element;
        }
        
        // 间距扩展
        public static T WithSpacing<T>(this T element, string spacing) where T : VisualElement
        {
            element.AddToClassList($"daisy-spacing-{spacing}");
            return element;
        }
        
        // 响应式扩展
        public static T WithResponsive<T>(this T element, string breakpoint, string className) where T : VisualElement
        {
            element.AddToClassList($"daisy-{breakpoint}-{className}");
            return element;
        }
        
        // 条件样式扩展
        public static T WithConditionalClass<T>(this T element, bool condition, string className) where T : VisualElement
        {
            if (condition)
                element.AddToClassList(className);
            return element;
        }
    }
}
```

## 📋 实施计划

### ✅ 阶段1：基础架构搭建（已完成）

#### 已完成任务
- [x] 创建DaisyUI目录结构
- [x] 实现DaisyComponent基类（已重构为CSS类优先）
- [x] 实现DaisyTheme主题系统
- [x] 创建TailwindUss样式库增强版
- [x] 实现DaisyBuilder构建器基础框架
- [x] 创建TailwindUssExtensions扩展方法
- [x] 建立CSS类优先的样式管理系统
- [x] 完成USS合规性重构

#### 已创建关键文件
- `DaisyComponent.cs` - 组件基类（已重构）
- `DaisyTheme.cs` - 主题系统
- `DaisyBuilder.cs` - 构建器
- `TailwindUssExtensions.cs` - TailwindUss扩展方法（新增）
- `Assets/Resources/TailwindUss/daisy-components.uss` - DaisyUI组件样式（新增）
- `Assets/Resources/TailwindUss/index.uss` - 样式入口文件（已更新）

#### 重构成果
- ✅ 彻底消除了内联样式的使用
- ✅ 建立了基于CSS类的样式管理系统
- ✅ 确保USS规范合规性
- ✅ 创建了完整的TailwindUss工具类库
- ✅ 实现了流式API和链式调用支持

### 阶段1补充：基础架构优化建议

#### 新增重构指导原则
1. **CSS类优先原则**：所有样式修改必须通过CSS类实现，避免使用`element.style.property = value`
2. **TailwindUss集成**：充分利用现有TailwindUss样式库，通过工具类实现常见样式需求
3. **USS合规性**：严格遵守Unity USS规范，避免使用不支持的CSS特性
4. **组件架构**：采用语义化的CSS类命名，支持尺寸、变体、修饰符的组合使用

### ✅ 阶段2：核心组件实现（已完成基础部分）

#### 已完成任务
- [x] 实现DaisyButton按钮组件（已重构为CSS类优先）
- [x] 实现DaisyCard卡片组件（已重构为CSS类优先）
- [x] 实现DaisyInput输入框组件（已重构为CSS类优先）
- [x] 创建完整的daisy-components.uss样式文件
- [x] 实现TailwindUss工具类集成
- [x] 消除所有内联样式使用

#### 待完成任务
- [ ] 实现DaisySelect选择器组件（需要重构为CSS类优先）
- [ ] 完善组件的响应式支持
- [ ] 添加组件动画效果（基于CSS类）
- [ ] 创建组件使用示例和文档

#### 重构后的关键文件
- `DaisyButton.cs` - 按钮组件（已重构）
- `DaisyCard.cs` - 卡片组件（已重构）
- `DaisyInput.cs` - 输入框组件（已重构）
- `TailwindUssExtensions.cs` - TailwindUss工具扩展
- `daisy-components.uss` - 统一的组件样式文件

#### 重构验收标准
- ✅ 所有组件完全基于CSS类进行样式管理
- ✅ 链式调用API工作正常
- ✅ 样式和主题切换正常
- ✅ USS规范完全合规
- [ ] 组件响应式效果正常（待测试）

#### 阶段2补充：重构优化成果

##### 性能提升
- **减少运行时计算**：CSS类预定义，避免动态样式计算
- **更好的渲染性能**：Unity UI Toolkit对CSS类有更好的优化
- **内存使用优化**：减少style对象的频繁修改

##### 开发体验改进
- **TailwindUss集成**：提供完整的工具类库，支持间距、尺寸、颜色等快速设置
- **流式API**：通过扩展方法实现更优雅的链式调用
- **类型安全**：泛型扩展方法提供更好的类型推断

### 阶段3：高级组件开发（3-4周）

#### 任务清单
- [ ] 实现DaisyModal模态框组件
- [ ] 实现DaisyDropdown下拉菜单组件
- [ ] 实现DaisyTabs标签页组件
- [ ] 实现DaisyTable数据表格组件
- [ ] 实现DaisyPagination分页组件
- [ ] 实现DaisyBreadcrumb面包屑组件
- [ ] 添加高级动画效果
- [ ] 实现组件间通信机制

#### 关键文件
- `DaisyModal.cs` - 模态框组件
- `DaisyDropdown.cs` - 下拉菜单组件
- `DaisyTabs.cs` - 标签页组件
- `DaisyTable.cs` - 数据表格组件
- 对应的USS样式文件

#### 验收标准
- 所有高级组件功能完整
- 组件间通信机制正常
- 动画效果流畅
- 性能表现良好

### 阶段4：集成和优化（1-2周）

#### 任务清单
- [ ] 与现有项目UIManager集成
- [ ] 性能优化和内存管理
- [ ] 兼容性测试
- [ ] 编写使用文档和示例
- [ ] 用户反馈收集和改进
- [ ] 单元测试编写

#### 关键文件
- 集成示例代码
- 性能优化补丁
- 使用文档
- 测试用例

#### 验收标准
- 与现有系统完全兼容
- 性能达到预期要求
- 文档完整可用
- 测试覆盖率达标

## 🎯 使用示例

### 1. 基础组件使用（重构后）

```csharp
// 创建基础按钮 - 使用CSS类优先的新架构
var button = DaisyBuilder.Button("点击我")
    .SetPrimary()           // 使用CSS类: .daisy-btn-primary
    .SetLarge()             // 使用CSS类: .daisy-btn-lg
    .OnClick(() => Debug.Log("按钮被点击"));

// 使用TailwindUss扩展方法进行样式设置
var styledButton = DaisyBuilder.Button("样式按钮")
    .SetPrimary()
    .SetPadding(4)          // 使用Tailwind类: .p-4
    .SetMargin(2)           // 使用Tailwind类: .m-2
    .SetRounded("lg")       // 使用Tailwind类: .rounded-lg
    .SetShadow("md");       // 使用Tailwind类: .shadow-md

// 创建输入框 - 完全基于CSS类
var input = DaisyBuilder.Input("请输入用户名")
    .SetBordered()          // 使用CSS类: .daisy-input-bordered
    .WithType("text")
    .SetWidth("full")       // 使用Tailwind类: .w-full
    .OnValueChanged(value => Debug.Log($"输入值: {value}"));

// 创建卡片 - 使用重构后的API
var card = DaisyBuilder.Card()
    .SetTitle("用户信息")   // 自动管理标题可见性
    .AddContent(new Label("这是卡片内容"))
    .AddActions(
        DaisyBuilder.Button("取消").SetGhost(),
        DaisyBuilder.Button("确定").SetPrimary()
    )
    .SetBordered()
    .ApplyDaisyShadow("lg"); // 使用DaisyUI特定的阴影效果
```

### 2. TailwindUss工具类使用示例

```csharp
// 使用TailwindUss扩展方法快速设置样式
var container = new VisualElement()
    .SetFlex()                    // display: flex
    .SetFlexDirection("col")      // flex-direction: column
    .SetPadding(6)               // padding: 24px
    .SetBackgroundColor("slate-100")  // background-color: var(--slate-100)
    .SetRounded("xl")            // border-radius: 12px
    .SetShadow("lg");            // box-shadow: large

// 条件样式应用
var errorInput = DaisyBuilder.Input("输入内容")
    .When(hasError, "daisy-input-error")  // 条件应用错误样式
    .When(isRequired, input => input.SetBorderColor("red-500")); // 条件设置边框色

// 响应式设计（如果支持）
var responsiveCard = DaisyBuilder.Card()
    .SetWidth("full")            // 默认全宽
    .SetWidth("one-half")        // 中等屏幕一半宽度
    .SetPadding(4)               // 默认内边距
    .SetPadding(6);              // 大屏幕更大内边距
```

### 2. 复杂UI构建

```csharp
// 项目管理界面
public class ProjectManagerUI : UIComponentBase
{
    protected override void OnInitialize()
    {
        var dashboard = DaisyBuilder.Container(
            // 头部区域
            DaisyBuilder.Card()
                .WithTitle("项目管理")
                .WithContent(new Label("管理您的3D爆破设计项目"))
                .Bordered(),
            
            // 操作区域
            DaisyBuilder.Container(
                DaisyBuilder.Button("新建项目").Primary().Large(),
                DaisyBuilder.Button("导入项目").Secondary().Large(),
                DaisyBuilder.Button("导出数据").Ghost().Large()
            ).AddDaisyClass("btn-group"),
            
            // 项目列表
            DaisyBuilder.Grid(2,
                DaisyBuilder.Card()
                    .WithTitle("矿山A区")
                    .WithContent(new Label("进度: 75%"))
                    .WithActions(
                        DaisyBuilder.Button("编辑").Small().Primary(),
                        DaisyBuilder.Button("删除").Small().Ghost()
                    )
                    .Compact(),
                    
                DaisyBuilder.Card()
                    .WithTitle("矿山B区")
                    .WithContent(new Label("进度: 45%"))
                    .WithActions(
                        DaisyBuilder.Button("编辑").Small().Primary(),
                        DaisyBuilder.Button("删除").Small().Ghost()
                    )
                    .Compact()
            )
        );
        
        rootElement.Add(dashboard);
    }
}
```

### 3. 主题切换

```csharp
// 主题管理器
public class ThemeManager : MonoBehaviour
{
    public DaisyTheme lightTheme;
    public DaisyTheme darkTheme;
    
    private bool isDarkMode = false;
    
    public void ToggleTheme()
    {
        isDarkMode = !isDarkMode;
        var theme = isDarkMode ? darkTheme : lightTheme;
        var root = GetComponent<UIDocument>().rootVisualElement;
        theme.Apply(root);
    }
}
```

## 📊 性能优化

### 1. 内存管理

```csharp
// 组件池管理
public class DaisyComponentPool
{
    private static Dictionary<Type, Queue<DaisyComponent>> _pools = new();
    
    public static T Get<T>() where T : DaisyComponent, new()
    {
        var type = typeof(T);
        if (!_pools.ContainsKey(type))
            _pools[type] = new Queue<DaisyComponent>();
        
        var pool = _pools[type];
        if (pool.Count > 0)
            return (T)pool.Dequeue();
        
        return new T();
    }
    
    public static void Return<T>(T component) where T : DaisyComponent
    {
        var type = typeof(T);
        if (!_pools.ContainsKey(type))
            _pools[type] = new Queue<DaisyComponent>();
        
        component.Reset(); // 重置组件状态
        _pools[type].Enqueue(component);
    }
}
```

### 2. 样式优化

```css
/* 性能优化样式 */
.daisy-optimized {
    /* 启用硬件加速 */
    will-change: transform;
    
    /* 优化重绘 */
    contain: layout style paint;
    
    /* 减少重排 */
    transform: translateZ(0);
}

/* 虚拟化列表 */
.daisy-virtual-list {
    overflow: hidden;
    position: relative;
}

.daisy-virtual-item {
    position: absolute;
    width: 100%;
}
```

## 🔧 调试和测试

### 1. 调试工具

```csharp
// DaisyDebugger.cs
public static class DaisyDebugger
{
    public static void LogComponentTree(VisualElement root)
    {
        LogElement(root, 0);
    }
    
    private static void LogElement(VisualElement element, int depth)
    {
        var indent = new string(' ', depth * 2);
        var classes = string.Join(", ", element.GetClasses());
        Debug.Log($"{indent}{element.GetType().Name} - Classes: {classes}");
        
        foreach (var child in element.Children())
        {
            LogElement(child, depth + 1);
        }
    }
    
    public static void ValidateTheme(DaisyTheme theme)
    {
        if (theme.primary == Color.clear)
            Debug.LogWarning("主题主色调未设置");
        
        if (theme.baseFontSize <= 0)
            Debug.LogWarning("基础字体大小无效");
    }
}
```

### 2. 单元测试

```csharp
// DaisyButtonTests.cs
[TestFixture]
public class DaisyButtonTests
{
    [Test]
    public void Button_Creation_ShouldHaveCorrectClasses()
    {
        var button = DaisyBuilder.Button("测试按钮");
        
        Assert.IsTrue(button.ClassListContains("daisy-btn"));
        Assert.IsTrue(button.ClassListContains("daisy-btn-md"));
        Assert.IsTrue(button.ClassListContains("daisy-btn-default"));
    }
    
    [Test]
    public void Button_SetPrimary_ShouldAddPrimaryClass()
    {
        var button = DaisyBuilder.Button("测试按钮").Primary();
        
        Assert.IsTrue(button.ClassListContains("daisy-btn-primary"));
    }
    
    [Test]
    public void Button_OnClick_ShouldTriggerCallback()
    {
        var clicked = false;
        var button = DaisyBuilder.Button("测试按钮")
            .OnClick(() => clicked = true);
        
        // 模拟点击
        button.SendEvent(new ClickEvent());
        
        Assert.IsTrue(clicked);
    }
}
```

## 📚 最佳实践（重构后更新）

### 1. 命名约定

- **组件类名**: `DaisyButton`, `DaisyCard`
- **CSS类名**: `daisy-btn`, `daisy-card`
- **TailwindUss类名**: `p-4`, `m-2`, `bg-blue-500`
- **变量名**: `--primary`, `--btn-padding-x`
- **修饰符**: `primary`, `large`, `outline`

### 2. 样式管理最佳实践（新增）

#### CSS类优先原则
```csharp
// ✅ 推荐：使用CSS类
element.AddToClassList("daisy-hidden");

// ❌ 避免：使用内联样式
element.style.display = DisplayStyle.None;
```

#### TailwindUss工具类使用
```csharp
// ✅ 推荐：使用TailwindUss扩展方法
element.SetPadding(4).SetMargin(2).SetBackgroundColor("blue-500");

// ❌ 避免：手动CSS类拼接
element.AddToClassList("p-4");
element.AddToClassList("m-2");
element.AddToClassList("bg-blue-500");
```

#### USS合规性检查
```csharp
// ✅ USS兼容的属性写法
.my-class {
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
    border-left-width: 1px;
}

// ❌ 避免CSS简写（USS不支持）
.my-class {
    border: 1px solid black; /* 不兼容 */
}
```

### 3. 性能建议（更新）

#### 样式性能优化
- **优先使用CSS类**：避免运行时样式计算
- **合理使用CSS变量**：提高主题切换性能
- **减少DOM操作**：批量应用CSS类而非逐个设置
- **缓存CSS类名**：使用DaisyUtilities的缓存机制

#### 内存管理
- 使用组件池减少GC压力
- 及时移除不必要的事件监听器
- 合理管理VisualElement的生命周期

### 4. 可维护性（更新）

#### 代码组织
- **组件文件大小控制**：超过600行使用partial类拆分
- **CSS类集中管理**：所有DaisyUI样式在daisy-components.uss中定义
- **扩展方法分类**：按功能区分TailwindUss扩展方法

#### 开发模式
- **渐进式增强**：先实现基础功能，再添加样式增强
- **组件复用**：通过继承和组合实现组件变体
- **类型安全**：使用泛型扩展方法保证类型推断

### 5. 调试和测试（新增）

#### 样式调试
```csharp
// 使用DaisyDebugger查看组件CSS类
DaisyDebugger.LogComponentTree(rootElement);

// 验证CSS类是否正确应用
Assert.IsTrue(button.ClassListContains("daisy-btn-primary"));
```

#### 性能监控
- 监控CSS类添加/移除频率
- 检查不必要的样式重绘
- 验证内存使用情况

## 🎉 总结（重构版本）

### 已完成的重要改进

这个DaisyUI到Unity UI Toolkit的迁移方案经过重构后现在提供了：

1. **完整的组件架构** - 从基础组件到复杂UI构建，全面基于CSS类管理
2. **现代化的API设计** - 链式调用和流畅的开发体验，集成TailwindUss工具类
3. **强大的主题系统** - 灵活的样式定制和主题切换，支持CSS变量
4. **高性能实现** - 原生Unity渲染优化，消除内联样式性能损耗
5. **易于扩展** - 模块化设计和丰富的扩展方法支持
6. **USS规范合规** - 完全兼容Unity UI Toolkit的样式系统
7. **TailwindUss集成** - 提供完整的工具类库，简化样式开发

### 重构带来的核心优势

#### 性能提升
- ✅ **消除运行时样式计算**：所有样式通过预定义CSS类实现
- ✅ **更好的渲染性能**：Unity UI Toolkit对CSS类有更好的优化
- ✅ **减少内存分配**：避免频繁的style对象修改

#### 开发体验改进
- ✅ **TailwindUss工具类**：提供完整的间距、尺寸、颜色工具
- ✅ **类型安全的API**：泛型扩展方法提供更好的类型推断
- ✅ **一致的样式管理**：统一的CSS类命名和管理机制

#### 代码质量提升
- ✅ **USS规范合规**：严格遵守Unity USS限制和最佳实践
- ✅ **更好的可维护性**：集中化的样式管理和模块化架构
- ✅ **测试友好**：基于CSS类的样式便于单元测试验证

### 下一步发展方向

1. **组件库扩展** - 基于新的CSS类优先架构开发更多组件
2. **性能基准测试** - 验证重构后的性能改进效果
3. **开发工具集成** - 创建Unity Editor工具辅助DaisyUI开发
4. **文档和示例** - 提供完整的使用指南和最佳实践
5. **社区贡献** - 建立贡献指南和代码规范

通过这次重构，DaisyUI框架现在完全符合Unity UI Toolkit的最佳实践，为3D爆破设计应用提供了高性能、易维护的UI解决方案。

---

*该文档已根据重构成果进行更新，反映了最新的架构和实现方式。*

### 🔄 版本历史

**v2.0 (当前版本)** - 重构版本
- 完全基于CSS类的样式管理
- TailwindUss工具类集成
- USS规范合规性保证
- 性能和可维护性大幅提升

**v1.0** - 初始版本
- 基础DaisyUI组件架构
- 混合使用内联样式和CSS类
- 基础主题系统支持