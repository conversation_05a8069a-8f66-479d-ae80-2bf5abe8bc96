# OVERVIEW.md

此文件为项目的总体概述，使开发者可以快速了解项目的整体情况

## 项目概述

这是一个 Unity 6.1 项目，实现了一个面向采矿和建筑行业的 **3D 爆破设计应用程序**。该项目使用 Unity 的 UI Toolkit、自研的 DaisyUI 组件库、Cesium for Unity 和 URP（通用渲染管线）来创建一个复杂的 3D 场景编辑器，具有先进的输入处理、材质管理功能和现代化的用户界面系统。

### 业务领域
应用程序专注于：
- **3D 钻孔可视化**：高性能钻孔模板系统，支持实时渲染数千个钻孔
- **爆破区域管理**：创建、编辑和管理带有钻孔参数的爆破区域
- **测量点管理**：收集、存储和处理爆破区域边界的测量点
- **钻孔设计与布局**：智能孔位排列算法，支持多种钻孔模式（三角形/方形）
- **项目数据管理**：完整的项目生命周期数据管理，包含地理参考
- **3D 场景交互**：高性能 3D 渲染和交互，用于采矿作业规划

## 技术栈

- **Unity 引擎**: 6.1 (6000.1.9f1)
- **UI 框架**: UI Toolkit (UXML/USS) + 自研 DaisyUI 组件库
- **样式系统**: TailwindUss 实用优先CSS类库
- **渲染管线**: 通用渲染管线 (URP) 17.1.0
- **3D 引擎**: Cesium for Unity 1.16.1
- **输入系统**: Unity 新输入系统 1.14.0 + 智能事件阻断
- **测试框架**: Unity 测试框架 1.5.1
- **数据库**: SQLite（主要）与可选的 SQL Server 支持
- **数据访问**: Dapper ORM 用于数据库操作
- **地理处理**: WGS84/UTM 坐标系统转换

## 构建命令

这是一个 Unity 项目，没有自定义构建脚本。使用 Unity 编辑器进行构建：

- **开发构建**: Unity 编辑器 → 文件 → 构建设置 → 构建
- **测试**: Unity 编辑器 → 窗口 → 通用 → 测试运行器
- **包管理**: Unity 编辑器 → 窗口 → 包管理器

## 架构概述

该项目采用 **基于管理器的架构** 并使用事件驱动通信，配合现代化的组件库系统：

### 核心系统

1. **InputManager**: 部分类系统，处理视口控制、第一人称模式、选择和鼠标包装
2. **UIManager**: 协调 UI Toolkit 界面与数据驱动配置
3. **SelectionManager**: 管理对象选择，提供视觉反馈和材质切换
4. **MaterialManager**: 处理 URP 兼容的材质创建和缓存
5. **ImportManager**: 管理 Cesium 3D 瓦片集导入与文件浏览器集成
6. **UIEventSystem**: 解耦系统通信的中央事件中心

### DaisyUI 组件库系统 (已完成的核心功能)

7. **DaisyUI 组件库**: 完整的 UI 组件库系统，包含：
   - **DaisyButton**: 多变体按钮组件，支持不同尺寸和样式
   - **DaisyInput**: 输入框组件，集成智能键盘事件阻断
   - **DaisyCard**: 卡片组件，支持标题、内容、操作区布局
   - **DaisySelect**: 下拉选择器组件，支持自定义选项
   - **DaisyModal**: 模态框组件，支持动画和主题
   - **DaisyDropdown**: 下拉菜单组件，支持嵌套菜单

8. **ComponentLibraryWindow**: 可视化组件库管理窗口
   - 实时组件预览和测试
   - 主题切换功能
   - 组件文档和示例展示

9. **扩展方法系统**: 为 VisualElement 提供 707 行扩展方法代码
   - 链式调用 API (With, When 等)
   - DaisyUI 样式管理 (AddDaisyClass, ToggleDaisyClass 等)
   - 主题和动画支持
   - 性能优化工具 (BatchUpdate, DelayedAction 等)

10. **智能事件阻断系统**: 基于焦点的键盘事件管理
    - 自动阻断UI焦点时的场景键盘事件
    - 智能恢复场景控制
    - 避免重复绑定的优化机制

### 业务系统（待实现）

11. **DataManager**: 使用 Dapper ORM 处理 SQLite 数据库操作，双数据库架构支持
12. **ProjectManager**: 管理项目生命周期、地理参考和坐标系统转换
13. **BlastAreaManager**: 管理爆破区域创建、编辑和钻孔参数配置
14. **MeasurePointManager**: 处理测量点收集和爆破区域边界定义
15. **DrillHoleManager**: 协调钻孔生成算法和外部 API 集成
16. **HoleGenerationService**: 执行自动孔位排列算法和几何计算

### 关键架构模式

- **管理器模式**: 单例管理器协调主要系统
- **事件驱动通信**: `UIEventSystem` 提供解耦消息传递
- **基于组件的UI**: DaisyUI 组件库与基于容器的布局
- **数据驱动配置**: ScriptableObjects 定义 UI 结构 (`MenuBarConfig`, `ToolbarConfig`)
- **部分类**: 大型系统跨多个文件分割以提高可维护性
- **扩展方法模式**: 通过扩展方法增强现有 UI 组件功能
- **组件-样式-模板三层架构**: 每个 DaisyUI 组件包含 C# + USS + UXML

## 代码组织

### 项目规模统计
- **C#文件总数**: 101个
- **扩展方法代码**: 707行（DaisyExtensions.cs）
- **DaisyUI组件**: 6个核心组件（Button、Input、Card、Select、Modal、Dropdown）
- **UI模板文件**: 17个UXML文件
- **样式文件**: 34个USS文件
- **测试框架**: 完整的组件验证和单元测试系统

### 核心系统 (`Assets/Scripts/Core/`)
- `Main.cs`: 应用程序入口点和场景设置
- `ImportManager.cs`: Cesium 集成和文件导入处理
- `GridPlane.cs`: 无限网格渲染系统，使用 URP 着色器

### 输入系统 (`Assets/Scripts/Input/`)
- `InputManager.cs`: 主要输入协调（部分类）
- 视口控制、第一人称模式、选择等的独立部分文件
- `InputEventPriorityManager.cs`: 解决 UI/场景交互冲突

### UI 系统 (`Assets/Scripts/UI/`)

#### 核心 UI 管理 (`Assets/Scripts/UI/`)
- `UIManager.cs`: 主要 UI 协调器
- 基于组件的架构与容器系统
- 配置驱动的菜单和工具栏生成

#### DaisyUI 组件库 (`Assets/Resources/DaisyUI/`)
- **核心层** (`Core/`):
  - `DaisyComponent.cs`: 所有 DaisyUI 组件的基类
  - `DaisyTheme.cs`: 主题管理和应用系统
  - `DaisyUtilities.cs`: 通用工具方法和常量
  - `TailwindUssExtensions.cs`: TailwindUss 集成扩展

- **组件层** (`Components/`):
  - `Actions/Button/`: DaisyButton 组件实现
  - `DataInput/Input/`: DaisyInput 输入框组件
  - `DataInput/Select/`: DaisySelect 选择器组件
  - `DataDisplay/Card/`: DaisyCard 卡片组件
  - `Navigation/Modal/`: DaisyModal 模态框组件
  - `Navigation/Dropdown/`: DaisyDropdown 下拉菜单组件

- **构建器** (`Builders/`):
  - `DaisyBuilder.cs`: 统一的组件构建和配置 API

- **验证系统** (`DaisyValidationExample/`):
  - 完整的组件测试和验证框架
  - 分模块的测试用例（按钮、输入框、卡片等）
  - 系统级集成测试

#### UI 扩展和工具 (`Assets/Scripts/UI/`)
- **扩展方法** (`Extensions/`):
  - `DaisyExtensions.cs`: 700+ VisualElement 扩展方法

- **核心工具** (`Core/`):
  - `TextFieldExtensions.cs`: TextField 智能键盘事件阻断
  - `UIEventBlocker.cs`: UI 事件拦截和管理
  - `UIEventSystem.cs`: 中央事件通信系统

- **UI 组件** (`Components/`):
  - `ComponentLibraryWindow.cs`: DaisyUI 组件库可视化窗口
  - `MenuBar.cs`, `Toolbar.cs`: 菜单和工具栏组件
  - `ModalWindow/`: 模态窗口系统
  - `SettingsWindow/`: 模块化设置窗口系统

- **测试系统** (`Tests/`):
  - `KeyboardEventBlockerTest.cs`: 键盘事件阻断功能测试

### 选择系统 (`Assets/Scripts/Selection/`)
- `SelectionManager.cs`: 支持多选的对象选择
- `SelectionBox.cs`: 视觉选择反馈
- `Selectable.cs`: 可选择对象的组件

### 材质系统 (`Assets/Scripts/Materials/`)
- `MaterialManager.cs`: URP 兼容的材质创建和缓存
- 带有颜色/属性变化的动态材质生成

### 业务系统 (`Assets/Scripts/Business/`) - 待实现

#### 数据管理 (`Assets/Scripts/Business/Data/`)
- `DataManager.cs`: 使用 Dapper ORM 的 SQLite 数据库操作
- `DatabaseModels/`: 数据库实体类 (ProjectInfo, BlastArea, MeasurePoint 等)
- `DatabaseService.cs`: 数据库连接管理和事务处理

#### 项目管理 (`Assets/Scripts/Business/Project/`)
- `ProjectManager.cs`: 项目生命周期管理和地理参考
- `ProjectEntity.cs`: 带有坐标系统转换的项目数据实体
- `CoordinateSystemConverter.cs`: WGS84/UTM 坐标转换工具

#### 爆破区域管理 (`Assets/Scripts/Business/BlastArea/`)
- `BlastAreaManager.cs`: 爆破区域创建、编辑和参数管理
- `BlastAreaEntity.cs`: 带有钻孔参数的爆破区域业务实体
- `BlastAreaService.cs`: 爆破区域操作的业务逻辑

#### 测量点 (`Assets/Scripts/Business/MeasurePoint/`)
- `MeasurePointManager.cs`: 测量点收集和边界定义
- `MeasurePointEntity.cs`: 带有地理坐标的测量点实体
- `BoundaryCalculator.cs`: 爆破区域边界的几何计算

#### 钻孔系统 (`Assets/Scripts/Business/DrillHole/`)
- `DrillHoleManager.cs`: 钻孔生成协调和 API 集成
- `HoleGenerationService.cs`: 自动孔位排列算法
- `DrillHoleEntity.cs`: 带有定位和参数的钻孔数据实体
- `PatternGenerator.cs`: 三角形和方形孔位模式生成算法

## DaisyUI 组件库架构

### TailwindUss 样式系统
项目使用位于 `Assets/Resources/TailwindUss/` 的综合 TailwindUss 样式库：
- **实用优先**：基于原子化CSS类的样式方法
- **模块化结构**：按功能分离的样式文件（布局、排版、颜色等）
- **Unity USS 兼容**：针对 Unity UI Toolkit 的 USS 语法优化
- **主题支持**：统一的主题变量和切换机制

### 组件开发模式
每个 DaisyUI 组件遵循三层架构：
1. **C# 组件类**：业务逻辑和事件处理
2. **USS 样式文件**：组件特定的样式定义
3. **UXML 模板文件**：组件的结构和布局模板

### 扩展方法系统
`DaisyExtensions.cs` 提供丰富的扩展方法支持：
- **链式调用**：With(), When() 等流畅 API
- **样式管理**：AddDaisyClass(), ToggleDaisyClass() 等样式操作
- **主题支持**：WithDaisyTheme(), ToggleDarkTheme() 等主题操作
- **动画系统**：WithAnimation(), WithFadeIn() 等动画扩展
- **响应式**：OnSmallScreen(), OnMediumScreen() 等响应式支持
- **性能优化**：BatchUpdate(), DelayedAction() 等性能工具

### 智能事件管理
- **焦点基础阻断**：输入框获得焦点时自动阻断场景键盘事件
- **智能恢复**：失去焦点时自动恢复场景控制
- **避免重复**：使用 ConditionalWeakTable 避免重复绑定
- **调试支持**：可选的调试日志记录
- **自动集成**：DaisyInput组件中已自动集成阻断功能

## UI Toolkit 集成

项目使用 Unity 的 UI Toolkit，结合自研组件库：
- **UXML 文件** 用于布局结构 (`Assets/UI Toolkit/`)
- **USS 文件** 用于样式（TailwindUss + 自定义样式）
- **ScriptableObject 配置** 用于数据驱动的 UI 生成
- **事件系统集成** 用于 UI/场景交互管理
- **DaisyUI 组件库** 提供现代化的 UI 组件
- **组件库窗口** 用于可视化组件管理和预览

## 输入系统功能

- **双输入模式**: 使用 Tab 键切换 Player/UI 模式
- **高级相机控制**: WASD 移动、鼠标观察、Q/E 垂直移动
- **第一人称模式**: 右键激活，带有鼠标包装
- **选择系统**: 框选，使用 Ctrl/Cmd 多选
- **Cesium 集成**: 使用键盘快捷键切换地形
- **智能事件优先级管理**: 自动解决 UI/场景交互冲突
- **焦点基础事件阻断**: UI 输入字段自动阻断场景事件

## Cesium 集成

项目集成 Cesium for Unity：
- **动态组件发现** 使用反射
- **文件导入系统** 将本地文件 URL 转换
- **交互式球体系统** 跟随鼠标光标在地形上
- **切换控制** 用于地形可见性

## 样式系统最佳实践

### TailwindUss 集成指南
- **优先使用 CSS 类**: 使用 TailwindUss 类而不是通过 C# 代码的内联样式
- **最小化运行时样式更改**: 避免在 C# 代码中修改 `element.style` 属性
- **USS 兼容性**: 遵循 Unity 的 USS 规范 - 不支持某些 CSS 简写属性和功能
- **基于类的主题**: 使用 CSS 类处理组件状态、主题和变化
- **语义类名**: 创建描述功能而非外观的有意义类名

### TailwindUss 样式库结构
- **布局**: Flexbox、网格、定位、间距工具
- **排版**: 字体大小、权重、行高、文本颜色
- **颜色**: 带有语义命名的综合调色板
- **背景**: 背景颜色、渐变、图案
- **边框**: 边框样式、圆角、颜色
- **效果**: 阴影、过渡、变换
- **交互状态**: 悬停、聚焦、激活、禁用状态
- **组件变体**: 尺寸变化、样式变体、主题变体

### 组件开发规则
1. **从 TailwindUss 类开始**: 在编写自定义样式之前，总是检查现有的实用类
2. **扩展 TailwindUss**: 在需要时向库中添加新的实用类
3. **避免内联样式**: 除非绝对必要，否则不要使用 `element.style.property = value`
4. **使用 USS 变量**: 利用 USS 自定义属性实现一致的主题
5. **遵循 USS 限制**: 了解 USS 相对于标准 CSS 的限制
6. **使用扩展方法**: 优先使用 DaisyExtensions 提供的链式调用 API

### 文件大小管理
- **600 行限制**: 使用 `partial` 类分割超过 600 行的 C# 文件
- **功能分离**: 按功能组织部分文件
- **一致命名**: 为部分文件使用描述性名称

## 常见开发任务

### 添加新的 DaisyUI 组件
1. 在 `Assets/Resources/DaisyUI/Components/` 相应分类目录中创建组件文件夹
2. 创建组件的 C# 类，继承自 `DaisyComponent`
3. 创建组件的 USS 样式文件
4. 创建组件的 UXML 模板文件
5. 在 `DaisyBuilder` 中添加构建方法
6. 在 `DaisyValidationExample` 中添加验证测试
7. 在 `ComponentLibraryWindow` 中注册组件

### 添加新的扩展方法
1. 在 `DaisyExtensions.cs` 中添加新的扩展方法
2. 遵循现有的命名约定和返回类型模式
3. 添加适当的 XML 文档注释
4. 使用 #region 组织相关方法

### 添加新的 UI 组件
1. 在 `Assets/Scripts/UI/Components/` 中创建组件类
2. 在 `UIManager` 中添加到适当的容器
3. 在相关的 ScriptableObject 中配置 (`MenuBarConfig`/`ToolbarConfig`)
4. 如需要，更新 UXML/USS 文件

### 扩展输入系统
1. 在 `Assets/Scripts/Input/InputManager/` 中添加新的部分类文件
2. 在部分类中实现功能
3. 如需要，在 `InputEventPriorityManager` 中注册事件
4. 如需要，更新输入动作映射

### 添加键盘事件阻断
1. 使用 `TextFieldExtensions.AddFocusBasedKeyboardEventBlocker()` 为输入字段添加阻断
2. 或在 DaisyInput 组件中，阻断功能已自动集成
3. 使用 `KeyboardEventBlockerTest` 验证阻断功能

### 添加新的管理器
1. 使用单例模式创建管理器类
2. 在 `Main.cs` 初始化中注册
3. 添加到 `UIEventSystem` 进行事件通信
4. 如复杂性需要，遵循部分类模式

### 材质系统集成
1. 使用 `MaterialManager` 进行 URP 兼容的材质创建
2. 缓存材质以避免重复
3. 使用基于模板的生成保证一致性
4. 通过事件处理材质状态变化

### 业务系统开发任务

#### 实现数据管理
1. 在 `Assets/Scripts/Business/Data/DatabaseModels/` 中创建数据库实体类
2. 在 `DataManager` 中使用 Dapper ORM 设置 SQLite 连接
3. 实现具有适当错误处理和事务的 CRUD 操作
4. 添加内存缓存层进行性能优化

#### 添加新的业务实体
1. 创建具有适当数据验证的实体类
2. 添加相应的数据库模型类
3. 遵循单例模式创建管理器类
4. 在服务类中实现业务逻辑
5. 添加到事件系统进行跨管理器通信

#### 地理坐标处理
1. 使用 `CoordinateSystemConverter` 进行 WGS84/UTM 转换
2. 处理坐标系统验证和错误情况
3. 实现地理计算的适当精度处理
4. 与 Cesium 坐标系统集成进行 3D 定位

#### 钻孔算法实现
1. 在 `Assets/Scripts/Business/DrillHole/` 中创建算法类
2. 实现带有几何计算的模式生成（三角形/方形）
3. 添加边界过滤以移除爆破区域外的孔
4. 使用 Unity 的作业系统进行性能关键计算
5. 处理准确定位的坐标转换

## 需要了解的关键文件

### 核心系统文件
- `Assets/Scripts/Core/Main.cs`: 应用程序初始化和场景设置
- `Assets/Scripts/UI/UIManager.cs`: UI 系统协调
- `Assets/Scripts/Input/InputManager.cs`: 输入系统入口点
- `Assets/Scripts/UI/Core/UIEventSystem.cs`: 中央事件通信
- `Assets/Resources/UI/MenuBarConfig.asset`: 菜单结构配置
- `Assets/Resources/UI/ToolbarConfig.asset`: 工具栏配置
- `Assets/UI Toolkit/MainLayout.uxml`: 主 UI 布局结构
- `Assets/UI Toolkit/MainLayout.uss`: 主 UI 样式

### DaisyUI 组件库核心文件
- `Assets/Resources/DaisyUI/Core/DaisyComponent.cs`: 所有 DaisyUI 组件的基类
- `Assets/Resources/DaisyUI/Core/DaisyTheme.cs`: 主题管理系统
- `Assets/Resources/DaisyUI/Builders/DaisyBuilder.cs`: 统一组件构建 API
- `Assets/Scripts/UI/Extensions/DaisyExtensions.cs`: 700+ 扩展方法集合
- `Assets/Scripts/UI/Components/ComponentLibraryWindow.cs`: 组件库可视化窗口
- `Assets/Scripts/UI/Core/TextFieldExtensions.cs`: 智能键盘事件阻断

### 样式系统文件
- `Assets/Resources/TailwindUss/index.uss`: TailwindUss 主样式入口
- `Assets/Resources/DaisyUI/daisy-components.uss`: DaisyUI 组件样式集合
- `Assets/Resources/Common/index.uss`: 通用样式组件

### 测试和验证文件
- `Assets/Resources/DaisyUI/DaisyValidationExample/`: 完整的组件验证框架
- `Assets/Scripts/UI/Tests/KeyboardEventBlockerTest.cs`: 键盘事件阻断测试

### 业务系统文件（待实现）
- `Assets/Scripts/Business/Data/DataManager.cs`: 核心数据管理和数据库操作
- `Assets/Scripts/Business/Project/ProjectManager.cs`: 项目生命周期和地理参考
- `Assets/Scripts/Business/BlastArea/BlastAreaManager.cs`: 爆破区域管理和配置
- `Assets/Scripts/Business/DrillHole/DrillHoleManager.cs`: 钻孔生成和算法协调
- `Assets/Scripts/Business/MeasurePoint/MeasurePointManager.cs`: 测量点收集和边界定义

### 业务流程
核心业务工作流遵循此模式：
```
项目创建 → 测量点收集 → 爆破区域划分 → 钻孔设计 → 方案优化 → 数据导出
```

### 关键业务常量
- **坐标系统**: WGS84、北京54、西安80、CGCS2000
- **孔位模式**: 三角形（梅花孔）、方形（方形孔）
- **货品类型**: 煤炭、废石、铁矿石、铜矿石
- **边界线类型**: 起爆孔线、爆破边界线

### 数据库架构 (SQLite)
- `t_project_info`: 项目元数据和地理参考
- `t_blast_area`: 爆破区域参数和配置
- `t_measure_point`: 测量点坐标和元数据
- `t_border_line`: 边界线定义
- `t_hole_info`: 生成的钻孔信息
- `t_mission_info`: 工作任务和任务管理

## 项目发展历程

### 第一阶段：基础架构
- Unity 6.1 项目基础设施
- UI Toolkit 集成
- 输入系统实现
- Cesium 3D 引擎集成

### 第二阶段：DaisyUI 组件库 (已完成)
- 完整的 DaisyUI 组件库实现 ✅
- TailwindUss 样式系统集成 ✅
- 智能键盘事件阻断系统 ✅
- 组件库可视化窗口 ✅
- 707行扩展方法 API ✅
- 完整的测试和验证框架 ✅
- 6个核心组件（Button、Input、Card、Select、Modal、Dropdown） ✅
- 组件库管理窗口和预览系统 ✅

### 第三阶段：业务系统 (待实现)
- 数据管理和数据库集成
- 项目生命周期管理
- 爆破区域和钻孔算法
- 3D 可视化和交互系统