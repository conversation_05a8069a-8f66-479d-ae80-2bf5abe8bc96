﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>SQLitePCLRaw.bundle_e_sqlite3</id>
    <version>2.1.11</version>
    <authors><PERSON></authors>
    <license type="expression">Apache-2.0</license>
    <licenseUrl>https://licenses.nuget.org/Apache-2.0</licenseUrl>
    <description>This 'batteries-included' bundle brings in SQLitePCLRaw.core and the necessary stuff for certain common use cases.  Call SQLitePCL.Batteries.Init().  Policy of this bundle: e_sqlite3 included</description>
    <copyright>Copyright 2014-2024 SourceGear, LLC</copyright>
    <tags>sqlite</tags>
    <repository type="git" url="https://github.com/ericsink/SQLitePCL.raw" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.1">
        <dependency id="SQLitePCLRaw.provider.dynamic_cdecl" version="2.1.11" exclude="Build,Analyzers" />
        <dependency id="SQLitePCLRaw.lib.e_sqlite3" version="2.1.11" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="MonoAndroid9.0">
        <dependency id="SQLitePCLRaw.lib.e_sqlite3.android" version="2.1.11" exclude="Build,Analyzers" />
        <dependency id="SQLitePCLRaw.provider.e_sqlite3" version="2.1.11" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="Xamarin.iOS1.0">
        <dependency id="SQLitePCLRaw.lib.e_sqlite3.ios" version="2.1.11" exclude="Build,Analyzers" />
        <dependency id="SQLitePCLRaw.provider.internal" version="2.1.11" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net6.0-android31.0">
        <dependency id="SQLitePCLRaw.lib.e_sqlite3.android" version="2.1.11" exclude="Build,Analyzers" />
        <dependency id="SQLitePCLRaw.provider.e_sqlite3" version="2.1.11" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net6.0-ios14.0">
        <dependency id="SQLitePCLRaw.provider.e_sqlite3" version="2.1.11" exclude="Build,Analyzers" />
        <dependency id="SQLitePCLRaw.lib.e_sqlite3" version="2.1.11" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.InteropServices.NFloat.Internal" version="6.0.1" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net6.0-ios14.2">
        <dependency id="SQLitePCLRaw.lib.e_sqlite3.ios" version="2.1.11" exclude="Build,Analyzers" />
        <dependency id="SQLitePCLRaw.provider.internal" version="2.1.11" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.InteropServices.NFloat.Internal" version="6.0.1" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net6.0-tvos10.0">
        <dependency id="SQLitePCLRaw.lib.e_sqlite3.tvos" version="2.1.11" exclude="Build,Analyzers" />
        <dependency id="SQLitePCLRaw.provider.internal" version="2.1.11" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.InteropServices.NFloat.Internal" version="6.0.1" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="SQLitePCLRaw.provider.e_sqlite3" version="2.1.11" exclude="Build,Analyzers" />
        <dependency id="SQLitePCLRaw.lib.e_sqlite3" version="2.1.11" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="Java.Interop" targetFramework="MonoAndroid9.0" />
      <frameworkAssembly assemblyName="Mono.Android" targetFramework="MonoAndroid9.0" />
      <frameworkAssembly assemblyName="mscorlib" targetFramework="MonoAndroid9.0, Xamarin.iOS1.0" />
      <frameworkAssembly assemblyName="System.Core" targetFramework="MonoAndroid9.0, Xamarin.iOS1.0" />
      <frameworkAssembly assemblyName="System" targetFramework="MonoAndroid9.0, Xamarin.iOS1.0" />
      <frameworkAssembly assemblyName="System.Numerics" targetFramework="MonoAndroid9.0, Xamarin.iOS1.0" />
      <frameworkAssembly assemblyName="System.Numerics.Vectors" targetFramework="MonoAndroid9.0, Xamarin.iOS1.0" />
      <frameworkAssembly assemblyName="System.Xml" targetFramework="MonoAndroid9.0, Xamarin.iOS1.0" />
      <frameworkAssembly assemblyName="System.Drawing.Common.dll" targetFramework="Xamarin.iOS1.0" />
      <frameworkAssembly assemblyName="Xamarin.iOS" targetFramework="Xamarin.iOS1.0" />
    </frameworkAssemblies>
  </metadata>
</package>