<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Data.Sqlite</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Data.Sqlite.Properties.Resources">
            <summary>
                This is an internal API that supports the Entity Framework Core infrastructure and not subject to
                the same compatibility standards as public APIs. It may be changed or removed without notice in
                any release. You should only use it directly in your code with extreme caution and knowing that
                doing so can result in application failures when updating to a new Entity Framework Core release.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.AmbiguousColumnName(System.Object,System.Object,System.Object)">
            <summary>
                The name '{name}' is ambiguous between columns '{column1}' and '{column2}'. Specify one using its exact case.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.AmbiguousParameterName(System.Object)">
            <summary>
                Cannot bind the value for parameter '{parameterName}' because multiple matching parameters were found in the command text. Specify the parameter name with the symbol prefix, e.g. '@{parameterName}'.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.CalledOnNullValue(System.Object)">
            <summary>
                The data is NULL at ordinal {ordinal}. This method can't be called on NULL values. Check using IsDBNull before calling.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.CallRequiresOpenConnection(System.Object)">
            <summary>
                {methodName} can only be called when the connection is open.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Sqlite.Properties.Resources.CannotStoreNaN">
            <summary>
                Cannot store 'NaN' values.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Sqlite.Properties.Resources.ConnectionStringRequiresClosedConnection">
            <summary>
                ConnectionString cannot be set when the connection is open.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.ConvertFailed(System.Object,System.Object)">
            <summary>
                Cannot convert object of type '{sourceType}' to object of type '{targetType}'.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.DataReaderClosed(System.Object)">
            <summary>
                Invalid attempt to call {operation} when reader is closed.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Sqlite.Properties.Resources.DataReaderOpen">
            <summary>
                An open reader is already associated with this command. Close it before opening a new one.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Sqlite.Properties.Resources.DefaultNativeError">
            <summary>
                For more information on this error code see https://www.sqlite.org/rescode.html
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.EncryptionNotSupported(System.Object)">
            <summary>
                You specified a password in the connection string, but the native SQLite library '{libraryName}' doesn't support encryption.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.InvalidCommandType(System.Object)">
            <summary>
                The CommandType '{commandType}' is not supported.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.InvalidEnumValue(System.Object,System.Object)">
            <summary>
                The {enumType} enumeration value, {value}, is invalid.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.InvalidIsolationLevel(System.Object)">
            <summary>
                The IsolationLevel '{isolationLevel}' is not supported.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Sqlite.Properties.Resources.InvalidOffsetAndCount">
            <summary>
                Offset and count were out of bounds for the buffer.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.InvalidParameterDirection(System.Object)">
            <summary>
                The ParameterDirection '{direction}' is not supported.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.KeywordNotSupported(System.Object)">
            <summary>
                Connection string keyword '{keyword}' is not supported. For a possible alternative, see https://go.microsoft.com/fwlink/?linkid=2142181.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.MissingParameters(System.Object)">
            <summary>
                Must add values for the following parameters: {parameters}
            </summary>
        </member>
        <member name="P:Microsoft.Data.Sqlite.Properties.Resources.NoData">
            <summary>
                No data exists for the row/column.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Sqlite.Properties.Resources.ParallelTransactionsNotSupported">
            <summary>
                SqliteConnection does not support nested transactions.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.ParameterNotFound(System.Object)">
            <summary>
                A SqliteParameter with ParameterName '{parameterName}' is not contained by this SqliteParameterCollection.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.RequiresSet(System.Object)">
            <summary>
                {propertyName} must be set.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Sqlite.Properties.Resources.ResizeNotSupported">
            <summary>
                The size of a blob may not be changed by the SqliteBlob API. Use an UPDATE command instead.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Sqlite.Properties.Resources.SeekBeforeBegin">
            <summary>
                An attempt was made to move the position before the beginning of the stream.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.SetRequiresNoOpenReader(System.Object)">
            <summary>
                An open reader is associated with this command. Close it before changing the {propertyName} property.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Sqlite.Properties.Resources.SqlBlobRequiresOpenConnection">
            <summary>
                SqliteBlob can only be used when the connection is open.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.SqliteNativeError(System.Object,System.Object)">
            <summary>
                SQLite Error {errorCode}: '{message}'.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.TooManyRestrictions(System.Object)">
            <summary>
                More restrictions were provided than the collection '{collectionName}' supports.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Sqlite.Properties.Resources.TransactionCompleted">
            <summary>
                This SqliteTransaction has completed; it is no longer usable.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Sqlite.Properties.Resources.TransactionConnectionMismatch">
            <summary>
                The transaction object is not associated with the same connection object as this command.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Sqlite.Properties.Resources.TransactionRequired">
            <summary>
                Execute requires the command to have a transaction object when the connection assigned to the command is in a pending local transaction.  The Transaction property of the command has not been initialized.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.UDFCalledWithNull(System.Object,System.Object)">
            <summary>
                The SQL function '{function}' was called with a NULL argument at ordinal {ordinal}. Create the function using a Nullable parameter or rewrite your query to avoid passing NULL.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.UnknownCollection(System.Object)">
            <summary>
                The requested collection '{collectionName}' is not defined.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.Properties.Resources.UnknownDataType(System.Object)">
            <summary>
                No mapping exists from object type {typeName} to a known managed provider native type.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Sqlite.Properties.Resources.WriteNotSupported">
            <summary>
                Stream does not support writing.
            </summary>
        </member>
        <member name="T:Microsoft.Data.Sqlite.SqliteBlob">
            <summary>
                Provides methods to access the contents of a blob.
            </summary>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/blob-io">BLOB I/O</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteBlob.#ctor(Microsoft.Data.Sqlite.SqliteConnection,System.String,System.String,System.Int64,System.Boolean)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteBlob" /> class.
            </summary>
            <param name="connection">An open connection to the database.</param>
            <param name="tableName">The name of table containing the blob.</param>
            <param name="columnName">The name of the column containing the blob.</param>
            <param name="rowid">The rowid of the row containing the blob.</param>
            <param name="readOnly">A value indicating whether the blob is read-only.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/blob-io">BLOB I/O</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteBlob.#ctor(Microsoft.Data.Sqlite.SqliteConnection,System.String,System.String,System.String,System.Int64,System.Boolean)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteBlob" /> class.
            </summary>
            <param name="connection">An open connection to the database.</param>
            <param name="databaseName">The name of the attached database containing the blob.</param>
            <param name="tableName">The name of table containing the blob.</param>
            <param name="columnName">The name of the column containing the blob.</param>
            <param name="rowid">The rowid of the row containing the blob.</param>
            <param name="readOnly">A value indicating whether the blob is read-only.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/blob-io">BLOB I/O</seealso>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteBlob.CanRead">
            <summary>
                Gets a value indicating whether the current stream supports reading.
                Always true.
            </summary>
            <value><see langword="true" /> if the stream supports reading; otherwise, <see langword="false" />. </value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteBlob.CanWrite">
            <summary>
                Gets a value indicating whether the current stream supports writing.
            </summary>
            <value><see langword="true" /> if the stream supports writing; otherwise, <see langword="false" />. </value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteBlob.CanSeek">
            <summary>
                Gets a value indicating whether the current stream supports seeking.
                Always true.
            </summary>
            <value><see langword="true" /> if the stream supports seeking; otherwise, <see langword="false" />. </value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteBlob.Length">
            <summary>
                Gets the length in bytes of the stream.
            </summary>
            <value>A long value representing the length of the stream in bytes.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteBlob.Position">
            <summary>
                Gets or sets the position within the current stream.
            </summary>
            <value>The current position within the stream.</value>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteBlob.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
                Reads a sequence of bytes from the current stream and advances the position
                within the stream by the number of bytes read.
            </summary>
            <param name="buffer">
                An array of bytes. When this method returns, the buffer contains the specified byte array
                with the values between offset and (offset + count - 1) replaced by the bytes read from the current source.
            </param>
            <param name="offset">The zero-based byte offset in buffer at which to begin storing the data read from the current stream.</param>
            <param name="count">The maximum number of bytes to be read from the current stream.</param>
            <returns>The total number of bytes read into the buffer.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteBlob.Read(System.Span{System.Byte})">
            <summary>
                Reads a sequence of bytes from the current stream and advances the position within the stream by the
                number of bytes read.
            </summary>
            <param name="buffer">
                A region of memory. When this method returns, the contents of this region are replaced by the bytes read
                from the current source.
            </param>
            <returns>
                The total number of bytes read into the buffer. This can be less than the number of bytes allocated in
                the buffer if that many bytes are not currently available, or zero (0) if the end of the stream has been
                reached.
            </returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteBlob.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
                Writes a sequence of bytes to the current stream and advances the current position
                within this stream by the number of bytes written.
            </summary>
            <param name="buffer">An array of bytes. This method copies count bytes from buffer to the current stream.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin copying bytes to the current stream.</param>
            <param name="count">The number of bytes to be written to the current stream.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteBlob.Write(System.ReadOnlySpan{System.Byte})">
            <summary>
                Writes a sequence of bytes to the current stream and advances the current position within this stream by
                the number of bytes written.
            </summary>
            <param name="buffer">
                A region of memory. This method copies the contents of this region to the current stream.
            </param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteBlob.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
                Sets the position within the current stream.
            </summary>
            <param name="offset">A byte offset relative to the origin parameter.</param>
            <param name="origin">A value indicating the reference point used to obtain the new position.</param>
            <returns>The new position within the current stream.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteBlob.Dispose(System.Boolean)">
            <summary>
                Releases any resources used by the blob and closes it.
            </summary>
            <param name="disposing">
                true to release managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.
            </param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteBlob.Flush">
            <summary>
                Clears all buffers for this stream and causes any buffered data to be written to the underlying device.
                Does nothing.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteBlob.SetLength(System.Int64)">
            <summary>
                Sets the length of the current stream. This is not supported by sqlite blobs.
                Not supported.
            </summary>
            <param name="value">The desired length of the current stream in bytes.</param>
            <exception cref="T:System.NotSupportedException">Always.</exception>
        </member>
        <member name="T:Microsoft.Data.Sqlite.SqliteCacheMode">
            <summary>
                Represents the caching modes that can be used when creating a new <see cref="T:Microsoft.Data.Sqlite.SqliteConnection" />.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Sqlite.SqliteCacheMode.Default">
            <summary>
                Default mode.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Sqlite.SqliteCacheMode.Private">
            <summary>
                Private-cache mode. Each connection uses a private cache.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Sqlite.SqliteCacheMode.Shared">
            <summary>
                Shared-cache mode. Connections share a cache. This mode can change the behavior of transaction and table
                locking.
            </summary>
            <seealso href="https://www.sqlite.org/sharedcache.html">SQLite Shared-Cache Mode</seealso>
        </member>
        <member name="T:Microsoft.Data.Sqlite.SqliteCommand">
            <summary>
                Represents a SQL statement to be executed against a SQLite database.
            </summary>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/batching">Batching</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/async">Async Limitations</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteCommand" /> class.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.#ctor(System.String)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteCommand" /> class.
            </summary>
            <param name="commandText">The SQL to execute against the database.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.#ctor(System.String,Microsoft.Data.Sqlite.SqliteConnection)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteCommand" /> class.
            </summary>
            <param name="commandText">The SQL to execute against the database.</param>
            <param name="connection">The connection used by the command.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.#ctor(System.String,Microsoft.Data.Sqlite.SqliteConnection,Microsoft.Data.Sqlite.SqliteTransaction)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteCommand" /> class.
            </summary>
            <param name="commandText">The SQL to execute against the database.</param>
            <param name="connection">The connection used by the command.</param>
            <param name="transaction">The transaction within which the command executes.</param>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteCommand.CommandType">
            <summary>
                Gets or sets a value indicating how <see cref="P:Microsoft.Data.Sqlite.SqliteCommand.CommandText" /> is interpreted. Only
                <see cref="F:System.Data.CommandType.Text" /> is supported.
            </summary>
            <value>A value indicating how <see cref="P:Microsoft.Data.Sqlite.SqliteCommand.CommandText" /> is interpreted.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteCommand.CommandText">
            <summary>
                Gets or sets the SQL to execute against the database.
            </summary>
            <value>The SQL to execute against the database.</value>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/batching">Batching</seealso>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteCommand.Connection">
            <summary>
                Gets or sets the connection used by the command.
            </summary>
            <value>The connection used by the command.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteCommand.DbConnection">
            <summary>
                Gets or sets the connection used by the command. Must be a <see cref="T:Microsoft.Data.Sqlite.SqliteConnection" />.
            </summary>
            <value>The connection used by the command.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteCommand.Transaction">
            <summary>
                Gets or sets the transaction within which the command executes.
            </summary>
            <value>The transaction within which the command executes.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteCommand.DbTransaction">
            <summary>
                Gets or sets the transaction within which the command executes. Must be a <see cref="T:Microsoft.Data.Sqlite.SqliteTransaction" />.
            </summary>
            <value>The transaction within which the command executes.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteCommand.Parameters">
            <summary>
                Gets the collection of parameters used by the command.
            </summary>
            <value>The collection of parameters used by the command.</value>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteCommand.DbParameterCollection">
            <summary>
                Gets the collection of parameters used by the command.
            </summary>
            <value>The collection of parameters used by the command.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteCommand.CommandTimeout">
            <summary>
                Gets or sets the number of seconds to wait before terminating the attempt to execute the command.
                Defaults to 30. A value of 0 means no timeout.
            </summary>
            <value>The number of seconds to wait before terminating the attempt to execute the command.</value>
            <remarks>
                The timeout is used when the command is waiting to obtain a lock on the table.
            </remarks>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteCommand.DesignTimeVisible">
            <summary>
                Gets or sets a value indicating whether the command should be visible in an interface control.
            </summary>
            <value>A value indicating whether the command should be visible in an interface control.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteCommand.UpdatedRowSource">
            <summary>
                Gets or sets a value indicating how the results are applied to the row being updated.
            </summary>
            <value>A value indicating how the results are applied to the row being updated.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteCommand.DataReader">
            <summary>
                Gets or sets the data reader currently being used by the command, or null if none.
            </summary>
            <value>The data reader currently being used by the command.</value>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.Dispose(System.Boolean)">
            <summary>
                Releases any resources used by the connection and closes it.
            </summary>
            <param name="disposing">
                <see langword="true" /> to release managed and unmanaged resources;
                <see langword="false" /> to release only unmanaged resources.
            </param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.CreateParameter">
            <summary>
                Creates a new parameter.
            </summary>
            <returns>The new parameter.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.CreateDbParameter">
            <summary>
                Creates a new parameter.
            </summary>
            <returns>The new parameter.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.Prepare">
            <summary>
                Creates a prepared version of the command on the database.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader">
            <summary>
                Executes the <see cref="P:Microsoft.Data.Sqlite.SqliteCommand.CommandText" /> against the database and returns a data reader.
            </summary>
            <returns>The data reader.</returns>
            <exception cref="T:Microsoft.Data.Sqlite.SqliteException">A SQLite error occurs during execution.</exception>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/batching">Batching</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(System.Data.CommandBehavior)">
            <summary>
                Executes the <see cref="P:Microsoft.Data.Sqlite.SqliteCommand.CommandText" /> against the database and returns a data reader.
            </summary>
            <param name="behavior">A description of the results of the query and its effect on the database.</param>
            <returns>The data reader.</returns>
            <exception cref="T:Microsoft.Data.Sqlite.SqliteException">A SQLite error occurs during execution.</exception>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/batching">Batching</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReader(System.Data.CommandBehavior)">
            <summary>
                Executes the <see cref="P:Microsoft.Data.Sqlite.SqliteCommand.CommandText" /> against the database and returns a data reader.
            </summary>
            <param name="behavior">A description of query's results and its effect on the database.</param>
            <returns>The data reader.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync">
            <summary>
                Executes the <see cref="P:Microsoft.Data.Sqlite.SqliteCommand.CommandText" /> asynchronously against the database and returns a data reader.
            </summary>
            <returns>A task representing the asynchronous operation.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/async">Async Limitations</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/batching">Batching</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(System.Threading.CancellationToken)">
            <summary>
                Executes the <see cref="P:Microsoft.Data.Sqlite.SqliteCommand.CommandText" /> asynchronously against the database and returns a data reader.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task representing the asynchronous operation.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/async">Async Limitations</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/batching">Batching</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
            <exception cref="T:System.OperationCanceledException">If the <see cref="T:System.Threading.CancellationToken"/> is canceled.</exception>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(System.Data.CommandBehavior)">
            <summary>
                Executes the <see cref="P:Microsoft.Data.Sqlite.SqliteCommand.CommandText" /> asynchronously against the database and returns a data reader.
            </summary>
            <param name="behavior">A description of query's results and its effect on the database.</param>
            <returns>A task representing the asynchronous operation.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/async">Async Limitations</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/batching">Batching</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)">
            <summary>
                Executes the <see cref="P:Microsoft.Data.Sqlite.SqliteCommand.CommandText" /> asynchronously against the database and returns a data reader.
            </summary>
            <param name="behavior">A description of query's results and its effect on the database.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task representing the asynchronous operation.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/async">Async Limitations</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/batching">Batching</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
            <exception cref="T:System.OperationCanceledException">If the <see cref="T:System.Threading.CancellationToken"/> is canceled.</exception>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)">
            <summary>
                Executes the <see cref="P:Microsoft.Data.Sqlite.SqliteCommand.CommandText" /> asynchronously against the database and returns a data reader.
            </summary>
            <param name="behavior">A description of query's results and its effect on the database.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task representing the asynchronous operation.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/async">Async Limitations</seealso>
            <exception cref="T:System.OperationCanceledException">If the <see cref="T:System.Threading.CancellationToken"/> is canceled.</exception>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.ExecuteNonQuery">
            <summary>
                Executes the <see cref="P:Microsoft.Data.Sqlite.SqliteCommand.CommandText" /> against the database.
            </summary>
            <returns>The number of rows inserted, updated, or deleted. -1 for SELECT statements.</returns>
            <exception cref="T:Microsoft.Data.Sqlite.SqliteException">A SQLite error occurs during execution.</exception>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.ExecuteScalar">
            <summary>
                Executes the <see cref="P:Microsoft.Data.Sqlite.SqliteCommand.CommandText" /> against the database and returns the result.
            </summary>
            <returns>The first column of the first row of the results, or null if no results.</returns>
            <exception cref="T:Microsoft.Data.Sqlite.SqliteException">A SQLite error occurs during execution.</exception>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteCommand.Cancel">
            <summary>
                Attempts to cancel the execution of the command. Does nothing.
            </summary>
        </member>
        <member name="T:Microsoft.Data.Sqlite.SqliteConnection">
            <summary>
                Represents a connection to a SQLite database.
            </summary>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/connection-strings">Connection Strings</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/async">Async Limitations</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``1(System.String,System.Func{``0,``0},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``2(System.String,System.Func{``1,``0,``1},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``3(System.String,System.Func{``2,``0,``1,``2},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``4(System.String,System.Func{``3,``0,``1,``2,``3},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``5(System.String,System.Func{``4,``0,``1,``2,``3,``4},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``6(System.String,System.Func{``5,``0,``1,``2,``3,``4,``5},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``7(System.String,System.Func{``6,``0,``1,``2,``3,``4,``5,``6},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``8(System.String,System.Func{``7,``0,``1,``2,``3,``4,``5,``6,``7},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``9(System.String,System.Func{``8,``0,``1,``2,``3,``4,``5,``6,``7,``8},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``10(System.String,System.Func{``9,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``11(System.String,System.Func{``10,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``12(System.String,System.Func{``11,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``13(System.String,System.Func{``12,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``14(System.String,System.Func{``13,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``15(System.String,System.Func{``14,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="T14">The type of the fourteenth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``16(System.String,System.Func{``15,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="T14">The type of the fourteenth parameter of the function.</typeparam>
            <typeparam name="T15">The type of the fifteenth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``1(System.String,System.Func{``0,System.Object[],``0},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``1(System.String,``0,System.Func{``0,``0},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``2(System.String,``1,System.Func{``1,``0,``1},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``3(System.String,``2,System.Func{``2,``0,``1,``2},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``4(System.String,``3,System.Func{``3,``0,``1,``2,``3},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``5(System.String,``4,System.Func{``4,``0,``1,``2,``3,``4},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``6(System.String,``5,System.Func{``5,``0,``1,``2,``3,``4,``5},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``7(System.String,``6,System.Func{``6,``0,``1,``2,``3,``4,``5,``6},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``8(System.String,``7,System.Func{``7,``0,``1,``2,``3,``4,``5,``6,``7},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``9(System.String,``8,System.Func{``8,``0,``1,``2,``3,``4,``5,``6,``7,``8},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``10(System.String,``9,System.Func{``9,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``11(System.String,``10,System.Func{``10,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``12(System.String,``11,System.Func{``11,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``13(System.String,``12,System.Func{``12,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``14(System.String,``13,System.Func{``13,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``15(System.String,``14,System.Func{``14,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="T14">The type of the fourteenth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``16(System.String,``15,System.Func{``15,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="T14">The type of the fourteenth parameter of the function.</typeparam>
            <typeparam name="T15">The type of the fifteenth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``1(System.String,``0,System.Func{``0,System.Object[],``0},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``2(System.String,``0,System.Func{``0,``0},System.Func{``0,``1},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``3(System.String,``1,System.Func{``1,``0,``1},System.Func{``1,``2},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``4(System.String,``2,System.Func{``2,``0,``1,``2},System.Func{``2,``3},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``5(System.String,``3,System.Func{``3,``0,``1,``2,``3},System.Func{``3,``4},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``6(System.String,``4,System.Func{``4,``0,``1,``2,``3,``4},System.Func{``4,``5},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``7(System.String,``5,System.Func{``5,``0,``1,``2,``3,``4,``5},System.Func{``5,``6},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``8(System.String,``6,System.Func{``6,``0,``1,``2,``3,``4,``5,``6},System.Func{``6,``7},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``9(System.String,``7,System.Func{``7,``0,``1,``2,``3,``4,``5,``6,``7},System.Func{``7,``8},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``10(System.String,``8,System.Func{``8,``0,``1,``2,``3,``4,``5,``6,``7,``8},System.Func{``8,``9},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``11(System.String,``9,System.Func{``9,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9},System.Func{``9,``10},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``12(System.String,``10,System.Func{``10,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10},System.Func{``10,``11},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``13(System.String,``11,System.Func{``11,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11},System.Func{``11,``12},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``14(System.String,``12,System.Func{``12,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12},System.Func{``12,``13},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``15(System.String,``13,System.Func{``13,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13},System.Func{``13,``14},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``16(System.String,``14,System.Func{``14,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14},System.Func{``14,``15},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="T14">The type of the fourteenth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``17(System.String,``15,System.Func{``15,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15},System.Func{``15,``16},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="T14">The type of the fourteenth parameter of the function.</typeparam>
            <typeparam name="T15">The type of the fifteenth parameter of the function.</typeparam>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateAggregate``2(System.String,``0,System.Func{``0,System.Object[],``0},System.Func{``0,``1},System.Boolean)">
            <summary>
                Creates or redefines an aggregate SQL function.
            </summary>
            <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="seed">The initial accumulator value.</param>
            <param name="func">An accumulator function to be invoked on each element. Pass null to delete a function.</param>
            <param name="resultSelector">
                A function to transform the final accumulator value into the result value. Pass null to
                delete a function.
            </param>
            <param name="isDeterministic">Flag indicating whether the aggregate is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``1(System.String,System.Func{``0},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``2(System.String,System.Func{``0,``1},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``3(System.String,System.Func{``0,``1,``2},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``4(System.String,System.Func{``0,``1,``2,``3},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``5(System.String,System.Func{``0,``1,``2,``3,``4},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``6(System.String,System.Func{``0,``1,``2,``3,``4,``5},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``7(System.String,System.Func{``0,``1,``2,``3,``4,``5,``6},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``8(System.String,System.Func{``0,``1,``2,``3,``4,``5,``6,``7},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``9(System.String,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``10(System.String,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``11(System.String,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``12(System.String,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``13(System.String,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``14(System.String,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``15(System.String,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="T14">The type of the fourteenth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``16(System.String,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="T14">The type of the fourteenth parameter of the function.</typeparam>
            <typeparam name="T15">The type of the fifteenth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``17(System.String,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15,``16},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="T14">The type of the fourteenth parameter of the function.</typeparam>
            <typeparam name="T15">The type of the fifteenth parameter of the function.</typeparam>
            <typeparam name="T16">The type of the sixteenth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``1(System.String,System.Func{System.Object[],``0},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``2(System.String,``0,System.Func{``0,``1},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``3(System.String,``0,System.Func{``0,``1,``2},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``4(System.String,``0,System.Func{``0,``1,``2,``3},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``5(System.String,``0,System.Func{``0,``1,``2,``3,``4},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``6(System.String,``0,System.Func{``0,``1,``2,``3,``4,``5},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``7(System.String,``0,System.Func{``0,``1,``2,``3,``4,``5,``6},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``8(System.String,``0,System.Func{``0,``1,``2,``3,``4,``5,``6,``7},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``9(System.String,``0,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``10(System.String,``0,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``11(System.String,``0,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``12(System.String,``0,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``13(System.String,``0,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``14(System.String,``0,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``15(System.String,``0,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``16(System.String,``0,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="T14">The type of the fourteenth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``17(System.String,``0,System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15,``16},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="T1">The type of the first parameter of the function.</typeparam>
            <typeparam name="T2">The type of the second parameter of the function.</typeparam>
            <typeparam name="T3">The type of the third parameter of the function.</typeparam>
            <typeparam name="T4">The type of the fourth parameter of the function.</typeparam>
            <typeparam name="T5">The type of the fifth parameter of the function.</typeparam>
            <typeparam name="T6">The type of the sixth parameter of the function.</typeparam>
            <typeparam name="T7">The type of the seventh parameter of the function.</typeparam>
            <typeparam name="T8">The type of the eighth parameter of the function.</typeparam>
            <typeparam name="T9">The type of the ninth parameter of the function.</typeparam>
            <typeparam name="T10">The type of the tenth parameter of the function.</typeparam>
            <typeparam name="T11">The type of the eleventh parameter of the function.</typeparam>
            <typeparam name="T12">The type of the twelfth parameter of the function.</typeparam>
            <typeparam name="T13">The type of the thirteenth parameter of the function.</typeparam>
            <typeparam name="T14">The type of the fourteenth parameter of the function.</typeparam>
            <typeparam name="T15">The type of the fifteenth parameter of the function.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateFunction``2(System.String,``0,System.Func{``0,System.Object[],``1},System.Boolean)">
            <summary>
                Creates or redefines a SQL function.
            </summary>
            <typeparam name="TState">The type of the state.</typeparam>
            <typeparam name="TResult">The type of the resulting value.</typeparam>
            <param name="name">The name of the SQL function.</param>
            <param name="state">An object available during each invocation of the function.</param>
            <param name="function">The function to be invoked.</param>
            <param name="isDeterministic">Flag indicating whether the function is deterministic.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/user-defined-functions">User-Defined Functions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteConnection" /> class.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.#ctor(System.String)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteConnection" /> class.
            </summary>
            <param name="connectionString">The string used to open the connection.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/connection-strings">Connection Strings</seealso>
            <seealso cref="T:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder" />
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnection.Handle">
            <summary>
                Gets a handle to underlying database connection.
            </summary>
            <value>A handle to underlying database connection.</value>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/interop">Interoperability</seealso>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnection.ConnectionString">
            <summary>
                Gets or sets a string used to open the connection.
            </summary>
            <value>A string used to open the connection.</value>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/connection-strings">Connection Strings</seealso>
            <seealso cref="T:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder" />
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnection.Database">
            <summary>
                Gets the name of the current database. Always 'main'.
            </summary>
            <value>The name of the current database.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnection.DataSource">
            <summary>
                Gets the path to the database file. Will be absolute for open connections.
            </summary>
            <value>The path to the database file.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnection.DefaultTimeout">
            <summary>
                Gets or sets the default <see cref="P:Microsoft.Data.Sqlite.SqliteCommand.CommandTimeout" /> value for commands created using
                this connection. This is also used for internal commands in methods like
                <see cref="M:Microsoft.Data.Sqlite.SqliteConnection.BeginTransaction" />.
            </summary>
            <value>The default <see cref="P:Microsoft.Data.Sqlite.SqliteCommand.CommandTimeout" /> value.</value>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnection.ServerVersion">
            <summary>
                Gets the version of SQLite used by the connection.
            </summary>
            <value>The version of SQLite used by the connection.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnection.State">
            <summary>
                Gets the current state of the connection.
            </summary>
            <value>The current state of the connection.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnection.DbProviderFactory">
            <summary>
                Gets the <see cref="P:Microsoft.Data.Sqlite.SqliteConnection.DbProviderFactory" /> for this connection.
            </summary>
            <value>The <see cref="P:Microsoft.Data.Sqlite.SqliteConnection.DbProviderFactory" />.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnection.Transaction">
            <summary>
                Gets or sets the transaction currently being used by the connection, or null if none.
            </summary>
            <value>The transaction currently being used by the connection.</value>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.ClearAllPools">
            <summary>
                Empties the connection pool.
            </summary>
            <remarks>Any open connections will not be returned to the pool when closed.</remarks>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.ClearPool(Microsoft.Data.Sqlite.SqliteConnection)">
            <summary>
                Empties the connection pool associated with the connection.
            </summary>
            <param name="connection">The connection.</param>
            <remarks>Any open connections will not be returned to the pool when closed.</remarks>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.Open">
            <summary>
                Opens a connection to the database using the value of <see cref="P:Microsoft.Data.Sqlite.SqliteConnection.ConnectionString" />. If
                <c>Mode=ReadWriteCreate</c> is used (the default) the file is created, if it doesn't already exist.
            </summary>
            <exception cref="T:Microsoft.Data.Sqlite.SqliteException">A SQLite error occurs while opening the connection.</exception>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.Close">
            <summary>
                Closes the connection to the database. Open transactions are rolled back.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.Dispose(System.Boolean)">
            <summary>
                Releases any resources used by the connection and closes it.
            </summary>
            <param name="disposing">
                <see langword="true" /> to release managed and unmanaged resources;
                <see langword="false" /> to release only unmanaged resources.
            </param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateCommand">
            <summary>
                Creates a new command associated with the connection.
            </summary>
            <returns>The new command.</returns>
            <remarks>
                The command's <see cref="P:Microsoft.Data.Sqlite.SqliteCommand.Transaction" /> property will also be set to the current
                transaction.
            </remarks>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateDbCommand">
            <summary>
                Creates a new command associated with the connection.
            </summary>
            <returns>The new command.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateCollation(System.String,System.Comparison{System.String})">
            <summary>
                Create custom collation.
            </summary>
            <param name="name">Name of the collation.</param>
            <param name="comparison">Method that compares two strings.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/collation">Collation</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.CreateCollation``1(System.String,``0,System.Func{``0,System.String,System.String,System.Int32})">
            <summary>
                Create custom collation.
            </summary>
            <typeparam name="T">The type of the state object.</typeparam>
            <param name="name">Name of the collation.</param>
            <param name="state">State object passed to each invocation of the collation.</param>
            <param name="comparison">Method that compares two strings, using additional state.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/collation">Collation</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.BeginTransaction">
            <summary>
                Begins a transaction on the connection.
            </summary>
            <returns>The transaction.</returns>
            <exception cref="T:Microsoft.Data.Sqlite.SqliteException">A SQLite error occurs during execution.</exception>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/transactions">Transactions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.BeginTransaction(System.Boolean)">
            <summary>
                Begins a transaction on the connection.
            </summary>
            <param name="deferred">
                <see langword="true" /> to defer the creation of the transaction.
                This also causes transactions to upgrade from read transactions to write transactions as needed by their commands.
            </param>
            <returns>The transaction.</returns>
            <remarks>
                Warning, commands inside a deferred transaction can fail if they cause the
                transaction to be upgraded from a read transaction to a write transaction
                but the database is locked. The application will need to retry the entire
                transaction when this happens.
            </remarks>
            <exception cref="T:Microsoft.Data.Sqlite.SqliteException">A SQLite error occurs during execution.</exception>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/transactions">Transactions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.BeginDbTransaction(System.Data.IsolationLevel)">
            <summary>
                Begins a transaction on the connection.
            </summary>
            <param name="isolationLevel">The isolation level of the transaction.</param>
            <returns>The transaction.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.BeginTransaction(System.Data.IsolationLevel)">
            <summary>
                Begins a transaction on the connection.
            </summary>
            <param name="isolationLevel">The isolation level of the transaction.</param>
            <returns>The transaction.</returns>
            <exception cref="T:Microsoft.Data.Sqlite.SqliteException">A SQLite error occurs during execution.</exception>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/transactions">Transactions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.BeginTransaction(System.Data.IsolationLevel,System.Boolean)">
            <summary>
                Begins a transaction on the connection.
            </summary>
            <param name="isolationLevel">The isolation level of the transaction.</param>
            <param name="deferred">
                <see langword="true" /> to defer the creation of the transaction.
                This also causes transactions to upgrade from read transactions to write transactions as needed by their commands.
            </param>
            <returns>The transaction.</returns>
            <remarks>
                Warning, commands inside a deferred transaction can fail if they cause the
                transaction to be upgraded from a read transaction to a write transaction
                but the database is locked. The application will need to retry the entire
                transaction when this happens.
            </remarks>
            <exception cref="T:Microsoft.Data.Sqlite.SqliteException">A SQLite error occurs during execution.</exception>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/transactions">Transactions</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.ChangeDatabase(System.String)">
            <summary>
                Changes the current database. Not supported.
            </summary>
            <param name="databaseName">The name of the database to use.</param>
            <exception cref="T:System.NotSupportedException">Always.</exception>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.EnableExtensions(System.Boolean)">
            <summary>
                Enables extension loading on the connection.
            </summary>
            <param name="enable"><see langword="true" /> to enable; <see langword="false" /> to disable.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/extensions">Extensions</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.LoadExtension(System.String,System.String)">
            <summary>
                Loads a SQLite extension library.
            </summary>
            <param name="file">The shared library containing the extension.</param>
            <param name="proc">The entry point. If null, the default entry point is used.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/extensions">Extensions</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.BackupDatabase(Microsoft.Data.Sqlite.SqliteConnection)">
            <summary>
                Backup of the connected database.
            </summary>
            <param name="destination">The destination of the backup.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/backup">Online Backup</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.BackupDatabase(Microsoft.Data.Sqlite.SqliteConnection,System.String,System.String)">
            <summary>
                Backup of the connected database.
            </summary>
            <param name="destination">The destination of the backup.</param>
            <param name="destinationName">The name of the destination database.</param>
            <param name="sourceName">The name of the source database.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/backup">Online Backup</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.GetSchema">
            <summary>
                Returns schema information for the data source of this connection.
            </summary>
            <returns>Schema information.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.GetSchema(System.String)">
            <summary>
                Returns schema information for the data source of this connection.
            </summary>
            <param name="collectionName">The name of the schema.</param>
            <returns>Schema information.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnection.GetSchema(System.String,System.String[])">
            <summary>
                Returns schema information for the data source of this connection.
            </summary>
            <param name="collectionName">The name of the schema.</param>
            <param name="restrictionValues">The restrictions.</param>
            <returns>Schema information.</returns>
        </member>
        <member name="T:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder">
            <summary>
                Provides a simple way to create and manage the contents of connection strings used by
                <see cref="T:Microsoft.Data.Sqlite.SqliteConnection" />.
            </summary>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/connection-strings">Connection Strings</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder" /> class.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.#ctor(System.String)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder" /> class.
            </summary>
            <param name="connectionString">
                The initial connection string the builder will represent. Can be null.
            </param>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.DataSource">
            <summary>
                Gets or sets the database file.
            </summary>
            <value>The database file.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.Mode">
            <summary>
                Gets or sets the connection mode.
            </summary>
            <value>The connection mode.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.Keys">
            <summary>
                Gets a collection containing the keys used by the connection string.
            </summary>
            <value>A collection containing the keys used by the connection string.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.Values">
            <summary>
                Gets a collection containing the values used by the connection string.
            </summary>
            <value>A collection containing the values used by the connection string.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.Cache">
            <summary>
                Gets or sets the caching mode used by the connection.
            </summary>
            <value>The caching mode used by the connection.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.Password">
            <summary>
                Gets or sets the encryption key. Warning, this has no effect when the native SQLite library doesn't
                support encryption. When specified, <c>PRAGMA key</c> is sent immediately after opening the connection.
            </summary>
            <value>The encryption key.</value>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/encryption">Encryption</seealso>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.ForeignKeys">
            <summary>
                Gets or sets a value indicating whether to enable foreign key constraints. When true,
                <c>PRAGMA foreign_keys = 1</c> is sent immediately after opening the connection. When false,
                <c>PRAGMA foreign_keys = 0</c> is sent. When null, no pragma is sent. There is no need to enable foreign
                keys if, like in e_sqlite3, SQLITE_DEFAULT_FOREIGN_KEYS was used to compile the native library.
            </summary>
            <value>A value indicating whether to enable foreign key constraints.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.RecursiveTriggers">
            <summary>
                Gets or sets a value indicating whether to enable recursive triggers. When true,
                <c>PRAGMA recursive_triggers</c> is sent immediately after opening the connection. When false, no pragma
                is sent.
            </summary>
            <value>A value indicating whether to enable recursive triggers.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.DefaultTimeout">
            <summary>
                Gets or sets the default <see cref="P:Microsoft.Data.Sqlite.SqliteConnection.DefaultTimeout" /> value.
            </summary>
            <value>The default <see cref="P:Microsoft.Data.Sqlite.SqliteConnection.DefaultTimeout" /> value.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.Pooling">
            <summary>
                Gets or sets a value indicating whether the connection will be pooled.
            </summary>
            <value>A value indicating whether the connection will be pooled.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.Item(System.String)">
            <summary>
                Gets or sets the value associated with the specified key.
            </summary>
            <param name="keyword">The key.</param>
            <returns>The value.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.Clear">
            <summary>
                Clears the contents of the builder.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.ContainsKey(System.String)">
            <summary>
                Determines whether the specified key is used by the connection string.
            </summary>
            <param name="keyword">The key to look for.</param>
            <returns><see langword="true" /> if it is used; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.Remove(System.String)">
            <summary>
                Removes the specified key and its value from the connection string.
            </summary>
            <param name="keyword">The key to remove.</param>
            <returns><see langword="true" /> if the key was used; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.ShouldSerialize(System.String)">
            <summary>
                Determines whether the specified key should be serialized into the connection string.
            </summary>
            <param name="keyword">The key to check.</param>
            <returns><see langword="true" /> if it should be serialized; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteConnectionStringBuilder.TryGetValue(System.String,System.Object@)">
            <summary>
                Gets the value of the specified key if it is used.
            </summary>
            <param name="keyword">The key.</param>
            <param name="value">The value.</param>
            <returns><see langword="true" /> if the key was used; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="T:Microsoft.Data.Sqlite.SqliteDataReader">
            <summary>
                Provides methods for reading the result of a command executed against a SQLite database.
            </summary>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteDataReader.Depth">
            <summary>
                Gets the depth of nesting for the current row. Always zero.
            </summary>
            <value>The depth of nesting for the current row.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteDataReader.FieldCount">
            <summary>
                Gets the number of columns in the current row.
            </summary>
            <value>The number of columns in the current row.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteDataReader.Handle">
            <summary>
                Gets a handle to underlying prepared statement.
            </summary>
            <value>A handle to underlying prepared statement.</value>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/interop">Interoperability</seealso>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteDataReader.HasRows">
            <summary>
                Gets a value indicating whether the data reader contains any rows.
            </summary>
            <value>A value indicating whether the data reader contains any rows.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteDataReader.IsClosed">
            <summary>
                Gets a value indicating whether the data reader is closed.
            </summary>
            <value>A value indicating whether the data reader is closed.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteDataReader.RecordsAffected">
            <summary>
                Gets the number of rows inserted, updated, or deleted. -1 for SELECT statements.
            </summary>
            <value>The number of rows inserted, updated, or deleted.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteDataReader.Item(System.String)">
            <summary>
                Gets the value of the specified column.
            </summary>
            <param name="name">The name of the column. The value is case-sensitive.</param>
            <returns>The value.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteDataReader.Item(System.Int32)">
            <summary>
                Gets the value of the specified column.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetEnumerator">
            <summary>
                Gets an enumerator that can be used to iterate through the rows in the data reader.
            </summary>
            <returns>The enumerator.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.Read">
            <summary>
                Advances to the next row in the result set.
            </summary>
            <returns><see langword="true" /> if there are more rows; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.NextResult">
            <summary>
                Advances to the next result set for batched statements.
            </summary>
            <returns><see langword="true" /> if there are more result sets; otherwise, <see langword="false" />.</returns>
            <exception cref="T:Microsoft.Data.Sqlite.SqliteException">A SQLite error occurs during execution.</exception>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/batching">Batching</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.Close">
            <summary>
                Closes the data reader.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.Dispose(System.Boolean)">
            <summary>
                Releases any resources used by the data reader and closes it.
            </summary>
            <param name="disposing">
                <see langword="true" /> to release managed and unmanaged resources;
                <see langword="false" /> to release only unmanaged resources.
            </param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetName(System.Int32)">
            <summary>
                Gets the name of the specified column.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The name of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetOrdinal(System.String)">
            <summary>
                Gets the ordinal of the specified column.
            </summary>
            <param name="name">The name of the column.</param>
            <returns>The zero-based column ordinal.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetDataTypeName(System.Int32)">
            <summary>
                Gets the declared data type name of the specified column. The storage class is returned for computed
                columns.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The data type name of the column.</returns>
            <remarks>Due to SQLite's dynamic type system, this may not reflect the actual type of the value.</remarks>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetFieldType(System.Int32)">
            <summary>
                Gets the data type of the specified column.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The data type of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.IsDBNull(System.Int32)">
            <summary>
                Gets a value indicating whether the specified column is <see cref="T:System.DBNull" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns><see langword="true" /> if the specified column is <see cref="T:System.DBNull" />; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetBoolean(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.Boolean" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetByte(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.Byte" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetChar(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.Char" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetDateTime(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.DateTime" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetDateTimeOffset(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.DateTimeOffset" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetTimeSpan(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.TimeSpan" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetDecimal(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.Decimal" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetDouble(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.Double" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetFloat(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.Single" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetGuid(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.Guid" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetInt16(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.Int16" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetInt32(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.Int32" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetInt64(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.Int64" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetString(System.Int32)">
            <summary>
                Gets the value of the specified column as a <see cref="T:System.String" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
            <summary>
                Reads a stream of bytes from the specified column. Not supported.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <param name="dataOffset">The index from which to begin the read operation.</param>
            <param name="buffer">The buffer into which the data is copied.</param>
            <param name="bufferOffset">The index to which the data will be copied.</param>
            <param name="length">The maximum number of bytes to read.</param>
            <returns>The actual number of bytes read.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
            <summary>
                Reads a stream of characters from the specified column. Not supported.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <param name="dataOffset">The index from which to begin the read operation.</param>
            <param name="buffer">The buffer into which the data is copied.</param>
            <param name="bufferOffset">The index to which the data will be copied.</param>
            <param name="length">The maximum number of characters to read.</param>
            <returns>The actual number of characters read.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetStream(System.Int32)">
            <summary>
                Retrieves data as a Stream. If the reader includes rowid (or any of its aliases), a
                <see cref="T:Microsoft.Data.Sqlite.SqliteBlob" /> is returned. Otherwise, the all of the data is read into memory and a
                <see cref="T:System.IO.MemoryStream" /> is returned.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The returned object.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/blob-io">BLOB I/O</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetTextReader(System.Int32)">
            <summary>
                Retrieves data as a <see cref="T:System.IO.TextReader" />.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The returned object.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetFieldValue``1(System.Int32)">
            <summary>
                Gets the value of the specified column.
            </summary>
            <typeparam name="T">The type of the value.</typeparam>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetValue(System.Int32)">
            <summary>
                Gets the value of the specified column.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the column.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetValues(System.Object[])">
            <summary>
                Gets the column values of the current row.
            </summary>
            <param name="values">An array into which the values are copied.</param>
            <returns>The number of values copied into the array.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteDataReader.GetSchemaTable">
            <summary>
                Returns a System.Data.DataTable that describes the column metadata of the System.Data.Common.DbDataReader.
            </summary>
            <returns>A System.Data.DataTable that describes the column metadata.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/metadata">Metadata</seealso>
        </member>
        <member name="T:Microsoft.Data.Sqlite.SqliteException">
            <summary>
                Represents a SQLite error.
            </summary>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/database-errors">Database Errors</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteException.#ctor(System.String,System.Int32)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteException" /> class.
            </summary>
            <param name="message">The message to display for the exception. Can be null.</param>
            <param name="errorCode">The SQLite error code.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteException.#ctor(System.String,System.Int32,System.Int32)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteException" /> class.
            </summary>
            <param name="message">The message to display for the exception. Can be null.</param>
            <param name="errorCode">The SQLite error code.</param>
            <param name="extendedErrorCode">The extended SQLite error code.</param>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteException.SqliteErrorCode">
            <summary>
                Gets the SQLite error code.
            </summary>
            <value>The SQLite error code.</value>
            <seealso href="https://www.sqlite.org/rescode.html">SQLite Result Codes</seealso>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteException.SqliteExtendedErrorCode">
            <summary>
                Gets the extended SQLite error code.
            </summary>
            <value>The SQLite error code.</value>
            <seealso href="https://www.sqlite.org/rescode.html#extrc">SQLite Result Codes</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(System.Int32,SQLitePCL.sqlite3)">
            <summary>
                Throws an exception with a specific SQLite error code value.
            </summary>
            <param name="rc">The SQLite error code corresponding to the desired exception.</param>
            <param name="db">A handle to database connection.</param>
            <remarks>
                No exception is thrown for non-error result codes.
            </remarks>
        </member>
        <member name="T:Microsoft.Data.Sqlite.SqliteFactory">
            <summary>
                Creates instances of various Microsoft.Data.Sqlite classes.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Sqlite.SqliteFactory.Instance">
            <summary>
                The singleton instance.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteFactory.CreateCommand">
            <summary>
                Creates a new command.
            </summary>
            <returns>The new command.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteFactory.CreateConnection">
            <summary>
                Creates a new connection.
            </summary>
            <returns>The new connection.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteFactory.CreateConnectionStringBuilder">
            <summary>
                Creates a new connection string builder.
            </summary>
            <returns>The new connection string builder.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteFactory.CreateParameter">
            <summary>
                Creates a new parameter.
            </summary>
            <returns>The new parameter.</returns>
        </member>
        <member name="T:Microsoft.Data.Sqlite.SqliteOpenMode">
            <summary>
                Represents the connection modes that can be used when opening a connection.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Sqlite.SqliteOpenMode.ReadWriteCreate">
            <summary>
                Opens the database for reading and writing, and creates it if it doesn't exist.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Sqlite.SqliteOpenMode.ReadWrite">
            <summary>
                Opens the database for reading and writing.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Sqlite.SqliteOpenMode.ReadOnly">
            <summary>
                Opens the database in read-only mode.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Sqlite.SqliteOpenMode.Memory">
            <summary>
                Opens an in-memory database.
            </summary>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/in-memory-databases">In-Memory Databases</seealso>
        </member>
        <member name="T:Microsoft.Data.Sqlite.SqliteParameter">
            <summary>
                Represents a parameter and its value in a <see cref="T:Microsoft.Data.Sqlite.SqliteCommand" />.
            </summary>
            <remarks>Due to SQLite's dynamic type system, parameter values are not converted.</remarks>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameter.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteParameter" /> class.
            </summary>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameter.#ctor(System.String,System.Object)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteParameter" /> class.
            </summary>
            <param name="name">The name of the parameter.</param>
            <param name="value">The value of the parameter. Can be null.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameter.#ctor(System.String,Microsoft.Data.Sqlite.SqliteType)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteParameter" /> class.
            </summary>
            <param name="name">The name of the parameter.</param>
            <param name="type">The type of the parameter.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameter.#ctor(System.String,Microsoft.Data.Sqlite.SqliteType,System.Int32)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteParameter" /> class.
            </summary>
            <param name="name">The name of the parameter.</param>
            <param name="type">The type of the parameter.</param>
            <param name="size">The maximum size, in bytes, of the parameter.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameter.#ctor(System.String,Microsoft.Data.Sqlite.SqliteType,System.Int32,System.String)">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteParameter" /> class.
            </summary>
            <param name="name">The name of the parameter.</param>
            <param name="type">The type of the parameter.</param>
            <param name="size">The maximum size, in bytes, of the parameter.</param>
            <param name="sourceColumn">The source column used for loading the value. Can be null.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteParameter.DbType">
            <summary>
                Gets or sets the type of the parameter.
            </summary>
            <value>The type of the parameter.</value>
            <remarks>Due to SQLite's dynamic type system, parameter values are not converted.</remarks>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteParameter.SqliteType">
            <summary>
                Gets or sets the SQLite type of the parameter.
            </summary>
            <value>The SQLite type of the parameter.</value>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteParameter.Direction">
            <summary>
                Gets or sets the direction of the parameter. Only <see cref="F:System.Data.ParameterDirection.Input" /> is supported.
            </summary>
            <value>The direction of the parameter.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteParameter.IsNullable">
            <summary>
                Gets or sets a value indicating whether the parameter is nullable.
            </summary>
            <value>A value indicating whether the parameter is nullable.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteParameter.ParameterName">
            <summary>
                Gets or sets the name of the parameter.
            </summary>
            <value>The name of the parameter.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteParameter.Size">
            <summary>
                Gets or sets the maximum size, in bytes, of the parameter.
            </summary>
            <value>The maximum size, in bytes, of the parameter.</value>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteParameter.SourceColumn">
            <summary>
                Gets or sets the source column used for loading the value.
            </summary>
            <value>The source column used for loading the value.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteParameter.SourceColumnNullMapping">
            <summary>
                Gets or sets a value indicating whether the source column is nullable.
            </summary>
            <value>A value indicating whether the source column is nullable.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteParameter.Value">
            <summary>
                Gets or sets the value of the parameter.
            </summary>
            <value>The value of the parameter.</value>
            <remarks>Due to SQLite's dynamic type system, parameter values are not converted.</remarks>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameter.ResetDbType">
            <summary>
                Resets the <see cref="P:Microsoft.Data.Sqlite.SqliteParameter.DbType" /> property to its original value.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameter.ResetSqliteType">
            <summary>
                Resets the <see cref="P:Microsoft.Data.Sqlite.SqliteParameter.SqliteType" /> property to its original value.
            </summary>
        </member>
        <member name="T:Microsoft.Data.Sqlite.SqliteParameterCollection">
            <summary>
                Represents a collection of SQLite parameters.
            </summary>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:Microsoft.Data.Sqlite.SqliteParameterCollection" /> class.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteParameterCollection.Count">
            <summary>
                Gets the number of items in the collection.
            </summary>
            <value>The number of items in the collection.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteParameterCollection.SyncRoot">
            <summary>
                Gets the object used to synchronize access to the collection.
            </summary>
            <value>The object used to synchronize access to the collection.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteParameterCollection.Item(System.Int32)">
            <summary>
                Gets or sets the parameter at the specified index.
            </summary>
            <param name="index">The zero-based index of the parameter.</param>
            <returns>The parameter.</returns>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteParameterCollection.Item(System.String)">
            <summary>
                Gets or sets the parameter with the specified name.
            </summary>
            <param name="parameterName">The name of the parameter.</param>
            <returns>The parameter.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.Add(System.Object)">
            <summary>
                Adds a parameter to the collection.
            </summary>
            <param name="value">The parameter to add. Must be a <see cref="T:Microsoft.Data.Sqlite.SqliteParameter" />.</param>
            <returns>The zero-based index of the parameter that was added.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.Add(Microsoft.Data.Sqlite.SqliteParameter)">
            <summary>
                Adds a parameter to the collection.
            </summary>
            <param name="value">The parameter to add.</param>
            <returns>The parameter that was added.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.Add(System.String,Microsoft.Data.Sqlite.SqliteType)">
            <summary>
                Adds a parameter to the collection.
            </summary>
            <param name="parameterName">The name of the parameter.</param>
            <param name="type">The SQLite type of the parameter.</param>
            <returns>The parameter that was added.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.Add(System.String,Microsoft.Data.Sqlite.SqliteType,System.Int32)">
            <summary>
                Adds a parameter to the collection.
            </summary>
            <param name="parameterName">The name of the parameter.</param>
            <param name="type">The SQLite type of the parameter.</param>
            <param name="size">The maximum size, in bytes, of the parameter.</param>
            <returns>The parameter that was added.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.Add(System.String,Microsoft.Data.Sqlite.SqliteType,System.Int32,System.String)">
            <summary>
                Adds a parameter to the collection.
            </summary>
            <param name="parameterName">The name of the parameter.</param>
            <param name="type">The SQLite type of the parameter.</param>
            <param name="size">The maximum size, in bytes, of the parameter.</param>
            <param name="sourceColumn">
                The source column used for loading the value of the parameter. Can be null.
            </param>
            <returns>The parameter that was added.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.AddRange(System.Array)">
            <summary>
                Adds multiple parameters to the collection.
            </summary>
            <param name="values">
                An array of parameters to add. They must be <see cref="T:Microsoft.Data.Sqlite.SqliteParameter" /> objects.
            </param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.AddRange(System.Collections.Generic.IEnumerable{Microsoft.Data.Sqlite.SqliteParameter})">
            <summary>
                Adds multiple parameters to the collection.
            </summary>
            <param name="values">The parameters to add.</param>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.AddWithValue(System.String,System.Object)">
            <summary>
                Adds a parameter to the collection.
            </summary>
            <param name="parameterName">The name of the parameter.</param>
            <param name="value">The value of the parameter. Can be null.</param>
            <returns>The parameter that was added.</returns>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/parameters">Parameters</seealso>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.Clear">
            <summary>
                Removes all parameters from the collection.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.Contains(System.Object)">
            <summary>
                Gets a value indicating whether the collection contains the specified parameter.
            </summary>
            <param name="value">The parameter to look for. Must be a <see cref="T:Microsoft.Data.Sqlite.SqliteParameter" />.</param>
            <returns><see langword="true" /> if the collection contains the parameter; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.Contains(Microsoft.Data.Sqlite.SqliteParameter)">
            <summary>
                Gets a value indicating whether the collection contains the specified parameter.
            </summary>
            <param name="value">The parameter to look for.</param>
            <returns><see langword="true" /> if the collection contains the parameter; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.Contains(System.String)">
            <summary>
                Gets a value indicating whether the collection contains a parameter with the specified name.
            </summary>
            <param name="value">The name of the parameter.</param>
            <returns><see langword="true" /> if the collection contains the parameter; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.CopyTo(System.Array,System.Int32)">
            <summary>
                Copies the collection to an array of parameters.
            </summary>
            <param name="array">
                The array into which the parameters are copied. Must be an array of <see cref="T:Microsoft.Data.Sqlite.SqliteParameter" /> objects.
            </param>
            <param name="index">The zero-based index to which the parameters are copied.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.CopyTo(Microsoft.Data.Sqlite.SqliteParameter[],System.Int32)">
            <summary>
                Copies the collection to an array of parameters.
            </summary>
            <param name="array">The array into which the parameters are copied.</param>
            <param name="index">The zero-based index to which the parameters are copied.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.GetEnumerator">
            <summary>
                Gets an enumerator that iterates through the collection.
            </summary>
            <returns>The enumerator.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.GetParameter(System.Int32)">
            <summary>
                Gets a parameter at the specified index.
            </summary>
            <param name="index">The zero-based index of the parameter.</param>
            <returns>The parameter.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.GetParameter(System.String)">
            <summary>
                Gets a parameter with the specified name.
            </summary>
            <param name="parameterName">The name of the parameter.</param>
            <returns>The parameter.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.IndexOf(System.Object)">
            <summary>
                Gets the index of the specified parameter.
            </summary>
            <param name="value">The parameter. Must be a <see cref="T:Microsoft.Data.Sqlite.SqliteParameter" />.</param>
            <returns>The zero-based index of the parameter.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.IndexOf(Microsoft.Data.Sqlite.SqliteParameter)">
            <summary>
                Gets the index of the specified parameter.
            </summary>
            <param name="value">The parameter.</param>
            <returns>The zero-based index of the parameter.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.IndexOf(System.String)">
            <summary>
                Gets the index of the parameter with the specified name.
            </summary>
            <param name="parameterName">The name of the parameter.</param>
            <returns>The zero-based index of the parameter or -1 if not found.</returns>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.Insert(System.Int32,System.Object)">
            <summary>
                Inserts a parameter into the collection at the specified index.
            </summary>
            <param name="index">The zero-based index at which the parameter should be inserted.</param>
            <param name="value">The parameter to insert. Must be a <see cref="T:Microsoft.Data.Sqlite.SqliteParameter" />.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.Insert(System.Int32,Microsoft.Data.Sqlite.SqliteParameter)">
            <summary>
                Inserts a parameter into the collection at the specified index.
            </summary>
            <param name="index">The zero-based index at which the parameter should be inserted.</param>
            <param name="value">The parameter to insert.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.Remove(System.Object)">
            <summary>
                Removes a parameter from the collection.
            </summary>
            <param name="value">The parameter to remove. Must be a <see cref="T:Microsoft.Data.Sqlite.SqliteParameter" />.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.Remove(Microsoft.Data.Sqlite.SqliteParameter)">
            <summary>
                Removes a parameter from the collection.
            </summary>
            <param name="value">The parameter to remove.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.RemoveAt(System.Int32)">
            <summary>
                Removes a parameter from the collection at the specified index.
            </summary>
            <param name="index">The zero-based index of the parameter to remove.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.RemoveAt(System.String)">
            <summary>
                Removes a parameter with the specified name from the collection.
            </summary>
            <param name="parameterName">The name of the parameter to remove.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.SetParameter(System.Int32,System.Data.Common.DbParameter)">
            <summary>
                Sets the parameter at the specified index.
            </summary>
            <param name="index">The zero-based index of the parameter to set.</param>
            <param name="value">The parameter. Must be a <see cref="T:Microsoft.Data.Sqlite.SqliteParameter" />.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteParameterCollection.SetParameter(System.String,System.Data.Common.DbParameter)">
            <summary>
                Sets the parameter with the specified name.
            </summary>
            <param name="parameterName">The name of the parameter to set.</param>
            <param name="value">The parameter. Must be a <see cref="T:Microsoft.Data.Sqlite.SqliteParameter" />.</param>
        </member>
        <member name="T:Microsoft.Data.Sqlite.SqliteTransaction">
            <summary>
                Represents a transaction made against a SQLite database.
            </summary>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/transactions">Transactions</seealso>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteTransaction.Connection">
            <summary>
                Gets the connection associated with the transaction.
            </summary>
            <value>The connection associated with the transaction.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteTransaction.DbConnection">
            <summary>
                Gets the connection associated with the transaction.
            </summary>
            <value>The connection associated with the transaction.</value>
        </member>
        <member name="P:Microsoft.Data.Sqlite.SqliteTransaction.IsolationLevel">
            <summary>
                Gets the isolation level for the transaction.
            </summary>
            <value>The isolation level for the transaction.</value>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteTransaction.Commit">
            <summary>
                Applies the changes made in the transaction.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteTransaction.Rollback">
            <summary>
                Reverts the changes made in the transaction.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteTransaction.Save(System.String)">
            <summary>
            Creates a savepoint in the transaction. This allows all commands that are executed after the savepoint was
            established to be rolled back, restoring the transaction state to what it was at the time of the savepoint.
            </summary>
            <param name="savepointName">The name of the savepoint to be created.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteTransaction.Rollback(System.String)">
            <summary>
            Rolls back all commands that were executed after the specified savepoint was established.
            </summary>
            <param name="savepointName">The name of the savepoint to roll back to.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteTransaction.Release(System.String)">
            <summary>
            Destroys a savepoint previously defined in the current transaction. This allows the system to
            reclaim some resources before the transaction ends.
            </summary>
            <param name="savepointName">The name of the savepoint to release.</param>
        </member>
        <member name="M:Microsoft.Data.Sqlite.SqliteTransaction.Dispose(System.Boolean)">
            <summary>
                Releases any resources used by the transaction and rolls it back.
            </summary>
            <param name="disposing">
                <see langword="true" /> to release managed and unmanaged resources;
                <see langword="false" /> to release only unmanaged resources.
            </param>
        </member>
        <member name="T:Microsoft.Data.Sqlite.SqliteType">
            <summary>
                Represents the type affinities used by columns in SQLite tables.
            </summary>
            <seealso href="https://docs.microsoft.com/dotnet/standard/data/sqlite/types">Data Types</seealso>
        </member>
        <member name="F:Microsoft.Data.Sqlite.SqliteType.Integer">
            <summary>
                A signed integer.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Sqlite.SqliteType.Real">
            <summary>
                A floating point value.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Sqlite.SqliteType.Text">
            <summary>
                A text string.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Sqlite.SqliteType.Blob">
            <summary>
                A blob of data.
            </summary>
        </member>
    </members>
</doc>
