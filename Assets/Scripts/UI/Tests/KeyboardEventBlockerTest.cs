using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Core;
using BlastingDesign.UI.DaisyUI.Components.DataInput;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.Tests
{
    /// <summary>
    /// 键盘事件阻断测试脚本
    /// 用于验证输入框焦点时是否正确阻断键盘事件传播到场景
    /// </summary>
    public class KeyboardEventBlockerTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool enableDebugLogging = true;
        [SerializeField] private bool testDaisyInput = true;
        [SerializeField] private bool testRegularTextField = true;

        private UIDocument uiDocument;
        private VisualElement root;
        private DaisyInput daisyInput;
        private TextField regularTextField;
        private Label statusLabel;

        private void Start()
        {
            SetupTestUI();
        }

        private void SetupTestUI()
        {
            // 创建UI Document
            uiDocument = gameObject.AddComponent<UIDocument>();

            // 创建根元素
            root = new VisualElement();
            root.style.flexGrow = 1;
            root.style.backgroundColor = new Color(0.1f, 0.1f, 0.1f, 0.8f);

            // 创建标题
            var title = new Label("键盘事件阻断测试");
            title.style.fontSize = 20;
            title.style.color = Color.white;
            title.style.marginBottom = 20;
            root.Add(title);

            // 创建说明文本
            var instructions = new Label("测试步骤：\n1. 点击输入框获得焦点\n2. 按下WASD键\n3. 观察相机是否被控制移动\n4. 点击输入框外部失去焦点\n5. 再次按下WASD键\n6. 观察相机是否恢复控制");
            instructions.style.color = Color.white;
            instructions.style.marginBottom = 20;
            instructions.style.whiteSpace = WhiteSpace.Normal;
            root.Add(instructions);

            // 状态标签
            statusLabel = new Label("状态：等待测试");
            statusLabel.style.color = Color.green;
            statusLabel.style.marginBottom = 20;
            root.Add(statusLabel);

            if (testDaisyInput)
            {
                SetupDaisyInputTest();
            }

            if (testRegularTextField)
            {
                SetupRegularTextFieldTest();
            }

            // 设置UI Document的根元素
            uiDocument.rootVisualElement.Add(root);
        }

        private void SetupDaisyInputTest()
        {
            var daisyGroup = new VisualElement();
            daisyGroup.style.marginBottom = 20;
            daisyGroup.style.paddingTop = 10;
            daisyGroup.style.paddingBottom = 10;
            daisyGroup.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 0.5f);

            var daisyTitle = new Label("DaisyInput 测试");
            daisyTitle.style.fontSize = 16;
            daisyTitle.style.color = Color.cyan;
            daisyTitle.style.marginBottom = 10;
            daisyGroup.Add(daisyTitle);

            // 创建DaisyInput
            daisyInput = DaisyInput.Create("测试DaisyInput的键盘事件阻断...");
            daisyInput.style.marginBottom = 10;
            daisyInput.style.minWidth = 300;
            daisyGroup.Add(daisyInput);

            // 添加值变化监听
            daisyInput.OnValueChanged(value =>
            {
                statusLabel.text = $"DaisyInput值变化: {value}";
                if (enableDebugLogging)
                {
                    Logging.LogInfo("KeyboardEventBlockerTest", $"DaisyInput值变化: {value}");
                }
            });

            root.Add(daisyGroup);
        }

        private void SetupRegularTextFieldTest()
        {
            var regularGroup = new VisualElement();
            regularGroup.style.marginBottom = 20;
            regularGroup.style.paddingTop = 10;
            regularGroup.style.paddingBottom = 10;
            regularGroup.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 0.5f);

            var regularTitle = new Label("普通 TextField 测试");
            regularTitle.style.fontSize = 16;
            regularTitle.style.color = Color.yellow;
            regularTitle.style.marginBottom = 10;
            regularGroup.Add(regularTitle);

            // 创建普通TextField并添加键盘事件阻断
            regularTextField = new TextField("测试普通TextField的键盘事件阻断...");
            regularTextField.AddFocusBasedKeyboardEventBlocker(enableDebugLogging);
            regularTextField.style.marginBottom = 10;
            regularTextField.style.minWidth = 300;
            regularGroup.Add(regularTextField);

            // 添加值变化监听
            regularTextField.RegisterValueChangedCallback(evt =>
            {
                statusLabel.text = $"普通TextField值变化: {evt.newValue}";
                if (enableDebugLogging)
                {
                    Logging.LogInfo("KeyboardEventBlockerTest", $"普通TextField值变化: {evt.newValue}");
                }
            });

            root.Add(regularGroup);
        }

        private void Update()
        {
            // 监听键盘输入以显示状态
            if (Input.inputString != "")
            {
                string inputStr = Input.inputString;
                bool isWASD = inputStr.Contains("w") || inputStr.Contains("a") || inputStr.Contains("s") || inputStr.Contains("d");

                if (isWASD)
                {
                    statusLabel.text = $"检测到WASD键输入: {inputStr}";
                    if (enableDebugLogging)
                    {
                        Logging.LogInfo("KeyboardEventBlockerTest", $"检测到WASD键输入: {inputStr}");
                    }
                }
            }
        }
    }
}