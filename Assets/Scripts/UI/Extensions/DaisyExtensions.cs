using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.Extensions
{
    /// <summary>
    /// DaisyUI扩展方法集合
    /// 为VisualElement添加DaisyUI相关的扩展功能
    /// </summary>
    public static class DaisyExtensions
    {
        #region 链式调用扩展

        /// <summary>
        /// 执行自定义操作（通用扩展）
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="action">操作</param>
        /// <returns>元素自身</returns>
        public static T With<T>(this T element, System.Action<T> action) where T : VisualElement
        {
            action?.Invoke(element);
            return element;
        }

        /// <summary>
        /// 条件执行操作
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="condition">条件</param>
        /// <param name="action">操作</param>
        /// <returns>元素自身</returns>
        public static T When<T>(this T element, bool condition, System.Action<T> action) where T : VisualElement
        {
            if (condition)
            {
                action?.Invoke(element);
            }
            return element;
        }

        /// <summary>
        /// 条件执行操作（带else分支）
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="condition">条件</param>
        /// <param name="trueAction">条件为真时的操作</param>
        /// <param name="falseAction">条件为假时的操作</param>
        /// <returns>元素自身</returns>
        public static T When<T>(this T element, bool condition,
            System.Action<T> trueAction,
            System.Action<T> falseAction) where T : VisualElement
        {
            if (condition)
            {
                trueAction?.Invoke(element);
            }
            else
            {
                falseAction?.Invoke(element);
            }
            return element;
        }

        #endregion

        #region 样式扩展

        /// <summary>
        /// 添加DaisyUI类名
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="className">类名（不包含daisy-前缀）</param>
        /// <returns>元素自身</returns>
        public static T AddDaisyClass<T>(this T element, string className) where T : VisualElement
        {
            if (!string.IsNullOrEmpty(className))
            {
                element.AddToClassList($"daisy-{className}");
            }
            return element;
        }

        /// <summary>
        /// 移除DaisyUI类名
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="className">类名（不包含daisy-前缀）</param>
        /// <returns>元素自身</returns>
        public static T RemoveDaisyClass<T>(this T element, string className) where T : VisualElement
        {
            if (!string.IsNullOrEmpty(className))
            {
                element.RemoveFromClassList($"daisy-{className}");
            }
            return element;
        }

        /// <summary>
        /// 切换DaisyUI类名
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="className">类名（不包含daisy-前缀）</param>
        /// <param name="enabled">是否启用</param>
        /// <returns>元素自身</returns>
        public static T ToggleDaisyClass<T>(this T element, string className, bool? enabled = null) where T : VisualElement
        {
            if (string.IsNullOrEmpty(className)) return element;

            var fullClassName = $"daisy-{className}";

            if (enabled.HasValue)
            {
                if (enabled.Value)
                    element.AddToClassList(fullClassName);
                else
                    element.RemoveFromClassList(fullClassName);
            }
            else
            {
                if (element.ClassListContains(fullClassName))
                    element.RemoveFromClassList(fullClassName);
                else
                    element.AddToClassList(fullClassName);
            }

            return element;
        }

        /// <summary>
        /// 添加CSS类名
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="className">完整类名</param>
        /// <returns>元素自身</returns>
        public static T AddClass<T>(this T element, string className) where T : VisualElement
        {
            if (!string.IsNullOrEmpty(className))
            {
                element.AddToClassList(className);
            }
            return element;
        }

        /// <summary>
        /// 移除CSS类名
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="className">完整类名</param>
        /// <returns>元素自身</returns>
        public static T RemoveClass<T>(this T element, string className) where T : VisualElement
        {
            if (!string.IsNullOrEmpty(className))
            {
                element.RemoveFromClassList(className);
            }
            return element;
        }

        /// <summary>
        /// 切换CSS类名
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="className">完整类名</param>
        /// <param name="enabled">是否启用</param>
        /// <returns>元素自身</returns>
        public static T ToggleClass<T>(this T element, string className, bool? enabled = null) where T : VisualElement
        {
            if (string.IsNullOrEmpty(className)) return element;

            if (enabled.HasValue)
            {
                if (enabled.Value)
                    element.AddToClassList(className);
                else
                    element.RemoveFromClassList(className);
            }
            else
            {
                if (element.ClassListContains(className))
                    element.RemoveFromClassList(className);
                else
                    element.AddToClassList(className);
            }

            return element;
        }

        /// <summary>
        /// 批量添加CSS类名
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="classNames">类名数组</param>
        /// <returns>元素自身</returns>
        public static T AddClasses<T>(this T element, params string[] classNames) where T : VisualElement
        {
            if (classNames != null)
            {
                foreach (var className in classNames)
                {
                    if (!string.IsNullOrEmpty(className))
                    {
                        element.AddToClassList(className);
                    }
                }
            }
            return element;
        }

        /// <summary>
        /// 批量移除CSS类名
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="classNames">类名数组</param>
        /// <returns>元素自身</returns>
        public static T RemoveClasses<T>(this T element, params string[] classNames) where T : VisualElement
        {
            if (classNames != null)
            {
                foreach (var className in classNames)
                {
                    if (!string.IsNullOrEmpty(className))
                    {
                        element.RemoveFromClassList(className);
                    }
                }
            }
            return element;
        }

        #endregion

        #region 主题扩展

        /// <summary>
        /// 应用DaisyUI主题
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="theme">主题</param>
        /// <returns>元素自身</returns>
        public static T WithDaisyTheme<T>(this T element, DaisyTheme theme) where T : VisualElement
        {
            if (theme != null)
            {
                theme.Apply(element);
            }
            return element;
        }

        /// <summary>
        /// 应用当前活跃主题
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <returns>元素自身</returns>
        public static T WithCurrentTheme<T>(this T element) where T : VisualElement
        {
            if (DaisyTheme.Current != null)
            {
                DaisyTheme.Current.Apply(element);
            }
            return element;
        }

        /// <summary>
        /// 切换暗色主题
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="isDark">是否暗色</param>
        /// <returns>元素自身</returns>
        public static T ToggleDarkTheme<T>(this T element, bool isDark = true) where T : VisualElement
        {
            element.ToggleClass("theme-dark", isDark);
            element.ToggleClass("theme-light", !isDark);
            return element;
        }

        #endregion

        #region 尺寸和变体扩展

        /// <summary>
        /// 设置DaisyUI组件尺寸
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="size">尺寸</param>
        /// <returns>元素自身</returns>
        public static T WithSize<T>(this T element, string size) where T : VisualElement
        {
            // 移除现有尺寸类名
            element.RemoveClasses(
                "daisy-xs", "daisy-sm", "daisy-md", "daisy-lg", "daisy-xl",
                "daisy-btn-xs", "daisy-btn-sm", "daisy-btn-md", "daisy-btn-lg", "daisy-btn-xl",
                "daisy-input-xs", "daisy-input-sm", "daisy-input-md", "daisy-input-lg", "daisy-input-xl"
            );

            // 添加新的尺寸类名
            if (!string.IsNullOrEmpty(size))
            {
                element.AddDaisyClass(size);
            }

            return element;
        }

        /// <summary>
        /// 设置DaisyUI组件变体
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="variant">变体</param>
        /// <returns>元素自身</returns>
        public static T WithVariant<T>(this T element, string variant) where T : VisualElement
        {
            // 移除现有变体类名
            element.RemoveClasses(
                "daisy-primary", "daisy-secondary", "daisy-accent", "daisy-neutral",
                "daisy-ghost", "daisy-link", "daisy-info", "daisy-success", "daisy-warning", "daisy-error",
                "daisy-btn-primary", "daisy-btn-secondary", "daisy-btn-accent", "daisy-btn-neutral",
                "daisy-btn-ghost", "daisy-btn-link", "daisy-btn-info", "daisy-btn-success", "daisy-btn-warning", "daisy-btn-error"
            );

            // 添加新的变体类名
            if (!string.IsNullOrEmpty(variant))
            {
                element.AddDaisyClass(variant);
            }

            return element;
        }

        #endregion

        #region 动画扩展

        /// <summary>
        /// 添加DaisyUI动画类
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="animationClass">动画类名</param>
        /// <returns>元素自身</returns>
        public static T WithAnimation<T>(this T element, string animationClass) where T : VisualElement
        {
            if (!string.IsNullOrEmpty(animationClass))
            {
                element.AddToClassList($"daisy-animate-{animationClass}");
            }
            return element;
        }

        /// <summary>
        /// 添加淡入动画
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <returns>元素自身</returns>
        public static T WithFadeIn<T>(this T element) where T : VisualElement
        {
            return element.WithAnimation("fade-in");
        }

        /// <summary>
        /// 添加淡出动画
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <returns>元素自身</returns>
        public static T WithFadeOut<T>(this T element) where T : VisualElement
        {
            return element.WithAnimation("fade-out");
        }

        /// <summary>
        /// 添加滑入动画
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="direction">方向</param>
        /// <returns>元素自身</returns>
        public static T WithSlideIn<T>(this T element, string direction = "up") where T : VisualElement
        {
            return element.WithAnimation($"slide-in-{direction}");
        }

        /// <summary>
        /// 添加缩放动画
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <returns>元素自身</returns>
        public static T WithScale<T>(this T element) where T : VisualElement
        {
            return element.WithAnimation("scale");
        }

        #endregion

        #region 间距扩展

        /// <summary>
        /// 设置间距
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="spacing">间距级别</param>
        /// <returns>元素自身</returns>
        public static T WithSpacing<T>(this T element, int spacing) where T : VisualElement
        {
            element.AddDaisyClass($"gap-{spacing}");
            return element;
        }

        /// <summary>
        /// 设置内边距
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="padding">内边距级别</param>
        /// <returns>元素自身</returns>
        public static T WithPadding<T>(this T element, int padding) where T : VisualElement
        {
            element.AddDaisyClass($"p-{padding}");
            return element;
        }

        /// <summary>
        /// 设置外边距
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="margin">外边距级别</param>
        /// <returns>元素自身</returns>
        public static T WithMargin<T>(this T element, int margin) where T : VisualElement
        {
            element.AddDaisyClass($"m-{margin}");
            return element;
        }

        #endregion

        #region 响应式扩展

        /// <summary>
        /// 添加响应式类名
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="breakpoint">断点</param>
        /// <param name="className">类名</param>
        /// <returns>元素自身</returns>
        public static T WithResponsive<T>(this T element, string breakpoint, string className) where T : VisualElement
        {
            if (!string.IsNullOrEmpty(breakpoint) && !string.IsNullOrEmpty(className))
            {
                element.AddToClassList($"daisy-{breakpoint}-{className}");
            }
            return element;
        }

        /// <summary>
        /// 添加小屏幕样式
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="className">类名</param>
        /// <returns>元素自身</returns>
        public static T OnSmallScreen<T>(this T element, string className) where T : VisualElement
        {
            return element.WithResponsive("sm", className);
        }

        /// <summary>
        /// 添加中等屏幕样式
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="className">类名</param>
        /// <returns>元素自身</returns>
        public static T OnMediumScreen<T>(this T element, string className) where T : VisualElement
        {
            return element.WithResponsive("md", className);
        }

        /// <summary>
        /// 添加大屏幕样式
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="className">类名</param>
        /// <returns>元素自身</returns>
        public static T OnLargeScreen<T>(this T element, string className) where T : VisualElement
        {
            return element.WithResponsive("lg", className);
        }

        #endregion

        #region 条件样式扩展

        /// <summary>
        /// 条件添加类名
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="condition">条件</param>
        /// <param name="className">类名</param>
        /// <returns>元素自身</returns>
        public static T WithConditionalClass<T>(this T element, bool condition, string className) where T : VisualElement
        {
            if (condition && !string.IsNullOrEmpty(className))
            {
                element.AddToClassList(className);
            }
            return element;
        }

        /// <summary>
        /// 条件添加DaisyUI类名
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="condition">条件</param>
        /// <param name="className">类名（不包含daisy-前缀）</param>
        /// <returns>元素自身</returns>
        public static T WithConditionalDaisyClass<T>(this T element, bool condition, string className) where T : VisualElement
        {
            if (condition && !string.IsNullOrEmpty(className))
            {
                element.AddDaisyClass(className);
            }
            return element;
        }

        #endregion

        #region 样式操作扩展

        /// <summary>
        /// 设置样式属性
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="styleAction">样式设置操作</param>
        /// <returns>元素自身</returns>
        public static T WithStyle<T>(this T element, System.Action<IStyle> styleAction) where T : VisualElement
        {
            styleAction?.Invoke(element.style);
            return element;
        }

        /// <summary>
        /// 设置显示状态
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="visible">是否可见</param>
        /// <returns>元素自身</returns>
        public static T SetVisible<T>(this T element, bool visible) where T : VisualElement
        {
            element.style.display = visible ? DisplayStyle.Flex : DisplayStyle.None;
            return element;
        }

        /// <summary>
        /// 切换显示状态
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <returns>元素自身</returns>
        public static T ToggleVisible<T>(this T element) where T : VisualElement
        {
            bool isVisible = element.style.display == DisplayStyle.Flex;
            return element.SetVisible(!isVisible);
        }

        /// <summary>
        /// 设置禁用状态
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="disabled">是否禁用</param>
        /// <returns>元素自身</returns>
        public static T SetDisabled<T>(this T element, bool disabled) where T : VisualElement
        {
            element.SetEnabled(!disabled);
            element.ToggleDaisyClass("disabled", disabled);
            return element;
        }

        #endregion

        #region 调试和验证扩展

        /// <summary>
        /// 启用调试模式
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <returns>元素自身</returns>
        public static T EnableDebug<T>(this T element) where T : VisualElement
        {
            element.AddToClassList("daisy-debug");
            return element;
        }

        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="message">调试消息</param>
        /// <returns>元素自身</returns>
        public static T LogDebug<T>(this T element, string message = "") where T : VisualElement
        {
            if (DaisyUtilities.IsDebugMode)
            {
                var msg = string.IsNullOrEmpty(message)
                    ? $"Element: {element.GetType().Name}, Classes: [{string.Join(", ", element.GetClasses())}]"
                    : message;
                Logging.LogInfo("DaisyExtensions", msg);
            }
            return element;
        }

        /// <summary>
        /// 验证元素
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="validator">验证器</param>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>元素自身</returns>
        public static T Validate<T>(this T element, System.Func<T, bool> validator, string errorMessage = "") where T : VisualElement
        {
            if (validator != null && !validator(element))
            {
                var msg = string.IsNullOrEmpty(errorMessage)
                    ? $"Validation failed for {element.GetType().Name}"
                    : errorMessage;
                Logging.LogWarning("DaisyExtensions", msg);
            }
            return element;
        }

        #endregion

        #region 性能优化扩展

        /// <summary>
        /// 批量操作（减少重绘）
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="batchAction">批量操作</param>
        /// <returns>元素自身</returns>
        public static T BatchUpdate<T>(this T element, System.Action<T> batchAction) where T : VisualElement
        {
            // Unity UI Toolkit 会自动批量处理样式更新
            // 这里主要是为了保持API一致性
            batchAction?.Invoke(element);
            return element;
        }

        /// <summary>
        /// 延迟执行操作
        /// </summary>
        /// <typeparam name="T">元素类型</typeparam>
        /// <param name="element">目标元素</param>
        /// <param name="action">延迟操作</param>
        /// <param name="delay">延迟时间（毫秒）</param>
        /// <returns>元素自身</returns>
        public static T DelayedAction<T>(this T element, System.Action<T> action, float delay = 0f) where T : VisualElement
        {
            if (action != null)
            {
                if (delay <= 0f)
                {
                    action(element);
                }
                else
                {
                    // 使用Unity的协程系统或定时器来延迟执行
                    element.schedule.Execute(() => action(element)).StartingIn((long)(delay * 1000));
                }
            }
            return element;
        }

        #endregion
    }
}