using UnityEngine;
using BlastingDesign.UI.Components;

namespace BlastingDesign.UI.Testing
{
    /// <summary>
    /// 数据库连接窗口测试脚本
    /// 用于测试重构后的数据库连接窗口功能
    /// </summary>
    public class DatabaseConnectionWindowTest : MonoBehaviour
    {
        [Header("Test Settings")]
        [SerializeField] private KeyCode testKey = KeyCode.F1;
        [SerializeField] private bool showDebugInfo = true;

        private DatabaseConnectionWindow dbWindow;

        private void Start()
        {
            if (showDebugInfo)
            {
                Debug.Log($"DatabaseConnectionWindowTest: 按 {testKey} 键打开数据库连接窗口进行测试");
            }
        }

        private void Update()
        {
            if (Input.GetKeyDown(testKey))
            {
                TestDatabaseConnectionWindow();
            }
        }

        /// <summary>
        /// 测试数据库连接窗口
        /// </summary>
        private void TestDatabaseConnectionWindow()
        {
            try
            {
                if (dbWindow == null)
                {
                    // 创建数据库连接窗口组件
                    var windowObject = new GameObject("DatabaseConnectionWindow");
                    dbWindow = windowObject.AddComponent<DatabaseConnectionWindow>();
                    
                    if (showDebugInfo)
                    {
                        Debug.Log("DatabaseConnectionWindowTest: 创建数据库连接窗口组件");
                    }
                }

                // 显示窗口
                dbWindow.ShowWindow();
                
                if (showDebugInfo)
                {
                    Debug.Log("DatabaseConnectionWindowTest: 显示数据库连接窗口");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"DatabaseConnectionWindowTest: 测试失败 - {ex.Message}");
            }
        }

        private void OnGUI()
        {
            if (showDebugInfo)
            {
                GUILayout.BeginArea(new Rect(10, 10, 300, 100));
                GUILayout.Label($"按 {testKey} 键测试数据库连接窗口");
                
                if (GUILayout.Button("测试数据库连接窗口"))
                {
                    TestDatabaseConnectionWindow();
                }
                
                GUILayout.EndArea();
            }
        }
    }
}