using UnityEngine;
using UnityEngine.UIElements;
using UnityEngine.InputSystem;
using BlastingDesign.UI.Core;
using BlastingDesign.Utils;
using BlastingDesign.Events;
using BlastingDesign.Events.Core;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 状态栏组件 - 显示状态信息、坐标和性能数据（轻量级架构）
    /// </summary>
    [UxmlElement]
    public partial class StatusBar : UIElementBase
    {
        // 资源路径
        protected override string TemplatePath => "UI/StatusBar";

        // 设置
        private bool showPerformanceInfo = true;
        private bool showCoordinates = true;
        private float updateInterval = 0.1f;
        private int coordinatePrecision = 2;
        private string currentUnit = "m";

        // UI元素引用
        private Label toolMessage;
        private Label actionMessage;
        private Label worldX, worldY, worldZ;
        private Label geoLon, geoLat;
        private Label zoomText;
        private Label fpsValue, msValue;
        private Label unitText;
        private Label precisionLabel;

        // 进度和通知
        private VisualElement progressContainer;
        private ProgressBar progressBar;
        private Label progressText;
        private Button progressCancel;
        private VisualElement notification;
        private Label notificationText;
        private Button notificationClose;

        // 控制按钮
        private Button coordSystemToggle;
        private Button unitToggle;
        private Button precisionToggle;

        // 新事件系统订阅
        private IEventSubscription statusMessageSubscription;

        // 状态数据
        private Vector3 currentWorldPosition;
        private Vector2 currentGeoPosition;
        private bool isMetricUnit = true;
        private bool isWorldCoordSystem = true;
        private float frameTime = 0f;
        private int frameCount = 0;
        private float fps = 60f;

        // 更新系统
        private IVisualElementScheduledItem performanceUpdateScheduler;
        private IVisualElementScheduledItem coordinateUpdateScheduler;
        private IVisualElementScheduledItem notificationScheduler;

        public StatusBar()
        {
            elementName = "StatusBar";
        }

        protected override void InitializeData()
        {
            // 设置初始状态
            SetupInitialState();

            // 开始更新循环
            StartUpdateLoop();

            // 订阅新事件系统
            SubscribeToNewEventSystem();

            Logging.LogInfo("StatusBar", "状态栏初始化完成");
        }

        protected override void CacheUIElements()
        {
            // 状态消息
            toolMessage = SafeQuery<Label>("tool-message");
            actionMessage = SafeQuery<Label>("action-message");

            // 坐标显示
            worldX = SafeQuery<Label>("world-x");
            worldY = SafeQuery<Label>("world-y");
            worldZ = SafeQuery<Label>("world-z");
            geoLon = SafeQuery<Label>("geo-lon");
            geoLat = SafeQuery<Label>("geo-lat");
            zoomText = SafeQuery<Label>("zoom-text");

            // 性能信息
            fpsValue = SafeQuery<Label>("fps-value");
            msValue = SafeQuery<Label>("ms-value");

            // 进度和通知
            progressContainer = SafeQuery<VisualElement>("progress-container");
            progressBar = SafeQuery<ProgressBar>("progress-bar");
            progressText = SafeQuery<Label>("progress-text");
            progressCancel = SafeQuery<Button>("progress-cancel");
            notification = SafeQuery<VisualElement>("notification");
            notificationText = SafeQuery<Label>("notification-text");
            notificationClose = SafeQuery<Button>("notification-close");

            // 控制按钮
            coordSystemToggle = SafeQuery<Button>("coord-system-toggle");
            unitToggle = SafeQuery<Button>("unit-toggle");
            precisionToggle = SafeQuery<Button>("precision-toggle");
            unitText = SafeQuery<Label>("unit-text");
            precisionLabel = SafeQuery<Label>("precision-label");
        }

        private void SetupInitialState()
        {
            // 隐藏进度和通知
            if (progressContainer != null)
                progressContainer.style.display = DisplayStyle.None;
            if (notification != null)
                notification.style.display = DisplayStyle.None;

            // 设置初始单位和精度
            if (unitText != null)
                unitText.text = currentUnit;
            if (precisionLabel != null)
                precisionLabel.text = coordinatePrecision.ToString();

            // 设置初始消息
            UpdateToolMessage("选择工具已激活");
            UpdateActionHint("点击对象进行选择");
        }

        protected override void SetupEventListeners()
        {
            // 控制按钮事件
            coordSystemToggle?.RegisterCallback<ClickEvent>(evt => ToggleCoordinateSystem());
            unitToggle?.RegisterCallback<ClickEvent>(evt => ToggleUnit());
            precisionToggle?.RegisterCallback<ClickEvent>(evt => TogglePrecision());

            // 进度取消按钮
            progressCancel?.RegisterCallback<ClickEvent>(evt => CancelProgress());

            // 通知关闭按钮
            notificationClose?.RegisterCallback<ClickEvent>(evt => HideNotification());

            // 监听UI事件系统
            if (eventSystem != null)
            {
                eventSystem.Toolbar.OnToolSelected.AddListener(OnToolSelected);
                eventSystem.Status.OnStatusMessageChanged.AddListener(UpdateStatusMessage);
                eventSystem.Status.OnMouseWorldPositionChanged.AddListener(UpdateWorldPosition);
                eventSystem.Status.OnMouseGeoPositionChanged.AddListener(UpdateGeoPosition);
                eventSystem.Status.OnToolTipChanged.AddListener(UpdateActionHint);
            }
        }

        private void StartUpdateLoop()
        {
            // 开始性能监控
            if (showPerformanceInfo)
            {
                performanceUpdateScheduler = schedule.Execute(UpdatePerformanceInfo)
                    .Every((long)(updateInterval * 1000)); // 转换为毫秒
            }

            // 开始坐标更新
            if (showCoordinates)
            {
                coordinateUpdateScheduler = schedule.Execute(UpdateCoordinateDisplay)
                    .Every((long)(updateInterval * 1000)); // 转换为毫秒
            }
        }

        private void UpdatePerformanceInfo()
        {
            // 计算FPS
            frameCount++;
            frameTime += Time.unscaledDeltaTime;

            if (frameTime >= 1.0f)
            {
                fps = frameCount / frameTime;
                frameCount = 0;
                frameTime = 0f;

                // 更新显示
                if (fpsValue != null)
                    fpsValue.text = Mathf.RoundToInt(fps).ToString();
                if (msValue != null)
                    msValue.text = (1000f / fps).ToString("F1");
            }
        }

        private void UpdateCoordinateDisplay()
        {
            // 更新鼠标位置（这里使用示例数据，实际应该从输入系统获取）
            UpdateMousePosition();
        }

        private void UpdateMousePosition()
        {
            // 使用新的InputSystem获取鼠标位置
            Vector2 mousePos = Mouse.current.position.ReadValue();

            // 模拟世界坐标转换
            currentWorldPosition = new Vector3(
                mousePos.x * 0.01f,
                0f,
                mousePos.y * 0.01f
            );

            // 模拟地理坐标转换
            currentGeoPosition = new Vector2(
                116.3974f + (mousePos.x - Screen.width * 0.5f) * 0.0001f,
                39.9093f + (mousePos.y - Screen.height * 0.5f) * 0.0001f
            );

            // 更新显示
            UpdateWorldCoordinateDisplay();
            UpdateGeoCoordinateDisplay();
        }

        private void UpdateWorldCoordinateDisplay()
        {
            if (worldX != null)
                worldX.text = FormatCoordinate(currentWorldPosition.x);
            if (worldY != null)
                worldY.text = FormatCoordinate(currentWorldPosition.y);
            if (worldZ != null)
                worldZ.text = FormatCoordinate(currentWorldPosition.z);
        }

        private void UpdateGeoCoordinateDisplay()
        {
            if (geoLon != null)
                geoLon.text = FormatGeoCoordinate(currentGeoPosition.x) + "°";
            if (geoLat != null)
                geoLat.text = FormatGeoCoordinate(currentGeoPosition.y) + "°";
        }

        private string FormatCoordinate(float value)
        {
            // 根据单位转换值
            float displayValue = isMetricUnit ? value : value * 3.28084f; // 米转英尺
            return displayValue.ToString($"F{coordinatePrecision}");
        }

        private string FormatGeoCoordinate(float value)
        {
            return value.ToString("F6");
        }

        // 事件处理方法
        private void OnToolSelected(string toolName)
        {
            string displayName = GetToolDisplayName(toolName);
            UpdateToolMessage($"{displayName}工具已激活");
            UpdateActionHint(GetToolHint(toolName));
        }

        private string GetToolDisplayName(string toolName)
        {
            return toolName switch
            {
                "select" => "选择",
                "move" => "移动",
                "rotate" => "旋转",
                "scale" => "缩放",
                _ => toolName
            };
        }

        private string GetToolHint(string toolName)
        {
            return toolName switch
            {
                "select" => "点击对象进行选择",
                "move" => "拖拽对象进行移动",
                "rotate" => "拖拽对象进行旋转",
                "scale" => "拖拽对象进行缩放",
                _ => "使用当前工具"
            };
        }

        private void UpdateWorldPosition(Vector3 worldPos)
        {
            currentWorldPosition = worldPos;
            UpdateWorldCoordinateDisplay();
        }

        private void UpdateGeoPosition(Vector2 geoPos)
        {
            currentGeoPosition = geoPos;
            UpdateGeoCoordinateDisplay();
        }

        // 公共方法
        public void UpdateToolMessage(string message)
        {
            if (toolMessage != null)
                toolMessage.text = message;
        }

        public void UpdateActionHint(string hint)
        {
            if (actionMessage != null)
                actionMessage.text = hint;
        }

        public void UpdateStatusMessage(string message)
        {
            UpdateActionHint(message);
        }

        public void UpdateZoomLevel(float zoomLevel)
        {
            if (zoomText != null)
                zoomText.text = $"1:{Mathf.RoundToInt(zoomLevel)}";
        }

        // 进度显示
        public void ShowProgress(string message, float progress = 0f)
        {
            if (progressContainer != null)
            {
                progressContainer.style.display = DisplayStyle.Flex;
            }

            if (progressText != null)
                progressText.text = message;

            if (progressBar != null)
                progressBar.value = progress * 100f;
        }

        public void UpdateProgress(float progress)
        {
            if (progressBar != null)
                progressBar.value = progress * 100f;
        }

        public void HideProgress()
        {
            if (progressContainer != null)
                progressContainer.style.display = DisplayStyle.None;
        }

        public void CancelProgress()
        {
            HideProgress();

            // 使用新事件系统（优先）
            if (BlastingDesign.Events.EventSystemManager.Instance != null && BlastingDesign.Events.EventSystemManager.Instance.EventBus != null)
            {
                BlastingDesign.Events.EventSystemManager.Instance.EventBus.Publish(new MenuItemClickedEvent("cancel-progress"));
            }
            else
            {
                // 回退到旧事件系统
                UIEventSystem.TriggerMenuItemClicked("cancel-progress");
            }

            Logging.LogInfo("StatusBar", "进度已取消");
        }

        // 通知显示
        public void ShowNotification(string message, NotificationType type = NotificationType.Info, float duration = 3f)
        {
            if (notification == null || notificationText == null) return;

            // 停止之前的通知调度器
            notificationScheduler?.Pause();

            // 设置通知样式
            notification.RemoveFromClassList("success");
            notification.RemoveFromClassList("warning");
            notification.RemoveFromClassList("error");

            switch (type)
            {
                case NotificationType.Success:
                    notification.AddToClassList("success");
                    break;
                case NotificationType.Warning:
                    notification.AddToClassList("warning");
                    break;
                case NotificationType.Error:
                    notification.AddToClassList("error");
                    break;
            }

            // 显示通知
            notificationText.text = message;
            notification.style.display = DisplayStyle.Flex;

            // 自动隐藏
            if (duration > 0)
            {
                notificationScheduler = schedule.Execute(HideNotification)
                    .StartingIn((long)(duration * 1000)); // 转换为毫秒
            }

            Logging.LogInfo("StatusBar", $"显示通知: {message} ({type})");
        }

        public void HideNotification()
        {
            if (notification != null)
                notification.style.display = DisplayStyle.None;

            notificationScheduler?.Pause();
        }

        // 控制按钮功能
        private void ToggleCoordinateSystem()
        {
            isWorldCoordSystem = !isWorldCoordSystem;
            Logging.LogInfo("StatusBar", $"坐标系统切换: {(isWorldCoordSystem ? "世界坐标" : "屏幕坐标")}");

            // 这里可以添加坐标系统切换的实际逻辑
            ShowNotification($"已切换到{(isWorldCoordSystem ? "世界坐标" : "屏幕坐标")}系统", NotificationType.Info, 2f);
        }

        private void ToggleUnit()
        {
            isMetricUnit = !isMetricUnit;
            currentUnit = isMetricUnit ? "m" : "ft";

            if (unitText != null)
                unitText.text = currentUnit;

            Logging.LogInfo("StatusBar", $"单位切换: {currentUnit}");
            ShowNotification($"已切换到{(isMetricUnit ? "公制" : "英制")}单位", NotificationType.Info, 2f);
        }

        private void TogglePrecision()
        {
            coordinatePrecision = (coordinatePrecision % 4) + 1; // 1-4之间循环

            if (precisionLabel != null)
                precisionLabel.text = coordinatePrecision.ToString();

            Logging.LogInfo("StatusBar", $"精度设置: {coordinatePrecision}位小数");
            ShowNotification($"坐标精度设置为{coordinatePrecision}位小数", NotificationType.Info, 2f);
        }

        protected override void RemoveEventListeners()
        {
            if (eventSystem != null)
            {
                eventSystem.Toolbar.OnToolSelected.RemoveListener(OnToolSelected);
                eventSystem.Status.OnStatusMessageChanged.RemoveListener(UpdateStatusMessage);
                eventSystem.Status.OnMouseWorldPositionChanged.RemoveListener(UpdateWorldPosition);
                eventSystem.Status.OnMouseGeoPositionChanged.RemoveListener(UpdateGeoPosition);
                eventSystem.Status.OnToolTipChanged.RemoveListener(UpdateActionHint);
            }
        }

        /// <summary>
        /// 订阅新事件系统
        /// </summary>
        private void SubscribeToNewEventSystem()
        {
            if (BlastingDesign.Events.EventSystemManager.Instance != null && BlastingDesign.Events.EventSystemManager.Instance.EventBus != null)
            {
                // 订阅状态消息事件
                statusMessageSubscription = BlastingDesign.Events.EventSystemManager.Instance.EventBus.Subscribe<StatusMessageEvent>(OnStatusMessageReceived);

                Logging.LogInfo("StatusBar", "已订阅新事件系统");
            }
            else
            {
                Logging.LogWarning("StatusBar", "新事件系统未初始化，将使用旧系统");
            }
        }

        /// <summary>
        /// 处理状态消息事件
        /// </summary>
        private void OnStatusMessageReceived(StatusMessageEvent evt)
        {
            if (evt == null) return;

            // 根据消息类型显示不同的通知
            NotificationType notificationType = evt.MessageType switch
            {
                StatusMessageType.Info => NotificationType.Info,
                StatusMessageType.Success => NotificationType.Success,
                StatusMessageType.Warning => NotificationType.Warning,
                StatusMessageType.Error => NotificationType.Error,
                _ => NotificationType.Info
            };

            ShowNotification(evt.Message, notificationType, evt.Duration);
        }

        public override void Cleanup()
        {
            // 清理新事件系统订阅
            statusMessageSubscription?.Dispose();

            // 停止所有调度器
            performanceUpdateScheduler?.Pause();
            coordinateUpdateScheduler?.Pause();
            notificationScheduler?.Pause();

            // 调用基类清理
            base.Cleanup();
        }
    }

    // 通知类型枚举
    public enum NotificationType
    {
        Info,
        Success,
        Warning,
        Error
    }
}
