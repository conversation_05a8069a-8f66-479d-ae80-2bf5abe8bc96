using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 模态窗口组件 - 窗口管理功能
    /// 包含位置、尺寸、显示管理相关方法
    /// </summary>
    public partial class ModalWindow
    {
        #region Size and Position Management

        /// <summary>
        /// 应用窗口尺寸
        /// </summary>
        private void ApplySize()
        {
            if (modalRoot != null)
            {
                modalRoot.style.width = windowSize.x;
                modalRoot.style.height = windowSize.y;
            }
        }

        /// <summary>
        /// 应用窗口位置
        /// </summary>
        private void ApplyPosition()
        {
            if (modalRoot != null)
            {
                modalRoot.style.left = windowPosition.x;
                modalRoot.style.top = windowPosition.y;
            }
        }

        /// <summary>
        /// 居中窗口
        /// </summary>
        private void CenterWindow()
        {
            // 延迟执行以确保父容器布局完成
            schedule.Execute(() =>
            {
                var parent = this.parent;
                if (parent != null)
                {
                    var parentRect = parent.contentRect;

                    // 如果父容器尺寸还没有准备好，使用默认屏幕尺寸
                    if (parentRect.width <= 0 || parentRect.height <= 0)
                    {
                        parentRect = new Rect(0, 0, Screen.width, Screen.height);
                    }

                    var centerX = (parentRect.width - windowSize.x) * 0.5f;
                    var centerY = (parentRect.height - windowSize.y) * 0.5f;

                    // 应用边界限制
                    centerY = Mathf.Max(centerY, TOP_MARGIN);
                    centerY = Mathf.Min(centerY, parentRect.height - windowSize.y - BOTTOM_MARGIN);

                    windowPosition = new Vector2(centerX, centerY);
                    ApplyPosition();
                }
            });
        }

        /// <summary>
        /// 限制窗口位置在边界内
        /// </summary>
        private Vector2 ClampPosition(Vector2 position)
        {
            var parent = this.parent;
            if (parent == null) return position;

            var parentRect = parent.contentRect;

            // 限制水平位置
            position.x = Mathf.Clamp(position.x, 0, parentRect.width - windowSize.x);

            // 限制垂直位置（考虑顶部工具栏和底部状态栏）
            position.y = Mathf.Clamp(position.y, TOP_MARGIN, parentRect.height - windowSize.y - BOTTOM_MARGIN);

            return position;
        }

        #endregion

        #region Display Management

        /// <summary>
        /// 更新显示顺序
        /// </summary>
        private void UpdateDisplayOrder()
        {
            // USS不支持z-index，通过UI层级顺序来管理窗口层级
            // 具体的层级管理由ModalWindowManager处理
        }

        /// <summary>
        /// 设置是否可关闭
        /// </summary>
        private void SetClosable(bool closable)
        {
            if (closeButton != null)
            {
                closeButton.style.display = closable ? DisplayStyle.Flex : DisplayStyle.None;
            }
        }

        /// <summary>
        /// 将窗口移到前台
        /// </summary>
        public new void BringToFront()
        {
            if (ModalWindowManager.Instance != null)
            {
                ModalWindowManager.Instance.BringWindowToFront(this);
            }
        }

        #endregion
    }
}