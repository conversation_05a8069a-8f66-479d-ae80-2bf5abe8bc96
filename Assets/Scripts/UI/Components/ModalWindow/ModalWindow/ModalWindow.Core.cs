using System;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Core;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 窗口调整尺寸方向
    /// </summary>
    public enum ResizeDirection
    {
        None,
        Right,
        Bottom,
        BottomRight
    }
    /// <summary>
    /// 模态窗口组件 - 核心功能
    /// 包含字段定义、属性、构造函数和基础初始化
    /// </summary>
    [UxmlElement]
    public partial class ModalWindow : UIElementBase
    {
        #region Fields

        // 资源路径
        protected override string TemplatePath => "UI/ModalWindow";

        [Header("Modal Window Settings")]
        private string windowTitle = "Modal Window";
        private Vector2 windowSize = new Vector2(400, 300);
        private Vector2 windowPosition = Vector2.zero;
        private bool isDraggable = true;
        private bool isClosable = true;
        private bool isResizable = true;
        private bool autoCenter = true;
        private int displayOrder = 0;

        [Header("Size Constraints")]
        private Vector2 minSize = new Vector2(300, 200);
        private Vector2 maxSize = new Vector2(1200, 800);

        // UI元素引用
        private VisualElement modalRoot;
        private VisualElement titleBar;
        private Label titleLabel;
        private Button closeButton;
        private new VisualElement contentContainer;

        // 调整尺寸相关UI元素
        private VisualElement resizeHandleRight;
        private VisualElement resizeHandleBottom;
        private VisualElement resizeHandleBottomRight;

        // 拖拽相关
        private bool isDragging = false;
        private Vector2 dragStartPosition;
        private Vector2 windowStartPosition;
        private bool mouseButtonDown = false;

        // 调整尺寸相关
        private bool isResizing = false;
        private ResizeDirection resizeDirection = ResizeDirection.None;
        private Vector2 resizeStartPosition;
        private Vector2 resizeStartSize;
        private Vector2 resizeStartWindowPosition;

        // 边界限制
        private const float TOP_MARGIN = 40f;   // 顶部工具栏高度
        private const float BOTTOM_MARGIN = 30f; // 底部状态栏高度

        // 事件
        public event Action<ModalWindow> OnWindowClosed;
        public event Action<ModalWindow> OnWindowFocused;

        #endregion

        #region Properties

        public string Title
        {
            get => windowTitle;
            set
            {
                windowTitle = value;
                if (titleLabel != null)
                {
                    titleLabel.text = windowTitle;
                }
            }
        }

        public Vector2 Size
        {
            get => windowSize;
            set
            {
                windowSize = value;
                ApplySize();
            }
        }

        public Vector2 Position
        {
            get => windowPosition;
            set
            {
                windowPosition = value;
                ApplyPosition();
            }
        }

        public bool IsResizable
        {
            get => isResizable;
            set
            {
                isResizable = value;
                UpdateResizeHandlesVisibility();
            }
        }

        public Vector2 MinSize
        {
            get => minSize;
            set => minSize = value;
        }

        public Vector2 MaxSize
        {
            get => maxSize;
            set => maxSize = value;
        }

        public bool IsDraggable
        {
            get => isDraggable;
            set => isDraggable = value;
        }

        public bool IsClosable
        {
            get => isClosable;
            set
            {
                isClosable = value;
                if (closeButton != null)
                {
                    closeButton.style.display = isClosable ? DisplayStyle.Flex : DisplayStyle.None;
                }
            }
        }

        public int DisplayOrder
        {
            get => displayOrder;
            set
            {
                displayOrder = value;
                UpdateDisplayOrder();
            }
        }

        public VisualElement ContentContainer => contentContainer;

        #endregion

        #region Constructor

        public ModalWindow()
        {
            elementName = "ModalWindow";
        }

        #endregion

        #region Initialization

        protected override void CacheUIElements()
        {
            // 缓存主要UI元素
            modalRoot = SafeQuery<VisualElement>("modal-window-root");
            titleBar = SafeQuery<VisualElement>("modal-titlebar");
            titleLabel = SafeQuery<Label>("modal-title");
            closeButton = SafeQuery<Button>("modal-close-btn");
            contentContainer = SafeQuery<VisualElement>("modal-content");

            // 缓存调整尺寸相关UI元素
            resizeHandleRight = SafeQuery<VisualElement>("resize-handle-right");
            resizeHandleBottom = SafeQuery<VisualElement>("resize-handle-bottom");
            resizeHandleBottomRight = SafeQuery<VisualElement>("resize-handle-bottom-right");

            // 调试信息
            Logging.LogInfo("ModalWindow", "UI元素缓存完成");
        }

        protected override void InitializeData()
        {
            // 设置初始属性
            Title = windowTitle;
            ApplySize();
            UpdateDisplayOrder();
            SetClosable(isClosable);
            UpdateResizeHandlesVisibility();

            // 如果需要自动居中，计算位置
            if (autoCenter)
            {
                CenterWindow();
            }
            else
            {
                ApplyPosition();
            }

            Logging.LogInfo("ModalWindow", $"模态窗口初始化完成 - 标题: {windowTitle}, 尺寸: {windowSize}");
        }

        protected override void SetupEventListeners()
        {
            // 标题栏拖拽事件
            if (titleBar != null && isDraggable)
            {
                titleBar.RegisterCallback<MouseDownEvent>(OnTitleBarMouseDown);
                titleBar.RegisterCallback<MouseMoveEvent>(OnTitleBarMouseMove);
                titleBar.RegisterCallback<MouseUpEvent>(OnTitleBarMouseUp);
                titleBar.RegisterCallback<MouseLeaveEvent>(OnTitleBarMouseLeave);
            }

            // 关闭按钮事件
            if (closeButton != null)
            {
                closeButton.RegisterCallback<ClickEvent>(OnCloseButtonClicked);
            }

            // 窗口焦点事件
            if (modalRoot != null)
            {
                modalRoot.RegisterCallback<MouseDownEvent>(OnWindowMouseDown);
            }

            // 调整尺寸事件
            if (isResizable)
            {
                SetupResizeEventListenersInternal();
            }

            Logging.LogInfo("ModalWindow", "事件监听器设置完成");
        }

        #endregion

        #region Drag Control Methods

        /// <summary>
        /// 强制停止拖拽操作
        /// </summary>
        private void StopDragging()
        {
            if (!isDragging) return;

            isDragging = false;
            mouseButtonDown = false;

            // 移除拖拽样式
            if (modalRoot != null)
            {
                modalRoot.RemoveFromClassList("dragging");
            }

            // 释放鼠标捕获
            if (titleBar != null)
            {
                titleBar.ReleaseMouse();
            }

            // 注销全局鼠标事件监听
            UnregisterGlobalMouseEvents();
        }

        /// <summary>
        /// 强制停止调整尺寸操作
        /// </summary>
        private void StopResizing()
        {
            if (!isResizing) return;

            isResizing = false;
            var currentDirection = resizeDirection;
            resizeDirection = ResizeDirection.None;

            // 移除调整尺寸样式
            if (modalRoot != null)
            {
                modalRoot.RemoveFromClassList("resizing");
            }

            // 释放鼠标捕获
            var handle = GetResizeHandleInternal(currentDirection);
            if (handle != null)
            {
                handle.ReleaseMouse();
            }

            // 注销全局鼠标事件监听
            UnregisterGlobalResizeEventsInternal();
        }

        #endregion

        #region Resize Event Setup

        /// <summary>
        /// 设置调整尺寸事件监听器
        /// </summary>
        private void SetupResizeEventListenersInternal()
        {
            if (!isResizable) return;

            // 右边框调整手柄
            if (resizeHandleRight != null)
            {
                resizeHandleRight.RegisterCallback<MouseDownEvent>(evt => OnResizeHandleMouseDownInternal(evt, ResizeDirection.Right));
            }

            // 底边框调整手柄
            if (resizeHandleBottom != null)
            {
                resizeHandleBottom.RegisterCallback<MouseDownEvent>(evt => OnResizeHandleMouseDownInternal(evt, ResizeDirection.Bottom));
            }

            // 右下角调整手柄
            if (resizeHandleBottomRight != null)
            {
                resizeHandleBottomRight.RegisterCallback<MouseDownEvent>(evt => OnResizeHandleMouseDownInternal(evt, ResizeDirection.BottomRight));
            }
        }

        /// <summary>
        /// 调整尺寸手柄鼠标按下事件
        /// </summary>
        private void OnResizeHandleMouseDownInternal(MouseDownEvent evt, ResizeDirection direction)
        {
            if (!isResizable) return;

            // 如果正在拖拽，先停止拖拽
            if (isDragging)
            {
                StopDragging();
            }

            isResizing = true;
            resizeDirection = direction;
            resizeStartPosition = evt.mousePosition;
            resizeStartSize = windowSize;
            resizeStartWindowPosition = windowPosition;

            // 添加调整尺寸样式
            if (modalRoot != null)
            {
                modalRoot.AddToClassList("resizing");
            }

            // 捕获鼠标
            var handle = GetResizeHandleInternal(direction);
            if (handle != null)
            {
                handle.CaptureMouse();
            }

            // 注册全局鼠标事件监听
            RegisterGlobalResizeEventsInternal();

            // 触发焦点事件
            OnWindowMouseDown(null);

            evt.StopPropagation();
        }

        /// <summary>
        /// 根据调整方向获取对应的调整手柄
        /// </summary>
        private VisualElement GetResizeHandleInternal(ResizeDirection direction)
        {
            return direction switch
            {
                ResizeDirection.Right => resizeHandleRight,
                ResizeDirection.Bottom => resizeHandleBottom,
                ResizeDirection.BottomRight => resizeHandleBottomRight,
                _ => null
            };
        }

        /// <summary>
        /// 注册全局调整尺寸事件监听
        /// </summary>
        private void RegisterGlobalResizeEventsInternal()
        {
            var rootContainer = GetRootContainer();
            if (rootContainer != null)
            {
                rootContainer.RegisterCallback<MouseMoveEvent>(OnGlobalResizeMouseMoveInternal);
                rootContainer.RegisterCallback<MouseUpEvent>(OnGlobalResizeMouseUpInternal);
            }
        }

        /// <summary>
        /// 全局鼠标移动事件（调整尺寸）
        /// </summary>
        private void OnGlobalResizeMouseMoveInternal(MouseMoveEvent evt)
        {
            if (!isResizing) return;

            var deltaPosition = evt.mousePosition - resizeStartPosition;
            var newSize = CalculateNewSizeInternal(deltaPosition);
            var newPosition = CalculateNewPositionInternal(newSize);

            // 应用尺寸和位置限制
            newSize = ClampSizeInternal(newSize);
            newPosition = ClampPosition(newPosition);

            windowSize = newSize;
            windowPosition = newPosition;

            ApplySize();
            ApplyPosition();

            // 阻止事件传播，避免与拖拽事件冲突
            evt.StopPropagation();
        }

        /// <summary>
        /// 全局鼠标释放事件（调整尺寸）
        /// </summary>
        private void OnGlobalResizeMouseUpInternal(MouseUpEvent evt)
        {
            if (!isResizing) return;

            isResizing = false;
            var currentDirection = resizeDirection;
            resizeDirection = ResizeDirection.None;

            // 移除调整尺寸样式
            if (modalRoot != null)
            {
                modalRoot.RemoveFromClassList("resizing");
            }

            // 释放鼠标捕获
            var handle = GetResizeHandleInternal(currentDirection);
            if (handle != null)
            {
                handle.ReleaseMouse();
            }

            // 注销全局鼠标事件监听
            UnregisterGlobalResizeEventsInternal();

            evt.StopPropagation();
        }

        /// <summary>
        /// 注销全局调整尺寸事件监听
        /// </summary>
        private void UnregisterGlobalResizeEventsInternal()
        {
            var rootContainer = GetRootContainer();
            if (rootContainer != null)
            {
                rootContainer.UnregisterCallback<MouseMoveEvent>(OnGlobalResizeMouseMoveInternal);
                rootContainer.UnregisterCallback<MouseUpEvent>(OnGlobalResizeMouseUpInternal);
            }
        }

        /// <summary>
        /// 根据鼠标移动计算新的窗口尺寸
        /// </summary>
        private Vector2 CalculateNewSizeInternal(Vector2 deltaPosition)
        {
            var newSize = resizeStartSize;

            switch (resizeDirection)
            {
                case ResizeDirection.Right:
                    newSize.x += deltaPosition.x;
                    break;
                case ResizeDirection.Bottom:
                    newSize.y += deltaPosition.y;
                    break;
                case ResizeDirection.BottomRight:
                    newSize.x += deltaPosition.x;
                    newSize.y += deltaPosition.y;
                    break;
            }

            return newSize;
        }

        /// <summary>
        /// 根据新尺寸计算新的窗口位置（某些调整方向需要调整位置）
        /// </summary>
        private Vector2 CalculateNewPositionInternal(Vector2 newSize)
        {
            // 对于右边框和底边框调整，位置不需要改变
            // 对于左边框和顶边框调整，需要调整位置（暂未实现）
            // 当前实现中newSize参数暂未使用，为将来扩展预留
            _ = newSize; // 显式忽略参数
            return resizeStartWindowPosition;
        }

        /// <summary>
        /// 限制窗口尺寸在允许范围内
        /// </summary>
        private Vector2 ClampSizeInternal(Vector2 size)
        {
            // 应用最小尺寸限制
            size.x = Mathf.Max(size.x, minSize.x);
            size.y = Mathf.Max(size.y, minSize.y);

            // 应用最大尺寸限制
            size.x = Mathf.Min(size.x, maxSize.x);
            size.y = Mathf.Min(size.y, maxSize.y);

            // 确保窗口不会超出父容器
            var parent = this.parent;
            if (parent != null)
            {
                var parentRect = parent.contentRect;
                if (parentRect.width > 0 && parentRect.height > 0)
                {
                    size.x = Mathf.Min(size.x, parentRect.width);
                    size.y = Mathf.Min(size.y, parentRect.height - TOP_MARGIN - BOTTOM_MARGIN);
                }
            }

            return size;
        }



        #endregion

        #region Resize Handles Management

        /// <summary>
        /// 更新调整尺寸手柄的可见性
        /// </summary>
        private void UpdateResizeHandlesVisibility()
        {
            var display = isResizable ? DisplayStyle.Flex : DisplayStyle.None;

            if (resizeHandleRight != null)
                resizeHandleRight.style.display = display;
            if (resizeHandleBottom != null)
                resizeHandleBottom.style.display = display;
            if (resizeHandleBottomRight != null)
                resizeHandleBottomRight.style.display = display;
        }

        #endregion

        #region Cleanup

        public override void Cleanup()
        {
            // 强制停止所有操作
            StopDragging();
            StopResizing();

            base.Cleanup();

            // 清理事件
            OnWindowClosed = null;
            OnWindowFocused = null;

            // 重置状态
            isDragging = false;
            isResizing = false;
            mouseButtonDown = false;
            resizeDirection = ResizeDirection.None;

            // 清理引用
            modalRoot = null;
            titleBar = null;
            titleLabel = null;
            closeButton = null;
            contentContainer = null;

            // 清理调整尺寸相关引用
            resizeHandleRight = null;
            resizeHandleBottom = null;
            resizeHandleBottomRight = null;

            Logging.LogInfo("ModalWindow", "模态窗口清理完成");
        }

        #endregion
    }
}