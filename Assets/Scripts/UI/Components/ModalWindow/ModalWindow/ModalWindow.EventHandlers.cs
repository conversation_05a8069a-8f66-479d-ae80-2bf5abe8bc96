using UnityEngine.UIElements;
using BlastingDesign.UI.Core;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 模态窗口组件 - 事件处理功能
    /// 包含窗口事件处理和事件清理方法
    /// </summary>
    public partial class ModalWindow
    {
        #region Window Event Handlers

        private void OnCloseButtonClicked(ClickEvent evt)
        {
            if (!isClosable) return;

            CloseWindow();
            evt.StopPropagation();
        }

        private void OnWindowMouseDown(MouseDownEvent evt)
        {
            // 将窗口移到前台
            BringToFront();

            // 触发焦点事件
            OnWindowFocused?.Invoke(this);

            // 通过事件系统通知焦点变化
            UIEventSystem.TriggerCustomEvent("ModalWindowFocused", this);
        }

        #endregion

        #region Event Cleanup

        protected override void RemoveEventListeners()
        {
            // 强制停止所有操作
            StopDragging();
            StopResizing();

            // 移除标题栏事件
            if (titleBar != null)
            {
                titleBar.UnregisterCallback<MouseDownEvent>(OnTitleBarMouseDown);
                titleBar.UnregisterCallback<MouseMoveEvent>(OnTitleBarMouseMove);
                titleBar.UnregisterCallback<MouseUpEvent>(OnTitleBarMouseUp);
                titleBar.UnregisterCallback<MouseLeaveEvent>(OnTitleBarMouseLeave);
            }

            // 移除关闭按钮事件
            if (closeButton != null)
            {
                closeButton.UnregisterCallback<ClickEvent>(OnCloseButtonClicked);
            }

            // 移除窗口事件
            if (modalRoot != null)
            {
                modalRoot.UnregisterCallback<MouseDownEvent>(OnWindowMouseDown);
            }

            // 移除调整尺寸事件
            RemoveResizeEventListeners();

            // 确保移除全局鼠标事件
            UnregisterGlobalMouseEvents();
            UnregisterGlobalResizeEventsInternal();
        }

        /// <summary>
        /// 移除调整尺寸事件监听器
        /// </summary>
        private void RemoveResizeEventListeners()
        {
            // 移除右边框调整手柄事件
            if (resizeHandleRight != null)
            {
                // 注意：由于使用了lambda表达式，无法直接注销，这里通过重新创建元素来清理
                // 或者在实际项目中应该保存事件处理器的引用
            }

            // 移除底边框调整手柄事件
            if (resizeHandleBottom != null)
            {
                // 同上
            }

            // 移除右下角调整手柄事件
            if (resizeHandleBottomRight != null)
            {
                // 同上
            }
        }

        #endregion
    }
}