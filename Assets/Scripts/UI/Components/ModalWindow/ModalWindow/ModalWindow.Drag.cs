using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Core;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 模态窗口组件 - 拖拽功能
    /// 包含所有拖拽相关的事件处理方法
    /// </summary>
    public partial class ModalWindow
    {
        #region Drag Event Handlers

        private void OnTitleBarMouseDown(MouseDownEvent evt)
        {
            if (!isDraggable) return;

            // 如果正在调整尺寸，先停止调整尺寸
            if (isResizing)
            {
                StopResizing();
            }

            isDragging = true;
            mouseButtonDown = true;
            dragStartPosition = evt.mousePosition;
            windowStartPosition = windowPosition;

            // 添加拖拽样式
            if (modalRoot != null)
            {
                modalRoot.AddToClassList("dragging");
            }

            // 捕获鼠标
            if (titleBar != null)
            {
                titleBar.CaptureMouse();
            }

            // 注册全局鼠标事件监听
            RegisterGlobalMouseEvents();

            // 触发焦点事件
            OnWindowMouseDown(null);

            evt.StopPropagation();
        }

        private void OnTitleBarMouseMove(MouseMoveEvent evt)
        {
            if (!isDragging || !mouseButtonDown) return;

            var deltaPosition = evt.mousePosition - dragStartPosition;
            var newPosition = windowStartPosition + deltaPosition;

            // 应用边界限制
            newPosition = ClampPosition(newPosition);

            windowPosition = newPosition;
            ApplyPosition();
        }

        private void OnTitleBarMouseUp(MouseUpEvent evt)
        {
            if (!isDragging) return;

            isDragging = false;
            mouseButtonDown = false;

            // 移除拖拽样式
            if (modalRoot != null)
            {
                modalRoot.RemoveFromClassList("dragging");
            }

            // 释放鼠标捕获
            if (titleBar != null)
            {
                titleBar.ReleaseMouse();
            }

            // 注销全局鼠标事件监听
            UnregisterGlobalMouseEvents();

            evt.StopPropagation();
        }

        private void OnTitleBarMouseLeave(MouseLeaveEvent evt)
        {
            // 移除此事件处理，因为我们现在使用全局鼠标事件来处理拖拽
            // 鼠标离开标题栏不再停止拖拽
        }

        #endregion

        #region Global Mouse Event Handlers

        /// <summary>
        /// 注册全局鼠标事件监听
        /// </summary>
        private void RegisterGlobalMouseEvents()
        {
            // 获取根容器或父容器来监听全局鼠标事件
            var rootContainer = GetRootContainer();
            if (rootContainer != null)
            {
                rootContainer.RegisterCallback<MouseMoveEvent>(OnGlobalMouseMove);
                rootContainer.RegisterCallback<MouseUpEvent>(OnGlobalMouseUp);
            }
        }

        /// <summary>
        /// 注销全局鼠标事件监听
        /// </summary>
        private void UnregisterGlobalMouseEvents()
        {
            var rootContainer = GetRootContainer();
            if (rootContainer != null)
            {
                rootContainer.UnregisterCallback<MouseMoveEvent>(OnGlobalMouseMove);
                rootContainer.UnregisterCallback<MouseUpEvent>(OnGlobalMouseUp);
            }
        }

        /// <summary>
        /// 获取根容器
        /// </summary>
        private VisualElement GetRootContainer()
        {
            VisualElement current = this;
            while (current.parent != null)
            {
                current = current.parent;
            }
            return current;
        }

        /// <summary>
        /// 全局鼠标移动事件
        /// </summary>
        private void OnGlobalMouseMove(MouseMoveEvent evt)
        {
            if (!isDragging || !mouseButtonDown) return;

            var deltaPosition = evt.mousePosition - dragStartPosition;
            var newPosition = windowStartPosition + deltaPosition;

            // 应用边界限制
            newPosition = ClampPosition(newPosition);

            windowPosition = newPosition;
            ApplyPosition();

            // 阻止事件传播，避免与调整尺寸事件冲突
            evt.StopPropagation();
        }

        /// <summary>
        /// 全局鼠标松开事件
        /// </summary>
        private void OnGlobalMouseUp(MouseUpEvent evt)
        {
            if (!isDragging) return;

            isDragging = false;
            mouseButtonDown = false;

            // 移除拖拽样式
            if (modalRoot != null)
            {
                modalRoot.RemoveFromClassList("dragging");
            }

            // 释放鼠标捕获
            if (titleBar != null)
            {
                titleBar.ReleaseMouse();
            }

            // 注销全局鼠标事件监听
            UnregisterGlobalMouseEvents();

            evt.StopPropagation();
        }

        #endregion
    }
}