using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Utils;
using BlastingDesign.UI.Core;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// ComponentLibraryWindow UI相关功能部分
    /// </summary>
    public partial class ComponentLibraryWindow : UIComponentBase
    {
        #region UI元素管理

        /// <summary>
        /// 缓存UI元素
        /// </summary>
        private void CacheUIElements()
        {
            if (rootElement == null)
            {
                Logging.LogWarning(componentName, "根元素为null，无法缓存UI元素");
                return;
            }

            try
            {
                closeButton = rootElement.Q<Button>("close-button");
                refreshButton = rootElement.Q<Button>("refresh-button");
                themeToggleButton = rootElement.Q<Button>("theme-toggle");
                componentNameLabel = rootElement.Q<Label>("component-name");
                componentDescriptionLabel = rootElement.Q<Label>("component-description");
                contentBodyScrollView = rootElement.Q<ScrollView>("content-body");
                statusLabel = rootElement.Q<Label>("status-label");

                // 检查关键元素是否成功加载
                if (closeButton == null) Logging.LogWarning(componentName, "close-button 未找到");
                if (refreshButton == null) Logging.LogWarning(componentName, "refresh-button 未找到");
                if (themeToggleButton == null) Logging.LogWarning(componentName, "theme-toggle 未找到");
                if (componentNameLabel == null) Logging.LogWarning(componentName, "component-name 未找到");
                if (componentDescriptionLabel == null) Logging.LogWarning(componentName, "component-description 未找到");
                if (contentBodyScrollView == null) Logging.LogWarning(componentName, "content-body 未找到");
                if (statusLabel == null) Logging.LogWarning(componentName, "status-label 未找到");

                Logging.LogInfo(componentName, "UI元素缓存完成");
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"缓存UI元素时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region 事件监听器设置

        /// <summary>
        /// 设置按钮事件
        /// </summary>
        private void SetupButtonEvents()
        {
            // 关闭按钮
            if (closeButton != null)
            {
                closeButton.clicked += CloseWindow;
            }

            // 刷新按钮
            if (refreshButton != null)
            {
                refreshButton.clicked += RefreshContent;
            }

            // 主题切换按钮
            if (themeToggleButton != null)
            {
                themeToggleButton.clicked += ToggleTheme;
            }
        }

        /// <summary>
        /// 设置分类按钮事件
        /// </summary>
        private void SetupCategoryEvents()
        {
            if (rootElement == null)
            {
                Logging.LogWarning(componentName, "根元素为null，无法设置分类按钮事件");
                return;
            }

            try
            {
                // 获取所有分类按钮
                var buttons = rootElement.Query<Button>(className: "category-item");
                if (buttons != null)
                {
                    categoryButtons = buttons.ToList();

                    if (categoryButtons != null && categoryButtons.Count > 0)
                    {
                        foreach (var button in categoryButtons)
                        {
                            if (button != null)
                            {
                                // 使用局部变量避免闭包问题
                                var localButton = button;
                                button.clicked += () => HandleCategorySelection(localButton);
                            }
                        }

                        Logging.LogInfo(componentName, $"分类按钮事件设置完成，共{categoryButtons.Count}个按钮");
                    }
                    else
                    {
                        Logging.LogWarning(componentName, "未找到分类按钮或按钮列表为空");
                        categoryButtons = new List<Button>(); // 确保不为null
                    }
                }
                else
                {
                    Logging.LogWarning(componentName, "按钮查询返回null");
                    categoryButtons = new List<Button>();
                }
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"设置分类按钮事件时发生错误: {ex.Message}");
                categoryButtons = new List<Button>(); // 确保不为null
            }
        }

        #endregion

        #region UI交互处理

        /// <summary>
        /// 处理分类选择
        /// </summary>
        private void HandleCategorySelection(Button button)
        {
            try
            {
                if (button == null)
                {
                    Logging.LogWarning(componentName, "选中的按钮为null");
                    return;
                }

                // 移除其他按钮的选中状态
                if (categoryButtons != null && categoryButtons.Count > 0)
                {
                    foreach (var btn in categoryButtons)
                    {
                        if (btn != null)
                        {
                            btn.RemoveFromClassList("selected");
                        }
                    }
                }

                // 添加当前按钮的选中状态
                button.AddToClassList("selected");

                // 更新选中的组件ID
                selectedComponentId = button.name ?? "";

                // 更新内容区域
                UpdateContentArea(button.name ?? "", button.text ?? "未知组件");

                // 触发事件
                OnComponentSelected?.Invoke(button.name ?? "");

                Logging.LogInfo(componentName, $"选中组件: {button.text ?? "未知"}");
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"处理分类选择时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新内容区域
        /// </summary>
        private void UpdateContentArea(string componentId, string componentName)
        {
            try
            {
                // 更新标题和描述
                if (componentNameLabel != null)
                {
                    componentNameLabel.text = componentName;
                }

                if (componentDescriptionLabel != null)
                {
                    componentDescriptionLabel.text = GetComponentDescription(componentId);
                }

                // 更新内容体
                if (contentBodyScrollView != null)
                {
                    contentBodyScrollView.Clear();
                    var previewContent = CreateComponentPreview(componentId, componentName);
                    contentBodyScrollView.Add(previewContent);
                }

                // 更新状态
                if (statusLabel != null)
                {
                    statusLabel.text = $"正在查看: {componentName}";
                }
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"更新内容区域时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 切换主题
        /// </summary>
        private void ToggleTheme()
        {
            try
            {
                if (rootElement == null || themeToggleButton == null) return;

                var isDark = rootElement.ClassListContains("theme-dark");
                if (isDark)
                {
                    rootElement.RemoveFromClassList("theme-dark");
                    rootElement.AddToClassList("theme-light");
                    themeToggleButton.text = "☀️";
                    UIEventSystem.TriggerStatusMessage("切换到浅色主题");
                    OnThemeChanged?.Invoke(false);
                }
                else
                {
                    rootElement.RemoveFromClassList("theme-light");
                    rootElement.AddToClassList("theme-dark");
                    themeToggleButton.text = "🌙";
                    UIEventSystem.TriggerStatusMessage("切换到深色主题");
                    OnThemeChanged?.Invoke(true);
                }

                Logging.LogInfo(componentName, $"主题切换到: {(isDark ? "浅色" : "深色")}");
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"切换主题时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理窗口关闭
        /// </summary>
        private void HandleWindowClosed(ModalWindow window)
        {
            try
            {
                modalWindow = null;
                selectedComponentId = "";
                categoryButtons.Clear();

                OnWindowClosed?.Invoke();
                Logging.LogInfo(componentName, "窗口关闭处理完成");
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"处理窗口关闭时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理代码复制
        /// </summary>
        private void HandleCodeCopy(string componentId)
        {
            try
            {
                var code = GetComponentCode(componentId);
                // 这里可以添加实际的复制到剪贴板的逻辑
                UIEventSystem.TriggerStatusMessage("代码已复制到剪贴板");
                Logging.LogInfo(componentName, $"代码已复制: {componentId}");
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"复制代码时发生错误: {ex.Message}");
            }
        }

        #endregion
    }
}