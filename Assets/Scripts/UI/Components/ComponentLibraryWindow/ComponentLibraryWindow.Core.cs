using System;
using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Utils;
using BlastingDesign.UI.Core;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// ComponentLibraryWindow 核心功能部分
    /// </summary>
    public partial class ComponentLibraryWindow : UIComponentBase
    {
        #region 公共方法

        /// <summary>
        /// 显示组件库窗口
        /// </summary>
        public void ShowWindow()
        {
            try
            {
                // 检查是否已存在窗口
                if (modalWindow != null)
                {
                    modalWindow.BringToFront();
                    Logging.LogInfo(componentName, "组件库窗口已存在，移到前台");
                    return;
                }

                // 创建模态窗口
                modalWindow = ModalWindowManager.Instance?.CreateNamedWindow(
                    "ComponentLibrary",
                    windowTitle,
                    windowSize
                );

                if (modalWindow != null)
                {
                    // 初始化组件内容
                    InitializeWindowContent();

                    // 设置窗口关闭事件
                    modalWindow.OnWindowClosed += HandleWindowClosed;

                    Logging.LogInfo(componentName, "组件库窗口创建成功");
                }
                else
                {
                    Logging.LogError(componentName, "无法创建组件库窗口");
                }
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"显示组件库窗口时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 关闭组件库窗口
        /// </summary>
        public void CloseWindow()
        {
            try
            {
                if (modalWindow != null)
                {
                    modalWindow.CloseWindow();
                    modalWindow = null;
                }

                OnWindowClosed?.Invoke();
                Logging.LogInfo(componentName, "组件库窗口已关闭");
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"关闭组件库窗口时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新组件库内容
        /// </summary>
        public void RefreshContent()
        {
            try
            {
                if (rootElement != null)
                {
                    // 重新绑定事件
                    SetupEventListeners();

                    // 刷新组件可见性
                    RefreshComponentVisibility();

                    // 如果有选中的组件，重新加载其内容
                    if (!string.IsNullOrEmpty(selectedComponentId) && categoryButtons != null && categoryButtons.Count > 0)
                    {
                        var selectedButton = categoryButtons.FirstOrDefault(b => b != null && b.name == selectedComponentId);
                        if (selectedButton != null)
                        {
                            UpdateContentArea(selectedComponentId, selectedButton.text ?? "未知组件");
                        }
                    }

                    UIEventSystem.TriggerStatusMessage("组件库已刷新");
                    Logging.LogInfo(componentName, "组件库内容已刷新");
                }
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"刷新组件库内容时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region UIComponentBase 实现

        protected override bool CreateUIElements()
        {
            try
            {
                // 加载UXML文档
                var uiDocument = Resources.Load<VisualTreeAsset>("Window/ComponentLibraryWindow");
                if (uiDocument == null)
                {
                    Logging.LogError(componentName, "无法加载组件库窗口UXML文件");
                    return false;
                }

                // 实例化UI文档
                rootElement = uiDocument.Instantiate();

                if (rootElement == null)
                {
                    Logging.LogError(componentName, "无法实例化UI文档");
                    return false;
                }

                Logging.LogInfo(componentName, "UI元素创建成功");
                return true;
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"创建UI元素时发生错误: {ex.Message}");
                return false;
            }
        }

        protected override void ApplyStyles()
        {
            try
            {
                if (rootElement == null)
                {
                    Logging.LogWarning(componentName, "根元素为null，无法应用样式");
                    return;
                }

                // 加载DaisyUI主样式表
                var daisyStyleSheet = Resources.Load<StyleSheet>("DaisyUI/daisy-components");
                if (daisyStyleSheet != null)
                {
                    rootElement.styleSheets.Add(daisyStyleSheet);
                    Logging.LogInfo(componentName, "DaisyUI主样式表加载成功");
                }
                else
                {
                    Logging.LogWarning(componentName, "无法加载DaisyUI主样式表");
                }

                // 加载组件库窗口样式表
                var styleSheet = Resources.Load<StyleSheet>("Window/ComponentLibraryWindow");
                if (styleSheet != null)
                {
                    rootElement.styleSheets.Add(styleSheet);
                    Logging.LogInfo(componentName, "组件库窗口样式表加载成功");
                }
                else
                {
                    Logging.LogWarning(componentName, "无法加载组件库窗口样式表");
                }
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"应用样式时发生错误: {ex.Message}");
            }
        }

        protected override void SetupEventListeners()
        {
            try
            {
                if (rootElement == null)
                {
                    Logging.LogWarning(componentName, "根元素为null，无法设置事件监听器");
                    return;
                }

                // 缓存UI元素
                CacheUIElements();

                // 设置组件可见性（必须在设置按钮事件之前）
                SetupComponentVisibility();

                // 设置按钮事件
                SetupButtonEvents();

                // 设置分类按钮事件
                SetupCategoryEvents();

                Logging.LogInfo(componentName, "事件监听器设置完成");
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"设置事件监听器时发生错误: {ex.Message}");
            }
        }

        protected override void OnInitialize()
        {
            try
            {
                // 标记为已初始化
                isInitialized = true;

                Logging.LogInfo(componentName, "组件库窗口初始化完成");
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"自定义初始化时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化窗口内容
        /// </summary>
        private void InitializeWindowContent()
        {
            try
            {
                if (modalWindow == null)
                {
                    Logging.LogError(componentName, "模态窗口为null，无法初始化内容");
                    return;
                }

                // 初始化组件（不需要父容器，因为我们会直接设置内容）
                Initialize(null);

                // 设置窗口内容
                if (rootElement != null)
                {
                    modalWindow.SetContent(rootElement);
                    Logging.LogInfo(componentName, "窗口内容设置成功");
                }
                else
                {
                    Logging.LogError(componentName, "根元素为null，无法设置窗口内容");
                    CreateFallbackContent();
                }
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"初始化窗口内容时发生错误: {ex.Message}\n堆栈跟踪: {ex.StackTrace}");
                CreateFallbackContent();
            }
        }

        #endregion
    }
}