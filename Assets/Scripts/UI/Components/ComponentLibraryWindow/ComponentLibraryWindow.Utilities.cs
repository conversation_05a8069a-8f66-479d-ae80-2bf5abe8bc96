using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Utils;
using BlastingDesign.UI.Core;
using BlastingDesign.UI.Components.Previews;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// ComponentLibraryWindow 工具方法部分
    /// </summary>
    public partial class ComponentLibraryWindow : UIComponentBase
    {
        #region 组件信息获取

        /// <summary>
        /// 获取组件描述
        /// </summary>
        private string GetComponentDescription(string componentId)
        {
            try
            {
                // 首先尝试从预览提供者获取描述
                var provider = ComponentPreviewManager.Instance.GetProvider(componentId);
                if (provider != null)
                {
                    return provider.ComponentDescription;
                }

                // 如果没有找到预览提供者，使用默认描述
                return GetFallbackDescription(componentId);
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"获取组件描述时发生错误: {ex.Message}");
                return "组件描述获取失败";
            }
        }

        /// <summary>
        /// 获取备用组件描述
        /// </summary>
        private string GetFallbackDescription(string componentId)
        {
            return componentId switch
            {
                "buttons-item" => "按钮组件提供各种样式和尺寸的可点击按钮",
                "dropdown-item" => "下拉菜单组件用于展示选项列表",
                "modal-item" => "模态窗口组件用于显示叠加内容",
                "input-item" => "输入框组件用于文本输入",
                "card-item" => "卡片组件用于展示内容块",
                "accordion-item" => "手风琴组件用于展示可折叠的内容面板",
                "avatar-item" => "头像组件用于展示用户头像",
                "badge-item" => "徽章组件用于展示状态或数量",
                "carousel-item" => "轮播图组件用于展示图片或内容滑动",
                "checkbox-item" => "复选框组件用于多选操作",
                "radio-item" => "单选按钮组件用于单选操作",
                "select-item" => "选择器组件用于下拉选择",
                "textarea-item" => "文本域组件用于多行文本输入",
                "toggle-item" => "切换开关组件用于开关操作",
                "progress-item" => "进度条组件用于显示进度",
                "alert-item" => "警告框组件用于显示重要信息",
                "tooltip-item" => "工具提示组件用于显示额外信息",
                "breadcrumbs-item" => "面包屑组件用于显示导航路径",
                "menu-item" => "菜单组件用于显示导航菜单",
                "navbar-item" => "导航栏组件用于页面导航",
                "pagination-item" => "分页组件用于分页导航",
                "tab-item" => "标签页组件用于内容切换",
                "tree-item" => "树组件用于展示层次结构数据，支持展开收起、搜索过滤等功能",
                "divider-item" => "分割线组件用于内容分割",
                "hero-item" => "英雄区域组件用于重要内容展示",
                "footer-item" => "页脚组件用于底部信息展示",
                _ => "DaisyUI组件的详细描述和使用说明"
            };
        }

        /// <summary>
        /// 获取组件代码示例
        /// </summary>
        private string GetComponentCode(string componentId)
        {
            try
            {
                // 首先尝试从预览提供者获取代码示例
                var provider = ComponentPreviewManager.Instance.GetProvider(componentId);
                if (provider != null)
                {
                    return provider.GetCodeExample();
                }

                // 如果没有找到预览提供者，使用备用代码示例
                return GetFallbackCodeExample(componentId);
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"获取组件代码示例时发生错误: {ex.Message}");
                return $"// 获取代码示例时发生错误: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取备用组件代码示例
        /// </summary>
        private string GetFallbackCodeExample(string componentId)
        {
            return componentId switch
            {
                "buttons-item" => @"// 方式一：使用DaisyBuilder（推荐）
using BlastingDesign.UI.DaisyUI.Builders;
using BlastingDesign.UI.Extensions;

var button = DaisyBuilder.Button(""点击我"")
    .SetPrimary()
    .OnClick(() => Debug.Log(""按钮被点击""));

// 方式二：直接创建DaisyButton
var daisyButton = DaisyButton.Create(""按钮"")
    .SetPrimary()
    .OnClick(() => Debug.Log(""点击""));

// 方式三：使用扩展方法
var nativeButton = new Button(""原生按钮"")
    .AddDaisyClass(""btn"")
    .AddDaisyClass(""btn-primary"")
    .With(btn => btn.clicked += () => Debug.Log(""点击""));",

                "input-item" => @"// 直接创建DaisyInput（推荐）
using BlastingDesign.UI.DaisyUI.Components.DataInput;
using BlastingDesign.UI.Extensions;

var input = DaisyInput.Create(""用户名"", ""请输入用户名"")
    .OnValueChanged(value => Debug.Log($""输入值: {value}""));

// 简单输入框
var simpleInput = DaisyInput.Create(""请输入内容..."")
    .SetValue(""预设值"");

// 使用扩展方法增强原生TextField
var textField = new TextField(""标签"")
    .AddDaisyClass(""input"")
    .AddFocusBasedKeyboardEventBlocker();",

                "card-item" => @"// 直接创建DaisyCard（推荐）
using BlastingDesign.UI.DaisyUI.Components.DataDisplay;
using BlastingDesign.UI.Extensions;

var card = DaisyCard.Create(""卡片标题"");
card.AddText(""这是卡片的内容"");

// 创建空卡片后添加内容
var emptyCard = DaisyCard.Create();
emptyCard.SetTitle(""动态标题"");
emptyCard.AddText(""动态添加的内容"");

// 使用扩展方法创建卡片样式
var customCard = new VisualElement()
    .AddDaisyClass(""card"")
    .AddDaisyClass(""bg-base-100"")
    .WithPadding(4);",

                "select-item" => @"// 直接创建DaisySelect（推荐）
using BlastingDesign.UI.DaisyUI.Components.DataInput;
using System.Collections.Generic;

var select = DaisySelect.Create(""选择选项"", 
    new List<string> { ""选项1"", ""选项2"", ""选项3"" });

// 使用参数数组方式
var simpleSelect = DaisySelect.Create(""类型"", ""类型A"", ""类型B"", ""类型C"");

// 设置默认值
var selectWithDefault = DaisySelect.Create(new List<string> { ""选项1"", ""选项2"" });
selectWithDefault.Value = ""选项1"";",

                "dropdown-item" => @"// 下拉菜单示例（基于按钮）
using BlastingDesign.UI.DaisyUI.Builders;
using BlastingDesign.UI.Extensions;

var dropdownBtn = DaisyBuilder.Button(""下拉菜单 ▼"")
    .OnClick(() => {
        // 在实际应用中，这里会显示下拉菜单
        Debug.Log(""显示下拉菜单"");
    });

// 或者使用DaisySelect作为下拉选择
var selectDropdown = DaisySelect.Create(""菜单选项"", ""选项1"", ""选项2"", ""选项3"");",

                "modal-item" => @"// 模态框示例（基于按钮触发）
using BlastingDesign.UI.DaisyUI.Builders;
using BlastingDesign.UI.Components;

var modalBtn = DaisyBuilder.Button(""打开模态框"")
    .SetPrimary()
    .OnClick(() => {
        // 使用ModalWindowManager创建模态窗口
        var modal = ModalWindowManager.Instance?.CreateNamedWindow(
            ""ExampleModal"", ""示例模态框"", new Vector2(400, 300));
        
        if (modal != null) {
            // 添加内容到模态框
            var content = new Label(""这是模态框的内容"");
            modal.SetContent(content);
        }
    });",

                _ => $@"// {componentId} 的使用示例
// 使用DaisyUI组件库的最佳实践：

// 1. 引入命名空间
using BlastingDesign.UI.DaisyUI.Builders;
using BlastingDesign.UI.DaisyUI.Components.Actions;
using BlastingDesign.UI.DaisyUI.Components.DataInput;
using BlastingDesign.UI.DaisyUI.Components.DataDisplay;
using BlastingDesign.UI.Extensions;

// 2. 使用DaisyBuilder创建基础组件
var button = DaisyBuilder.Button(""按钮文本"")
    .SetPrimary()
    .SetMedium();

var input = DaisyInput.Create(""占位符"")
    .SetBordered();

var card = DaisyCard.Create(""卡片标题"")
    .SetContent(""卡片内容"");

// 3. 使用扩展方法链式调用
var element = new VisualElement()
    .AddDaisyClass(""component-class"")
    .WithCurrentTheme()
    .SetVisible(true);"
            };
        }

        #endregion

        #region 组件可见性管理

        /// <summary>
        /// 设置组件项的可见性
        /// </summary>
        private void SetupComponentVisibility()
        {
            try
            {
                if (rootElement == null)
                {
                    Logging.LogWarning(componentName, "根元素为null，无法设置组件可见性");
                    return;
                }

                // 获取所有已实现的组件ID
                var implementedComponentIds = ComponentPreviewManager.Instance.GetAllComponentIds();
                var implementedComponentsList = new HashSet<string>(implementedComponentIds);

                Logging.LogInfo(componentName, $"已实现的组件: {string.Join(", ", implementedComponentsList)}");

                // 隐藏所有未实现的组件按钮
                HideUnimplementedComponents(implementedComponentsList);

                // 隐藏空的分类组
                HideEmptyCategories();

                Logging.LogInfo(componentName, "组件可见性设置完成");
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"设置组件可见性时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 隐藏未实现的组件
        /// </summary>
        private void HideUnimplementedComponents(HashSet<string> implementedComponents)
        {
            try
            {
                // 获取所有分类按钮
                var allButtons = rootElement.Query<Button>(className: "category-item").ToList();

                foreach (var button in allButtons)
                {
                    if (button != null && !string.IsNullOrEmpty(button.name))
                    {
                        if (implementedComponents.Contains(button.name))
                        {
                            // 已实现的组件，确保可见
                            button.style.display = DisplayStyle.Flex;
                            Logging.LogInfo(componentName, $"显示已实现组件: {button.name} ({button.text})");
                        }
                        else
                        {
                            // 未实现的组件，隐藏
                            button.style.display = DisplayStyle.None;
                            Logging.LogInfo(componentName, $"隐藏未实现组件: {button.name} ({button.text})");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"隐藏未实现组件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 隐藏空的分类组
        /// </summary>
        private void HideEmptyCategories()
        {
            try
            {
                // 获取所有分类组
                var categoryGroups = rootElement.Query<VisualElement>(className: "category-group").ToList();

                foreach (var categoryGroup in categoryGroups)
                {
                    if (categoryGroup != null)
                    {
                        // 检查该分类组下是否有可见的按钮
                        var visibleButtons = categoryGroup.Query<Button>(className: "category-item")
                            .ToList()
                            .Where(btn => btn != null && btn.style.display.value == DisplayStyle.Flex)
                            .ToList();

                        if (visibleButtons.Count == 0)
                        {
                            // 没有可见的按钮，隐藏整个分类组
                            categoryGroup.style.display = DisplayStyle.None;
                            Logging.LogInfo(componentName, $"隐藏空分类组: {categoryGroup.name}");
                        }
                        else
                        {
                            // 有可见的按钮，确保分类组可见
                            categoryGroup.style.display = DisplayStyle.Flex;
                            Logging.LogInfo(componentName, $"显示分类组: {categoryGroup.name} (包含 {visibleButtons.Count} 个组件)");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"隐藏空分类组时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新组件可见性
        /// </summary>
        public void RefreshComponentVisibility()
        {
            try
            {
                SetupComponentVisibility();
                Logging.LogInfo(componentName, "组件可见性已刷新");
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"刷新组件可见性时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取可见组件统计信息
        /// </summary>
        public string GetVisibilityStats()
        {
            try
            {
                if (rootElement == null)
                {
                    return "根元素为null，无法获取统计信息";
                }

                var allButtons = rootElement.Query<Button>(className: "category-item").ToList();
                var visibleButtons = allButtons.Where(btn => btn != null && btn.style.display.value == DisplayStyle.Flex).ToList();
                var hiddenButtons = allButtons.Where(btn => btn != null && btn.style.display.value == DisplayStyle.None).ToList();

                var allCategories = rootElement.Query<VisualElement>(className: "category-group").ToList();
                var visibleCategories = allCategories.Where(cat => cat != null && cat.style.display.value == DisplayStyle.Flex).ToList();
                var hiddenCategories = allCategories.Where(cat => cat != null && cat.style.display.value == DisplayStyle.None).ToList();

                return $"组件可见性统计:\n" +
                       $"- 总组件数: {allButtons.Count}\n" +
                       $"- 可见组件: {visibleButtons.Count}\n" +
                       $"- 隐藏组件: {hiddenButtons.Count}\n" +
                       $"- 总分类数: {allCategories.Count}\n" +
                       $"- 可见分类: {visibleCategories.Count}\n" +
                       $"- 隐藏分类: {hiddenCategories.Count}";
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"获取可见性统计信息时发生错误: {ex.Message}");
                return $"获取统计信息时发生错误: {ex.Message}";
            }
        }

        #endregion

        #region 备用内容和清理

        /// <summary>
        /// 创建备用内容
        /// </summary>
        private void CreateFallbackContent()
        {
            try
            {
                var container = new VisualElement();
                container.style.flexGrow = 1;
                container.style.paddingTop = 16;
                container.style.paddingBottom = 16;
                container.style.paddingLeft = 16;
                container.style.paddingRight = 16;

                var titleLabel = new Label("DaisyUI组件库");
                titleLabel.style.fontSize = 24;
                titleLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
                titleLabel.style.marginBottom = 16;
                container.Add(titleLabel);

                var errorLabel = new Label("组件库界面加载失败，请检查UXML文件是否存在");
                errorLabel.style.color = Color.red;
                container.Add(errorLabel);

                modalWindow?.SetContent(container);
                Logging.LogInfo(componentName, "备用内容创建完成");
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"创建备用内容时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        private void CleanupResources()
        {
            try
            {
                // 清理事件订阅
                if (modalWindow != null)
                {
                    modalWindow.OnWindowClosed -= HandleWindowClosed;
                }

                // 清理按钮事件
                if (closeButton != null)
                {
                    closeButton.clicked -= CloseWindow;
                }
                if (refreshButton != null)
                {
                    refreshButton.clicked -= RefreshContent;
                }
                if (themeToggleButton != null)
                {
                    themeToggleButton.clicked -= ToggleTheme;
                }

                // 清理分类按钮事件
                if (categoryButtons != null && categoryButtons.Count > 0)
                {
                    foreach (var button in categoryButtons)
                    {
                        if (button != null)
                        {
                            // 移除所有点击事件监听器
                            button.clicked -= () => HandleCategorySelection(button);
                        }
                    }
                    categoryButtons.Clear();
                }

                // 清理UI元素引用
                closeButton = null;
                refreshButton = null;
                themeToggleButton = null;
                componentNameLabel = null;
                componentDescriptionLabel = null;
                contentBodyScrollView = null;
                statusLabel = null;

                // 清理其他资源
                selectedComponentId = "";
                modalWindow = null;

                Logging.LogInfo(componentName, "资源清理完成");
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"清理资源时发生错误: {ex.Message}");
            }
        }

        #endregion
    }
}