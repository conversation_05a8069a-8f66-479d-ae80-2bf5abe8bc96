using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Builders;
using BlastingDesign.UI.Extensions;
using BlastingDesign.UI.Core;

namespace BlastingDesign.UI.Components.Previews
{
    /// <summary>
    /// 下拉菜单组件预览提供者
    /// </summary>
    public class DropdownPreviewProvider : BaseComponentPreviewProvider
    {
        public override string ComponentId => "dropdown-item";
        public override string ComponentName => "下拉菜单 (Dropdown)";
        public override string ComponentDescription => "下拉菜单组件用于展示选项列表，支持多种触发方式和样式";

        public override VisualElement CreatePreview()
        {
            var container = CreatePreviewContainer("flex", "flex-col", "gap-4");

            // 基本下拉菜单组
            var basicSection = new VisualElement();
            basicSection.AddToClassList("preview-section");

            var basicTitle = CreatePreviewTitle("基本下拉菜单");
            basicSection.Add(basicTitle);

            var basicDropdownContainer = CreatePreviewContainer("flex", "flex-wrap", "gap-3");

            // 基本下拉菜单
            var basicDropdownBtn = DaisyBuilder.Button("下拉菜单 ▼")
                .SetPrimary()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("下拉菜单功能演示 - 在实际应用中会显示菜单"));
            basicDropdownContainer.Add(basicDropdownBtn);

            var basicDescription = new Label("点击可展开下拉菜单（在实际应用中）")
                .AddDaisyClass("text-sm")
                .AddDaisyClass("text-gray-500");
            basicDropdownContainer.Add(basicDescription);

            basicSection.Add(basicDropdownContainer);
            container.Add(basicSection);

            // 下拉菜单样式组
            var styleSection = new VisualElement();
            styleSection.AddToClassList("preview-section");

            var styleTitle = CreatePreviewTitle("下拉菜单样式");
            styleSection.Add(styleTitle);

            var styleDropdownContainer = CreatePreviewContainer("flex", "flex-wrap", "gap-2");

            // 主要样式
            var primaryDropdownBtn = DaisyBuilder.Button("主要菜单 ▼")
                .SetPrimary()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("主要下拉菜单"));
            styleDropdownContainer.Add(primaryDropdownBtn);

            // 次要样式
            var secondaryDropdownBtn = DaisyBuilder.Button("次要菜单 ▼")
                .SetSecondary()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("次要下拉菜单"));
            styleDropdownContainer.Add(secondaryDropdownBtn);

            // 幽灵样式
            var ghostDropdownBtn = DaisyBuilder.Button("幽灵菜单 ▼")
                .SetGhost()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("幽灵下拉菜单"));
            styleDropdownContainer.Add(ghostDropdownBtn);

            styleSection.Add(styleDropdownContainer);
            container.Add(styleSection);

            // 下拉菜单尺寸组
            var sizeSection = new VisualElement();
            sizeSection.AddToClassList("preview-section");

            var sizeTitle = CreatePreviewTitle("下拉菜单尺寸");
            sizeSection.Add(sizeTitle);

            var sizeDropdownContainer = CreatePreviewContainer("flex", "flex-wrap", "gap-2", "items-center");

            // 小尺寸
            var smallDropdownBtn = DaisyBuilder.Button("小菜单 ▼")
                .SetPrimary()
                .SetSmall()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("小尺寸下拉菜单"));
            sizeDropdownContainer.Add(smallDropdownBtn);

            // 中等尺寸
            var mediumDropdownBtn = DaisyBuilder.Button("中等菜单 ▼")
                .SetPrimary()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("中等尺寸下拉菜单"));
            sizeDropdownContainer.Add(mediumDropdownBtn);

            // 大尺寸
            var largeDropdownBtn = DaisyBuilder.Button("大菜单 ▼")
                .SetPrimary()
                .SetLarge()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("大尺寸下拉菜单"));
            sizeDropdownContainer.Add(largeDropdownBtn);

            sizeSection.Add(sizeDropdownContainer);
            container.Add(sizeSection);

            // 下拉菜单位置组
            var positionSection = new VisualElement();
            positionSection.AddToClassList("preview-section");

            var positionTitle = CreatePreviewTitle("下拉菜单位置");
            positionSection.Add(positionTitle);

            var positionDropdownContainer = CreatePreviewContainer("flex", "flex-wrap", "gap-2");

            // 底部展开
            var bottomDropdownBtn = DaisyBuilder.Button("底部展开 ▼")
                .SetSecondary()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("底部展开菜单"));
            positionDropdownContainer.Add(bottomDropdownBtn);

            // 顶部展开
            var topDropdownBtn = DaisyBuilder.Button("顶部展开 ▲")
                .SetSecondary()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("顶部展开菜单"));
            positionDropdownContainer.Add(topDropdownBtn);

            // 左侧展开
            var leftDropdownBtn = DaisyBuilder.Button("◀ 左侧展开")
                .SetSecondary()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("左侧展开菜单"));
            positionDropdownContainer.Add(leftDropdownBtn);

            // 右侧展开
            var rightDropdownBtn = DaisyBuilder.Button("右侧展开 ▶")
                .SetSecondary()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("右侧展开菜单"));
            positionDropdownContainer.Add(rightDropdownBtn);

            positionSection.Add(positionDropdownContainer);
            container.Add(positionSection);

            return container;
        }

        public override string GetCodeExample()
        {
            return @"// 基本用法
using BlastingDesign.UI.DaisyUI.Components.Navigation;
using BlastingDesign.UI.DaisyUI.Builders;

// 创建下拉菜单
var dropdown = DaisyDropdown.Create(""下拉菜单"");

// 添加菜单项
dropdown.AddItem(""选项1"", () => Debug.Log(""选择了选项1""));
dropdown.AddItem(""选项2"", () => Debug.Log(""选择了选项2""));
dropdown.AddItem(""选项3"", () => Debug.Log(""选择了选项3""));

// 添加分隔线
dropdown.AddSeparator();

// 添加子菜单
var submenu = dropdown.AddSubmenu(""子菜单"");
submenu.AddItem(""子选项1"", () => Debug.Log(""子选项1""));
submenu.AddItem(""子选项2"", () => Debug.Log(""子选项2""));

// 设置下拉菜单样式
dropdown.SetPrimary();         // 主要样式
dropdown.SetSecondary();       // 次要样式
dropdown.SetGhost();           // 幽灵样式

// 设置下拉菜单尺寸
dropdown.SetSmall();           // 小尺寸
dropdown.SetMedium();          // 中等尺寸
dropdown.SetLarge();           // 大尺寸

// 设置展开位置
dropdown.SetPosition(DropdownPosition.Bottom);  // 底部展开
dropdown.SetPosition(DropdownPosition.Top);     // 顶部展开
dropdown.SetPosition(DropdownPosition.Left);    // 左侧展开
dropdown.SetPosition(DropdownPosition.Right);   // 右侧展开

// 设置触发方式
dropdown.SetTrigger(DropdownTrigger.Click);     // 点击触发
dropdown.SetTrigger(DropdownTrigger.Hover);     // 悬停触发

// 事件处理
dropdown.OnItemSelected += (item) => Debug.Log($""选择了: {item}"");
dropdown.OnMenuOpened += () => Debug.Log(""菜单已打开"");
dropdown.OnMenuClosed += () => Debug.Log(""菜单已关闭"");

// 程序控制
dropdown.Open();               // 打开菜单
dropdown.Close();              // 关闭菜单
dropdown.Toggle();             // 切换菜单状态

// 添加到容器
container.Add(dropdown);";
        }

        public override string GetUsageInstructions()
        {
            return @"下拉菜单组件使用说明：

1. 基本用法：
   - 使用 DaisyDropdown.Create() 创建下拉菜单
   - 使用 AddItem() 添加菜单项

2. 菜单项管理：
   - AddItem(string, Action)：添加普通菜单项
   - AddSeparator()：添加分隔线
   - AddSubmenu(string)：添加子菜单
   - RemoveItem(string)：移除菜单项
   - ClearItems()：清空所有菜单项

3. 样式选项：
   - SetPrimary()：主要样式
   - SetSecondary()：次要样式
   - SetGhost()：幽灵样式

4. 尺寸选项：
   - SetSmall()：小尺寸菜单
   - SetMedium()：中等尺寸菜单（默认）
   - SetLarge()：大尺寸菜单

5. 位置设置：
   - SetPosition(DropdownPosition)：设置展开位置
   - Bottom：底部展开（默认）
   - Top：顶部展开
   - Left：左侧展开
   - Right：右侧展开

6. 触发方式：
   - SetTrigger(DropdownTrigger)：设置触发方式
   - Click：点击触发（默认）
   - Hover：悬停触发

7. 程序控制：
   - Open()：打开菜单
   - Close()：关闭菜单
   - Toggle()：切换菜单状态
   - IsOpen：获取菜单状态

8. 事件处理：
   - OnItemSelected：菜单项选择事件
   - OnMenuOpened：菜单打开事件
   - OnMenuClosed：菜单关闭事件

9. 最佳实践：
   - 合理组织菜单项层级
   - 避免菜单项过多影响用户体验
   - 使用分隔线分组相关功能
   - 考虑菜单的可访问性";
        }

        public override string[] GetSupportedVariants()
        {
            return new string[]
            {
                "basic",
                "primary",
                "secondary",
                "ghost",
                "hover",
                "click",
                "top",
                "bottom",
                "left",
                "right"
            };
        }

        public override VisualElement CreateVariantPreview(string variant)
        {
            var container = CreatePreviewContainer("flex", "gap-2");

            var button = DaisyBuilder.Button($"{variant.ToUpper()} ▼");

            switch (variant.ToLower())
            {
                case "basic":
                    // 基本样式，无需额外设置
                    break;
                case "primary":
                    button.SetPrimary();
                    break;
                case "secondary":
                    button.SetSecondary();
                    break;
                case "ghost":
                    button.SetGhost();
                    break;
                case "hover":
                    button.SetSecondary();
                    break;
                case "click":
                    button.SetPrimary();
                    break;
                case "top":
                    button.SetSecondary();
                    button.SetText($"{variant.ToUpper()} ▲");
                    break;
                case "bottom":
                    button.SetSecondary();
                    break;
                case "left":
                    button.SetSecondary();
                    button.SetText($"◀ {variant.ToUpper()}");
                    break;
                case "right":
                    button.SetSecondary();
                    button.SetText($"{variant.ToUpper()} ▶");
                    break;
                default:
                    return null;
            }

            button.OnClick(() => UIEventSystem.TriggerStatusMessage($"{variant} 下拉菜单演示"));
            container.Add(button);
            return container;
        }

        public override string GetVariantCodeExample(string variant)
        {
            return variant.ToLower() switch
            {
                "basic" => @"var dropdown = DaisyDropdown.Create(""基本菜单"");",
                "primary" => @"var dropdown = DaisyDropdown.Create(""主要菜单"").SetPrimary();",
                "secondary" => @"var dropdown = DaisyDropdown.Create(""次要菜单"").SetSecondary();",
                "ghost" => @"var dropdown = DaisyDropdown.Create(""幽灵菜单"").SetGhost();",
                "hover" => @"var dropdown = DaisyDropdown.Create(""悬停菜单"").SetTrigger(DropdownTrigger.Hover);",
                "click" => @"var dropdown = DaisyDropdown.Create(""点击菜单"").SetTrigger(DropdownTrigger.Click);",
                "top" => @"var dropdown = DaisyDropdown.Create(""顶部菜单"").SetPosition(DropdownPosition.Top);",
                "bottom" => @"var dropdown = DaisyDropdown.Create(""底部菜单"").SetPosition(DropdownPosition.Bottom);",
                "left" => @"var dropdown = DaisyDropdown.Create(""左侧菜单"").SetPosition(DropdownPosition.Left);",
                "right" => @"var dropdown = DaisyDropdown.Create(""右侧菜单"").SetPosition(DropdownPosition.Right);",
                _ => null
            };
        }
    }
}
