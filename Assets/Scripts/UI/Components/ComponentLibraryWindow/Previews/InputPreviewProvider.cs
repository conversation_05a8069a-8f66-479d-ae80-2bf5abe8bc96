using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Components.DataInput;
using BlastingDesign.UI.Core;

namespace BlastingDesign.UI.Components.Previews
{
    /// <summary>
    /// 输入框组件预览提供者
    /// </summary>
    public class InputPreviewProvider : BaseComponentPreviewProvider
    {
        public override string ComponentId => "input-item";
        public override string ComponentName => "输入框 (Input)";
        public override string ComponentDescription => "输入框组件用于文本输入，支持多种样式、尺寸、验证状态和 placeholder 占位符功能";

        public override VisualElement CreatePreview()
        {
            var container = CreatePreviewContainer("flex", "flex-col", "gap-4");

            // 基本输入框组
            var basicSection = new VisualElement();
            basicSection.AddToClassList("preview-section");

            var basicTitle = CreatePreviewTitle("基本输入框");
            basicSection.Add(basicTitle);

            var basicInputContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 基本输入框
            var basicInput = DaisyInput.Create("基本输入框", "请输入内容...")
                .OnValueChanged(value => UIEventSystem.TriggerStatusMessage($"输入值: {value}"));
            basicInputContainer.Add(basicInput);

            // 预设内容输入框
            var presetInput = DaisyInput.Create("预设内容", "输入内容");
            presetInput.Value = "预设内容";
            basicInputContainer.Add(presetInput);

            // 只读输入框
            var readonlyInput = DaisyInput.Create("只读输入框");
            readonlyInput.Value = "只读内容";
            readonlyInput.ReadOnly();
            basicInputContainer.Add(readonlyInput);

            basicSection.Add(basicInputContainer);
            container.Add(basicSection);

            // Placeholder 功能演示组
            var placeholderSection = new VisualElement();
            placeholderSection.AddToClassList("preview-section");

            var placeholderTitle = CreatePreviewTitle("Placeholder 功能演示");
            placeholderSection.Add(placeholderTitle);

            var placeholderContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 基础 placeholder 演示
            var basicPlaceholderInput = DaisyInput.Create("基础 Placeholder", "请输入您的姓名");
            placeholderContainer.Add(basicPlaceholderInput);

            // 不同类型的 placeholder 演示
            var emailPlaceholderInput = DaisyInput.Create("邮箱 Placeholder", "请输入您的邮箱地址")
                .SetType("email");
            placeholderContainer.Add(emailPlaceholderInput);

            var passwordPlaceholderInput = DaisyInput.Create("密码 Placeholder", "请输入至少8位密码")
                .SetType("password");
            placeholderContainer.Add(passwordPlaceholderInput);

            var numberPlaceholderInput = DaisyInput.Create("数字 Placeholder", "请输入您的年龄")
                .SetType("number");
            placeholderContainer.Add(numberPlaceholderInput);

            // 长 placeholder 文本演示
            var longPlaceholderInput = DaisyInput.Create("长文本 Placeholder", "请输入详细的个人信息，包括姓名、年龄、职业、联系方式等相关信息");
            placeholderContainer.Add(longPlaceholderInput);

            // 动态 placeholder 演示
            var dynamicPlaceholderInput = DaisyInput.Create("动态 Placeholder", "初始占位符文本");
            placeholderContainer.Add(dynamicPlaceholderInput);

            // 添加按钮来演示动态修改 placeholder
            var buttonContainer = new VisualElement();
            buttonContainer.style.flexDirection = FlexDirection.Row;
            buttonContainer.style.marginTop = 10;
            buttonContainer.AddToClassList("flex");
            buttonContainer.AddToClassList("gap-2");

            var changeButton1 = new Button(() =>
            {
                dynamicPlaceholderInput.SetPlaceholder("占位符已更改为新文本");
            });
            changeButton1.text = "更改 Placeholder";
            changeButton1.AddToClassList("btn");
            changeButton1.AddToClassList("btn-sm");
            changeButton1.AddToClassList("btn-primary");
            buttonContainer.Add(changeButton1);

            var changeButton2 = new Button(() =>
            {
                dynamicPlaceholderInput.SetPlaceholder("请输入更新后的内容");
            });
            changeButton2.text = "再次更改";
            changeButton2.AddToClassList("btn");
            changeButton2.AddToClassList("btn-sm");
            changeButton2.AddToClassList("btn-secondary");
            buttonContainer.Add(changeButton2);

            var resetButton = new Button(() =>
            {
                dynamicPlaceholderInput.SetPlaceholder("初始占位符文本");
            });
            resetButton.text = "重置";
            resetButton.AddToClassList("btn");
            resetButton.AddToClassList("btn-sm");
            resetButton.AddToClassList("btn-ghost");
            buttonContainer.Add(resetButton);

            placeholderContainer.Add(buttonContainer);

            // 不同状态下的 placeholder 演示
            var stateDescription = new Label("不同状态下的 Placeholder 演示：");
            stateDescription.AddToClassList("text-sm");
            stateDescription.AddToClassList("text-gray-600");
            stateDescription.style.marginTop = 15;
            stateDescription.style.marginBottom = 10;
            placeholderContainer.Add(stateDescription);

            var errorPlaceholderInput = DaisyInput.Create("错误状态", "请输入正确的邮箱格式")
                .SetError()
                .SetHelperText("邮箱格式不正确");
            placeholderContainer.Add(errorPlaceholderInput);

            var successPlaceholderInput = DaisyInput.Create("成功状态", "请输入用户名")
                .SetSuccess()
                .SetHelperText("用户名可用");
            placeholderContainer.Add(successPlaceholderInput);

            var disabledPlaceholderInput = DaisyInput.Create("禁用状态", "此输入框已禁用")
                .SetDisabled();
            placeholderContainer.Add(disabledPlaceholderInput);

            // 带初始值的输入框（placeholder 应该隐藏）
            var prefilledPlaceholderInput = DaisyInput.Create("预填充内容", "这个占位符应该被隐藏")
                .SetValue("张三")
                .SetHelperText("这个输入框有初始值，占位符应该隐藏");
            placeholderContainer.Add(prefilledPlaceholderInput);

            placeholderSection.Add(placeholderContainer);
            container.Add(placeholderSection);

            // 输入框尺寸组
            var sizeSection = new VisualElement();
            sizeSection.AddToClassList("preview-section");

            var sizeTitle = CreatePreviewTitle("输入框尺寸");
            sizeSection.Add(sizeTitle);

            var sizeInputContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 超小输入框
            var extraSmallInput = DaisyInput.Create("超小尺寸", "超小输入框").ExtraSmall();
            sizeInputContainer.Add(extraSmallInput);

            // 小输入框
            var smallInput = DaisyInput.Create("小尺寸", "小尺寸输入框").Small();
            sizeInputContainer.Add(smallInput);

            // 中等输入框
            var mediumInput = DaisyInput.Create("中等尺寸", "中等输入框").Medium();
            sizeInputContainer.Add(mediumInput);

            // 大输入框
            var largeInput = DaisyInput.Create("大尺寸", "大尺寸输入框").Large();
            sizeInputContainer.Add(largeInput);

            // 超大输入框
            var extraLargeInput = DaisyInput.Create("超大尺寸", "超大输入框").ExtraLarge();
            sizeInputContainer.Add(extraLargeInput);


            sizeSection.Add(sizeInputContainer);
            container.Add(sizeSection);

            // 颜色变体组
            var colorSection = new VisualElement();
            colorSection.AddToClassList("preview-section");

            var colorTitle = CreatePreviewTitle("颜色变体");
            colorSection.Add(colorTitle);

            var colorInputContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 中性色
            var neutralInput = DaisyInput.Create("中性色", "中性色输入框").Neutral();
            colorInputContainer.Add(neutralInput);

            // 主要色
            var primaryInput = DaisyInput.Create("主要色", "主要色输入框").Primary();
            colorInputContainer.Add(primaryInput);

            // 次要色
            var secondaryInput = DaisyInput.Create("次要色", "次要色输入框").Secondary();
            colorInputContainer.Add(secondaryInput);

            // 强调色
            var accentInput = DaisyInput.Create("强调色", "强调色输入框").Accent();
            colorInputContainer.Add(accentInput);

            colorSection.Add(colorInputContainer);
            container.Add(colorSection);

            // 输入类型组
            var typeSection = new VisualElement();
            typeSection.AddToClassList("preview-section");

            var typeTitle = CreatePreviewTitle("输入类型");
            typeSection.Add(typeTitle);

            var typeInputContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 文本输入
            var textInput = DaisyInput.Text("文本输入框");
            typeInputContainer.Add(textInput);

            // 密码输入
            var passwordInput = DaisyInput.Password("密码输入框");
            typeInputContainer.Add(passwordInput);

            // 邮箱输入
            var emailInput = DaisyInput.Email("邮箱输入框");
            typeInputContainer.Add(emailInput);

            // 数字输入
            var numberInput = DaisyInput.Number("数字输入框");
            typeInputContainer.Add(numberInput);

            // 搜索输入
            var searchInput = DaisyInput.Search("搜索输入框");
            typeInputContainer.Add(searchInput);

            typeSection.Add(typeInputContainer);
            container.Add(typeSection);

            // 输入框状态组
            var stateSection = new VisualElement();
            stateSection.AddToClassList("preview-section");

            var stateTitle = CreatePreviewTitle("输入框状态");
            stateSection.Add(stateTitle);

            var stateInputContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 正常状态
            var normalInput = DaisyInput.Create("正常状态", "正常输入框");
            stateInputContainer.Add(normalInput);

            // 成功状态
            var successInput = DaisyInput.Create("成功状态", "成功输入框").Success();
            stateInputContainer.Add(successInput);

            // 警告状态
            var warningInput = DaisyInput.Create("警告状态", "警告输入框").Warning();
            stateInputContainer.Add(warningInput);

            // 错误状态
            var errorInput = DaisyInput.Create("错误状态", "错误输入框").Error();
            stateInputContainer.Add(errorInput);

            // 信息状态
            var infoInput = DaisyInput.Create("信息状态", "信息输入框").Info();
            stateInputContainer.Add(infoInput);

            // 禁用状态
            var disabledInput = DaisyInput.Create("禁用状态", "禁用的输入框").Disabled();
            stateInputContainer.Add(disabledInput);

            stateSection.Add(stateInputContainer);
            container.Add(stateSection);

            // 修饰符组
            var modifierSection = new VisualElement();
            modifierSection.AddToClassList("preview-section");

            var modifierTitle = CreatePreviewTitle("修饰符样式");
            modifierSection.Add(modifierTitle);

            var modifierInputContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 边框样式
            var borderedInput = DaisyInput.Create("边框样式", "边框输入框").SetBordered();
            modifierInputContainer.Add(borderedInput);

            // 幽灵样式
            var ghostInput = DaisyInput.Create("幽灵样式", "幽灵输入框").SetGhost();
            modifierInputContainer.Add(ghostInput);

            // 可增长样式
            var growInput = DaisyInput.Create("可增长", "可增长输入框").Grow();
            modifierInputContainer.Add(growInput);

            modifierSection.Add(modifierInputContainer);
            container.Add(modifierSection);

            return container;
        }

        public override string GetCodeExample()
        {
            return @"// 基本用法
using BlastingDesign.UI.DaisyUI.Components.DataInput;

var input = DaisyInput.Create(""标签文本"", ""占位符文本"")
    .OnValueChanged(value => Debug.Log($""输入值: {value}""));

// 设置初始值
input.SetValue(""初始内容"");

// Placeholder 占位符功能
var placeholderInput = DaisyInput.Create(""用户名"", ""请输入您的用户名"");

// 动态修改 placeholder
placeholderInput.SetPlaceholder(""请输入新的占位符文本"");

// 获取当前 placeholder
string placeholder = placeholderInput.Placeholder;

// 不同类型的 placeholder
var emailInput = DaisyInput.Create(""邮箱"", ""请输入您的邮箱地址"").SetType(""email"");
var passwordInput = DaisyInput.Create(""密码"", ""请输入至少8位密码"").SetType(""password"");
var numberInput = DaisyInput.Create(""年龄"", ""请输入您的年龄"").SetType(""number"");

// 输入类型（静态工厂方法）
var textInput = DaisyInput.Text(""文本输入框"");
var passwordInput2 = DaisyInput.Password(""密码输入框"");
var emailInput2 = DaisyInput.Email(""邮箱输入框"");
var numberInput2 = DaisyInput.Number(""数字输入框"");
var searchInput = DaisyInput.Search(""搜索输入框"");

// 尺寸变体（链式调用）
input.ExtraSmall();     // 超小尺寸
input.Small();          // 小尺寸
input.Medium();         // 中等尺寸
input.Large();          // 大尺寸
input.ExtraLarge();     // 超大尺寸

// 颜色变体
input.Neutral();        // 中性色
input.Primary();        // 主要色
input.Secondary();      // 次要色
input.Accent();         // 强调色

// 状态变体
input.Success();        // 成功状态
input.Warning();        // 警告状态
input.Error();          // 错误状态
input.Info();           // 信息状态

// 修饰符样式
input.SetBordered();    // 边框样式
input.SetGhost();       // 幽灵样式
input.Grow();           // 可增长样式
input.JoinItem();       // 连接项（用于输入组）

// 状态控制
input.Disabled();       // 禁用状态
input.ReadOnly();       // 只读状态

// 获取输入值
string value = input.Value;

// 清空输入框
input.Value = """";

// 设置焦点
input.Focus();

// 添加到容器
container.Add(input);";
        }

        public override string GetUsageInstructions()
        {
            return @"输入框组件使用说明：

1. 基本用法：
   - 使用 DaisyInput.Create() 创建输入框
   - 设置标签文本和占位符文本

2. Placeholder 占位符功能：
   - 占位符在输入框为空时显示提示文本
   - 获得焦点时自动隐藏，失去焦点且为空时重新显示
   - 支持动态修改：SetPlaceholder(string)
   - 获取当前占位符：Placeholder 属性
   - 不同尺寸自动适配样式和位置

3. 输入类型（静态工厂方法）：
   - DaisyInput.Text()：文本输入框
   - DaisyInput.Password()：密码输入框
   - DaisyInput.Email()：邮箱输入框
   - DaisyInput.Number()：数字输入框
   - DaisyInput.Search()：搜索输入框

4. 尺寸变体：
   - ExtraSmall()：超小尺寸 (xs)
   - Small()：小尺寸 (sm)
   - Medium()：中等尺寸 (md，默认)
   - Large()：大尺寸 (lg)
   - ExtraLarge()：超大尺寸 (xl)

5. 颜色变体：
   - Neutral()：中性色
   - Primary()：主要色
   - Secondary()：次要色
   - Accent()：强调色

6. 状态变体：
   - Success()：成功状态
   - Warning()：警告状态
   - Error()：错误状态
   - Info()：信息状态

7. 修饰符样式：
   - SetBordered()：边框样式
   - SetGhost()：幽灵样式（透明背景）
   - Grow()：可增长样式
   - JoinItem()：连接项（用于输入组）

8. 状态控制：
   - Disabled()：禁用状态
   - ReadOnly()：只读状态

9. 值操作：
   - SetValue(string)：设置输入值
   - Value 属性：获取/设置输入值
   - Reset()：重置到初始状态

10. 事件处理：
    - OnValueChanged(Action<string>)：值改变事件
    - Focus()：设置焦点
    - Blur()：失去焦点

11. Placeholder 最佳实践：
    - 提供清晰、简洁的占位符文本
    - 占位符应该描述期望的输入内容或格式
    - 避免过长的占位符文本，保持可读性
    - 在不同状态下测试占位符的显示效果
    - 考虑不同尺寸输入框的占位符显示

12. 一般最佳实践：
    - 提供清晰的标签和占位符文本
    - 合理使用验证状态提示用户
    - 为必填字段提供明确的视觉指示
    - 使用适当的输入类型提升用户体验
    - 考虑输入框的可访问性";
        }

        public override string[] GetSupportedVariants()
        {
            return new string[]
            {
                "bordered",
                "ghost",
                "neutral",
                "primary",
                "secondary",
                "accent",
                "success",
                "warning",
                "error",
                "info"
            };
        }

        public override VisualElement CreateVariantPreview(string variant)
        {
            var container = CreatePreviewContainer("flex", "flex-col", "gap-2");

            var input = DaisyInput.Create($"{variant.ToUpper()} Input", $"{variant} 样式输入框");

            switch (variant.ToLower())
            {
                case "bordered":
                    input.SetBordered();
                    break;
                case "ghost":
                    input.SetGhost();
                    break;
                case "neutral":
                    input.Neutral();
                    break;
                case "primary":
                    input.Primary();
                    break;
                case "secondary":
                    input.Secondary();
                    break;
                case "accent":
                    input.Accent();
                    break;
                case "success":
                    input.Success();
                    break;
                case "warning":
                    input.Warning();
                    break;
                case "error":
                    input.Error();
                    break;
                case "info":
                    input.Info();
                    break;
                default:
                    return null;
            }

            container.Add(input);
            return container;
        }

        public override string GetVariantCodeExample(string variant)
        {
            return variant.ToLower() switch
            {
                "bordered" => @"var input = DaisyInput.Create(""Bordered"", ""边框样式"").SetBordered();",
                "ghost" => @"var input = DaisyInput.Create(""Ghost"", ""幽灵样式"").SetGhost();",
                "neutral" => @"var input = DaisyInput.Create(""Neutral"", ""中性色样式"").Neutral();",
                "primary" => @"var input = DaisyInput.Create(""Primary"", ""主要色样式"").Primary();",
                "secondary" => @"var input = DaisyInput.Create(""Secondary"", ""次要色样式"").Secondary();",
                "accent" => @"var input = DaisyInput.Create(""Accent"", ""强调色样式"").Accent();",
                "success" => @"var input = DaisyInput.Create(""Success"", ""成功状态"").Success();",
                "warning" => @"var input = DaisyInput.Create(""Warning"", ""警告状态"").Warning();",
                "error" => @"var input = DaisyInput.Create(""Error"", ""错误状态"").Error();",
                "info" => @"var input = DaisyInput.Create(""Info"", ""信息状态"").Info();",
                _ => null
            };
        }
    }
}
