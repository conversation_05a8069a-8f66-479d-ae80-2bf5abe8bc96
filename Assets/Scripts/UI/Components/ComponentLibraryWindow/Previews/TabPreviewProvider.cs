using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Components.Navigation;
using BlastingDesign.UI.Core;

namespace BlastingDesign.UI.Components.Previews
{
    /// <summary>
    /// Tab组件预览提供者
    /// </summary>
    public class TabPreviewProvider : BaseComponentPreviewProvider
    {
        public override string ComponentId => "tab-item";
        public override string ComponentName => "选项卡 (Tab)";
        public override string ComponentDescription => "选项卡组件用于在多个内容面板之间切换，支持多种样式变体、尺寸选项和布局方向";

        public override VisualElement CreatePreview()
        {
            var container = CreatePreviewContainer("flex", "flex-col", "gap-6");

            // 基本Tab组
            var basicSection = new VisualElement();
            basicSection.AddToClassList("preview-section");

            var basicTitle = CreatePreviewTitle("基本选项卡");
            basicSection.Add(basicTitle);

            var basicTabContainer = CreatePreviewContainer("flex", "flex-col", "gap-4");

            // 基本水平Tab
            var basicTab = DaisyTab.Create("tab1")
                .AddTab("tab1", "首页", CreateTabContent("欢迎来到首页", "这里是首页内容，包含网站的主要信息和导航。"))
                .AddTab("tab2", "关于", CreateTabContent("关于我们", "了解更多关于我们公司的信息、历史和团队。"))
                .AddTab("tab3", "服务", CreateTabContent("我们的服务", "查看我们提供的各种专业服务和解决方案。"))
                .AddTab("tab4", "联系", CreateTabContent("联系方式", "通过以下方式与我们取得联系。"))
                .OnTabChange(tabId => UIEventSystem.TriggerStatusMessage($"切换到选项卡: {tabId}"));

            basicTabContainer.Add(basicTab);
            basicSection.Add(basicTabContainer);
            container.Add(basicSection);

            // Tab样式变体组
            var variantSection = new VisualElement();
            variantSection.AddToClassList("preview-section");

            var variantTitle = CreatePreviewTitle("样式变体");
            variantSection.Add(variantTitle);

            var variantTabContainer = CreatePreviewContainer("flex", "flex-col", "gap-6");

            // Bordered样式
            var borderedTab = DaisyTab.Bordered("bordered1")
                .AddTab("bordered1", "文档", CreateTabContent("文档中心", "查看完整的API文档和使用指南。"))
                .AddTab("bordered2", "示例", CreateTabContent("代码示例", "浏览各种实用的代码示例。"))
                .AddTab("bordered3", "教程", CreateTabContent("学习教程", "跟随教程快速上手。"));

            var borderedLabel = new Label("Bordered 样式");
            borderedLabel.AddToClassList("text-sm");
            borderedLabel.AddToClassList("font-semibold");
            borderedLabel.style.marginBottom = 8;
            variantTabContainer.Add(borderedLabel);
            variantTabContainer.Add(borderedTab);

            // Lifted样式
            var liftedTab = DaisyTab.Lifted("lifted1")
                .AddTab("lifted1", "设置", CreateTabContent("系统设置", "配置系统的各项参数。"))
                .AddTab("lifted2", "用户", CreateTabContent("用户管理", "管理系统中的用户账户。"))
                .AddTab("lifted3", "安全", CreateTabContent("安全选项", "配置安全相关设置。"));

            var liftedLabel = new Label("Lifted 样式");
            liftedLabel.AddToClassList("text-sm");
            liftedLabel.AddToClassList("font-semibold");
            liftedLabel.style.marginBottom = 8;
            variantTabContainer.Add(liftedLabel);
            variantTabContainer.Add(liftedTab);

            // Boxed样式
            var boxedTab = DaisyTab.Boxed("boxed1")
                .AddTab("boxed1", "概览", CreateTabContent("项目概览", "查看项目的整体状态和进度。"))
                .AddTab("boxed2", "统计", CreateTabContent("数据统计", "详细的数据分析和报告。"))
                .AddTab("boxed3", "报告", CreateTabContent("生成报告", "创建和导出各种报告。"));

            var boxedLabel = new Label("Boxed 样式");
            boxedLabel.AddToClassList("text-sm");
            boxedLabel.AddToClassList("font-semibold");
            boxedLabel.style.marginBottom = 8;
            variantTabContainer.Add(boxedLabel);
            variantTabContainer.Add(boxedTab);

            variantSection.Add(variantTabContainer);
            container.Add(variantSection);

            // Tab尺寸组
            var sizeSection = new VisualElement();
            sizeSection.AddToClassList("preview-section");

            var sizeTitle = CreatePreviewTitle("选项卡尺寸");
            sizeSection.Add(sizeTitle);

            var sizeTabContainer = CreatePreviewContainer("flex", "flex-col", "gap-4");

            // 超小尺寸
            var extraSmallTab = DaisyTab.Create("xs1")
                .SetExtraSmall()
                .AddTab("xs1", "XS", CreateTabContent("超小选项卡", "这是超小尺寸的选项卡内容。"))
                .AddTab("xs2", "小", CreateTabContent("超小选项卡", "适合紧凑的界面设计。"));

            var xsLabel = new Label("超小尺寸 (XS)");
            xsLabel.AddToClassList("text-sm");
            xsLabel.style.marginBottom = 4;
            sizeTabContainer.Add(xsLabel);
            sizeTabContainer.Add(extraSmallTab);

            // 小尺寸
            var smallTab = DaisyTab.Create("sm1")
                .SetSmall()
                .AddTab("sm1", "Small", CreateTabContent("小尺寸选项卡", "这是小尺寸的选项卡内容。"))
                .AddTab("sm2", "选项卡", CreateTabContent("小尺寸选项卡", "适合大多数应用场景。"));

            var smLabel = new Label("小尺寸 (SM)");
            smLabel.AddToClassList("text-sm");
            smLabel.style.marginBottom = 4;
            sizeTabContainer.Add(smLabel);
            sizeTabContainer.Add(smallTab);

            // 中等尺寸
            var mediumTab = DaisyTab.Create("md1")
                .SetMedium()
                .AddTab("md1", "Medium", CreateTabContent("中等尺寸选项卡", "这是中等尺寸的选项卡内容。"))
                .AddTab("md2", "选项卡", CreateTabContent("中等尺寸选项卡", "默认的标准尺寸。"));

            var mdLabel = new Label("中等尺寸 (MD) - 默认");
            mdLabel.AddToClassList("text-sm");
            mdLabel.style.marginBottom = 4;
            sizeTabContainer.Add(mdLabel);
            sizeTabContainer.Add(mediumTab);

            // 大尺寸
            var largeTab = DaisyTab.Create("lg1")
                .SetLarge()
                .AddTab("lg1", "Large", CreateTabContent("大尺寸选项卡", "这是大尺寸的选项卡内容。"))
                .AddTab("lg2", "选项卡", CreateTabContent("大尺寸选项卡", "适合需要更多空间的界面。"));

            var lgLabel = new Label("大尺寸 (LG)");
            lgLabel.AddToClassList("text-sm");
            lgLabel.style.marginBottom = 4;
            sizeTabContainer.Add(lgLabel);
            sizeTabContainer.Add(largeTab);

            // 超大尺寸
            var extraLargeTab = DaisyTab.Create("xl1")
                .SetExtraLarge()
                .AddTab("xl1", "Extra Large", CreateTabContent("超大尺寸选项卡", "这是超大尺寸的选项卡内容。"))
                .AddTab("xl2", "选项卡", CreateTabContent("超大尺寸选项卡", "适合大屏幕和重要导航。"));

            var xlLabel = new Label("超大尺寸 (XL)");
            xlLabel.AddToClassList("text-sm");
            xlLabel.style.marginBottom = 4;
            sizeTabContainer.Add(xlLabel);
            sizeTabContainer.Add(extraLargeTab);

            sizeSection.Add(sizeTabContainer);
            container.Add(sizeSection);

            // 垂直布局组
            var directionSection = new VisualElement();
            directionSection.AddToClassList("preview-section");

            var directionTitle = CreatePreviewTitle("布局方向");
            directionSection.Add(directionTitle);

            var directionTabContainer = CreatePreviewContainer("flex", "flex-col", "gap-6");

            // 水平布局
            var horizontalTab = DaisyTab.Create("horizontal1")
                .SetDirection(TabDirection.Row)
                .SetVariant(TabVariant.Bordered)
                .AddTab("horizontal1", "水平", CreateTabContent("水平布局", "这是水平布局的选项卡，标签页在上方。"))
                .AddTab("horizontal2", "布局", CreateTabContent("水平布局", "适合大多数应用场景的经典布局。"))
                .AddTab("horizontal3", "示例", CreateTabContent("水平布局", "标签页横向排列，内容在下方显示。"));

            var horizontalLabel = new Label("水平布局 (Row) - 默认");
            horizontalLabel.AddToClassList("text-sm");
            horizontalLabel.AddToClassList("font-semibold");
            horizontalLabel.style.marginBottom = 8;
            directionTabContainer.Add(horizontalLabel);
            directionTabContainer.Add(horizontalTab);

            // 垂直布局
            var verticalTab = DaisyTab.Create("vertical1")
                .SetDirection(TabDirection.Column)
                .SetVariant(TabVariant.Lifted)
                .AddTab("vertical1", "垂直", CreateTabContent("垂直布局", "这是垂直布局的选项卡，标签页在左侧。"))
                .AddTab("vertical2", "布局", CreateTabContent("垂直布局", "适合侧边栏导航的布局方式。"))
                .AddTab("vertical3", "示例", CreateTabContent("垂直布局", "标签页垂直排列，内容在右侧显示。"));

            var verticalLabel = new Label("垂直布局 (Column)");
            verticalLabel.AddToClassList("text-sm");
            verticalLabel.AddToClassList("font-semibold");
            verticalLabel.style.marginBottom = 8;
            directionTabContainer.Add(verticalLabel);
            directionTabContainer.Add(verticalTab);

            directionSection.Add(directionTabContainer);
            container.Add(directionSection);

            // 动态操作组
            var dynamicSection = new VisualElement();
            dynamicSection.AddToClassList("preview-section");

            var dynamicTitle = CreatePreviewTitle("动态操作演示");
            dynamicSection.Add(dynamicTitle);

            var dynamicTabContainer = CreatePreviewContainer("flex", "flex-col", "gap-4");

            // 创建动态Tab
            var dynamicTab = DaisyTab.Create("dynamic1")
                .AddTab("dynamic1", "Tab 1", CreateTabContent("动态Tab 1", "这是第一个动态创建的选项卡。"))
                .AddTab("dynamic2", "Tab 2", CreateTabContent("动态Tab 2", "这是第二个动态创建的选项卡。"));

            dynamicTabContainer.Add(dynamicTab);

            // 操作按钮
            var buttonContainer = new VisualElement();
            buttonContainer.style.flexDirection = FlexDirection.Row;
            buttonContainer.style.marginTop = 10;
            buttonContainer.AddToClassList("flex");
            buttonContainer.AddToClassList("gap-2");

            var addTabCounter = 3;
            var addTabButton = new Button(() =>
            {
                var tabId = $"dynamic{addTabCounter}";
                var tabLabel = $"Tab {addTabCounter}";
                var content = CreateTabContent($"动态Tab {addTabCounter}", $"这是第{addTabCounter}个动态添加的选项卡。");

                dynamicTab.AddTab(tabId, tabLabel, content);
                addTabCounter++;

                UIEventSystem.TriggerStatusMessage($"已添加新选项卡: {tabLabel}");
            });
            addTabButton.text = "添加Tab";
            addTabButton.AddToClassList("btn");
            addTabButton.AddToClassList("btn-sm");
            addTabButton.AddToClassList("btn-primary");
            buttonContainer.Add(addTabButton);

            var removeTabButton = new Button(() =>
            {
                if (addTabCounter > 3)
                {
                    var tabId = $"dynamic{addTabCounter - 1}";
                    dynamicTab.RemoveTab(tabId);
                    addTabCounter--;
                    UIEventSystem.TriggerStatusMessage($"已移除选项卡: {tabId}");
                }
                else
                {
                    UIEventSystem.TriggerStatusMessage("至少需要保留两个选项卡");
                }
            });
            removeTabButton.text = "移除Tab";
            removeTabButton.AddToClassList("btn");
            removeTabButton.AddToClassList("btn-sm");
            removeTabButton.AddToClassList("btn-secondary");
            buttonContainer.Add(removeTabButton);

            var switchTabButton = new Button(() =>
            {
                var currentActive = dynamicTab.ActiveTabId;
                var nextTab = currentActive == "dynamic1" ? "dynamic2" : "dynamic1";
                dynamicTab.SetActiveTab(nextTab);
                UIEventSystem.TriggerStatusMessage($"切换到选项卡: {nextTab}");
            });
            switchTabButton.text = "切换Tab";
            switchTabButton.AddToClassList("btn");
            switchTabButton.AddToClassList("btn-sm");
            switchTabButton.AddToClassList("btn-ghost");
            buttonContainer.Add(switchTabButton);

            dynamicTabContainer.Add(buttonContainer);
            dynamicSection.Add(dynamicTabContainer);
            container.Add(dynamicSection);

            return container;
        }

        private VisualElement CreateTabContent(string title, string description)
        {
            var content = new VisualElement();
            content.AddToClassList("p-4");
            content.style.minHeight = 120;

            var titleLabel = new Label(title);
            titleLabel.AddToClassList("text-lg");
            titleLabel.AddToClassList("font-bold");
            titleLabel.AddToClassList("text-base-content");
            titleLabel.style.marginBottom = 8;
            content.Add(titleLabel);

            var descLabel = new Label(description);
            descLabel.AddToClassList("text-base-content");
            descLabel.style.opacity = 0.7f;
            descLabel.style.whiteSpace = WhiteSpace.Normal;
            content.Add(descLabel);

            return content;
        }

        public override string GetCodeExample()
        {
            return @"// 基本用法
using DaisyUI.Components.Navigation;

var tab = DaisyTab.Create(""activeTabId"")
    .AddTab(""tab1"", ""首页"", homeContent)
    .AddTab(""tab2"", ""关于"", aboutContent)
    .AddTab(""tab3"", ""联系"", contactContent)
    .OnTabChange(tabId => Debug.Log($""切换到: {tabId}""));

// 静态工厂方法（预设样式）
var borderedTab = DaisyTab.Bordered(""tab1"");
var liftedTab = DaisyTab.Lifted(""tab1"");
var boxedTab = DaisyTab.Boxed(""tab1"");

// 样式变体（链式调用）
tab.SetVariant(TabVariant.Bordered);   // 边框样式
tab.SetVariant(TabVariant.Lifted);     // 抬起样式
tab.SetVariant(TabVariant.Boxed);      // 盒子样式

// 尺寸变体
tab.SetExtraSmall();    // 超小尺寸
tab.SetSmall();         // 小尺寸
tab.SetMedium();        // 中等尺寸（默认）
tab.SetLarge();         // 大尺寸
tab.SetExtraLarge();    // 超大尺寸

// 布局方向
tab.SetDirection(TabDirection.Row);      // 水平布局（默认）
tab.SetDirection(TabDirection.Column);   // 垂直布局

// 动态操作
tab.AddTab(""newTab"", ""新标签"", newContent);   // 添加新标签页
tab.RemoveTab(""oldTab"");                        // 移除标签页
tab.SetActiveTab(""targetTab"");                  // 切换到指定标签页

// 获取当前激活的标签页
string activeTab = tab.ActiveTabId;

// 添加标签页内容
var content = new VisualElement();
content.Add(new Label(""标签页内容""));
tab.AddTabPanel(""tabId"", content);

// 事件处理
tab.OnTabChange(tabId => {
    Debug.Log($""用户切换到标签页: {tabId}"");
    // 处理标签页切换逻辑
});

// 添加到容器
container.Add(tab);";
        }

        public override string GetUsageInstructions()
        {
            return @"Tab组件使用说明：

1. 基本用法：
   - 使用 DaisyTab.Create() 创建选项卡容器
   - 使用 AddTab() 添加标签页和内容
   - 指定初始激活的标签页ID

2. 静态工厂方法：
   - DaisyTab.Create()：基本选项卡
   - DaisyTab.Bordered()：边框样式选项卡
   - DaisyTab.Lifted()：抬起样式选项卡
   - DaisyTab.Boxed()：盒子样式选项卡

3. 样式变体：
   - None：默认样式
   - Bordered：底部边框样式
   - Lifted：抬起的卡片样式
   - Boxed：独立的盒子样式

4. 尺寸变体：
   - ExtraSmall (XS)：超小尺寸
   - Small (SM)：小尺寸
   - Medium (MD)：中等尺寸（默认）
   - Large (LG)：大尺寸
   - ExtraLarge (XL)：超大尺寸

5. 布局方向：
   - Row：水平布局（标签在上方，默认）
   - Column：垂直布局（标签在左侧）

6. 动态操作：
   - AddTab()：动态添加新标签页
   - RemoveTab()：移除指定标签页
   - SetActiveTab()：程序化切换标签页
   - AddTabPanel()：为标签页添加内容

7. 事件处理：
   - OnTabChange()：标签页切换事件
   - 传入回调函数处理切换逻辑

8. 内容管理：
   - 每个标签页可以包含任意的VisualElement内容
   - 内容会自动显示/隐藏根据标签页状态
   - 支持复杂的嵌套布局

9. 最佳实践：
   - 标签页标题要简洁明了
   - 不要创建太多标签页（建议不超过7个）
   - 为重要操作提供快捷键切换
   - 考虑移动端的触摸友好性
   - 为动态添加的标签页提供关闭功能

10. 响应式设计：
    - 在较小屏幕上考虑使用垂直布局
    - 较多标签页时考虑可滚动设计
    - 适当调整不同设备上的尺寸";
        }

        public override string[] GetSupportedVariants()
        {
            return new string[]
            {
                "bordered",
                "lifted",
                "boxed",
                "xs",
                "sm",
                "md",
                "lg",
                "xl",
                "row",
                "column"
            };
        }

        public override VisualElement CreateVariantPreview(string variant)
        {
            var container = CreatePreviewContainer("flex", "flex-col", "gap-2");

            var tab = DaisyTab.Create($"{variant}1");

            switch (variant.ToLower())
            {
                case "bordered":
                    tab.SetVariant(TabVariant.Bordered);
                    break;
                case "lifted":
                    tab.SetVariant(TabVariant.Lifted);
                    break;
                case "boxed":
                    tab.SetVariant(TabVariant.Boxed);
                    break;
                case "xs":
                    tab.SetExtraSmall();
                    break;
                case "sm":
                    tab.SetSmall();
                    break;
                case "md":
                    tab.SetMedium();
                    break;
                case "lg":
                    tab.SetLarge();
                    break;
                case "xl":
                    tab.SetExtraLarge();
                    break;
                case "row":
                    tab.SetDirection(TabDirection.Row);
                    break;
                case "column":
                    tab.SetDirection(TabDirection.Column);
                    break;
                default:
                    return null;
            }

            // 添加示例标签页
            tab.AddTab($"{variant}1", "Tab 1", CreateTabContent($"{variant.ToUpper()} 样式", $"这是 {variant} 样式的选项卡内容。"))
               .AddTab($"{variant}2", "Tab 2", CreateTabContent($"{variant.ToUpper()} 样式", $"展示 {variant} 变体的效果。"));

            container.Add(tab);
            return container;
        }

        public override string GetVariantCodeExample(string variant)
        {
            return variant.ToLower() switch
            {
                "bordered" => @"var tab = DaisyTab.Bordered(""tab1"")
    .AddTab(""tab1"", ""首页"", content1)
    .AddTab(""tab2"", ""关于"", content2);",

                "lifted" => @"var tab = DaisyTab.Lifted(""tab1"")
    .AddTab(""tab1"", ""首页"", content1)
    .AddTab(""tab2"", ""关于"", content2);",

                "boxed" => @"var tab = DaisyTab.Boxed(""tab1"")
    .AddTab(""tab1"", ""首页"", content1)
    .AddTab(""tab2"", ""关于"", content2);",

                "xs" => @"var tab = DaisyTab.Create(""tab1"")
    .SetExtraSmall()
    .AddTab(""tab1"", ""小"", content);",

                "sm" => @"var tab = DaisyTab.Create(""tab1"")
    .SetSmall()
    .AddTab(""tab1"", ""小"", content);",

                "md" => @"var tab = DaisyTab.Create(""tab1"")
    .SetMedium()
    .AddTab(""tab1"", ""中"", content);",

                "lg" => @"var tab = DaisyTab.Create(""tab1"")
    .SetLarge()
    .AddTab(""tab1"", ""大"", content);",

                "xl" => @"var tab = DaisyTab.Create(""tab1"")
    .SetExtraLarge()
    .AddTab(""tab1"", ""超大"", content);",

                "row" => @"var tab = DaisyTab.Create(""tab1"")
    .SetDirection(TabDirection.Row)
    .AddTab(""tab1"", ""水平"", content);",

                "column" => @"var tab = DaisyTab.Create(""tab1"")
    .SetDirection(TabDirection.Column)
    .AddTab(""tab1"", ""垂直"", content);",

                _ => null
            };
        }
    }
}