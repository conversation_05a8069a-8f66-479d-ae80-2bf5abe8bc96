using UnityEngine.UIElements;

namespace BlastingDesign.UI.Components.Previews
{
    /// <summary>
    /// 组件预览提供者接口
    /// 定义了组件预览展示的标准方法
    /// </summary>
    public interface IComponentPreviewProvider
    {
        /// <summary>
        /// 组件ID，用于标识组件类型
        /// </summary>
        string ComponentId { get; }

        /// <summary>
        /// 组件名称
        /// </summary>
        string ComponentName { get; }

        /// <summary>
        /// 组件描述
        /// </summary>
        string ComponentDescription { get; }

        /// <summary>
        /// 创建组件预览的可视化元素
        /// </summary>
        /// <returns>包含组件预览的VisualElement</returns>
        VisualElement CreatePreview();

        /// <summary>
        /// 获取组件的代码使用示例
        /// </summary>
        /// <returns>代码示例字符串</returns>
        string GetCodeExample();

        /// <summary>
        /// 获取组件的详细使用说明
        /// </summary>
        /// <returns>使用说明字符串</returns>
        string GetUsageInstructions();

        /// <summary>
        /// 获取组件支持的变体列表
        /// </summary>
        /// <returns>变体名称数组</returns>
        string[] GetSupportedVariants();

        /// <summary>
        /// 创建指定变体的预览
        /// </summary>
        /// <param name="variant">变体名称</param>
        /// <returns>变体预览的VisualElement，如果不支持则返回null</returns>
        VisualElement CreateVariantPreview(string variant);

        /// <summary>
        /// 获取指定变体的代码示例
        /// </summary>
        /// <param name="variant">变体名称</param>
        /// <returns>变体代码示例，如果不支持则返回null</returns>
        string GetVariantCodeExample(string variant);
    }
}
