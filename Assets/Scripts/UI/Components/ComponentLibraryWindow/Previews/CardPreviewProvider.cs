using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Components.DataDisplay;
using BlastingDesign.UI.DaisyUI.Builders;
using BlastingDesign.UI.Core;

namespace BlastingDesign.UI.Components.Previews
{
    /// <summary>
    /// 卡片组件预览提供者
    /// </summary>
    public class CardPreviewProvider : BaseComponentPreviewProvider
    {
        public override string ComponentId => "card-item";
        public override string ComponentName => "卡片 (Card)";
        public override string ComponentDescription => "卡片组件用于展示内容块，支持标题、内容、操作按钮等多种布局";

        public override VisualElement CreatePreview()
        {
            var container = CreatePreviewContainer("flex", "flex-col", "gap-4");

            // 基本卡片组
            var basicSection = new VisualElement();
            basicSection.AddToClassList("preview-section");

            var basicTitle = CreatePreviewTitle("基本卡片");
            basicSection.Add(basicTitle);

            var basicCardContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 基本卡片
            var basicCard = DaisyCard.Create("基本卡片");
            basicCard.SetContent("这是一个基本的DaisyUI卡片组件，用于展示内容。卡片可以包含文本、图片、按钮等各种元素。");
            basicCardContainer.Add(basicCard);

            // 带操作的卡片
            var actionCard = DaisyCard.Create("操作卡片");
            actionCard.SetContent("这个卡片包含操作按钮，用户可以与卡片进行交互。");

            var actionButton = DaisyBuilder.Button("查看详情")
                .SetPrimary()
                .SetSmall()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("卡片操作被点击"));
            actionCard.AddActions(actionButton);

            basicCardContainer.Add(actionCard);

            basicSection.Add(basicCardContainer);
            container.Add(basicSection);

            // 卡片样式组
            var styleSection = new VisualElement();
            styleSection.AddToClassList("preview-section");

            var styleTitle = CreatePreviewTitle("卡片样式");
            styleSection.Add(styleTitle);

            var styleCardContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 边框卡片
            var borderedCard = DaisyCard.Create("边框卡片");
            borderedCard.SetContent("这是一个带边框的卡片样式。");
            borderedCard.SetBordered();
            styleCardContainer.Add(borderedCard);

            // 紧凑卡片
            var compactCard = DaisyCard.Create("紧凑卡片");
            compactCard.SetContent("这是一个紧凑样式的卡片，内边距较小。");
            compactCard.SetCompact();
            styleCardContainer.Add(compactCard);

            // 玻璃效果卡片
            var glassCard = DaisyCard.Create("玻璃效果卡片");
            glassCard.SetContent("这是一个具有玻璃效果的卡片样式。");
            glassCard.SetGlass();
            styleCardContainer.Add(glassCard);

            styleSection.Add(styleCardContainer);
            container.Add(styleSection);

            // 复杂卡片组
            var complexSection = new VisualElement();
            complexSection.AddToClassList("preview-section");

            var complexTitle = CreatePreviewTitle("复杂卡片");
            complexSection.Add(complexTitle);

            var complexCardContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 多操作卡片
            var multiActionCard = DaisyCard.Create("多操作卡片");
            multiActionCard.SetContent("这个卡片包含多个操作按钮，展示了卡片的灵活性。");

            var editButton = DaisyBuilder.Button("编辑")
                .SetSecondary()
                .SetSmall()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("编辑操作"));
            var deleteButton = DaisyBuilder.Button("删除")
                .SetError()
                .SetSmall()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("删除操作"));

            multiActionCard.AddActions(editButton, deleteButton);

            complexCardContainer.Add(multiActionCard);

            complexSection.Add(complexCardContainer);
            container.Add(complexSection);

            return container;
        }

        public override string GetCodeExample()
        {
            return @"// 基本用法
using BlastingDesign.UI.DaisyUI.Components.DataDisplay;
using BlastingDesign.UI.DaisyUI.Builders;

var card = DaisyCard.Create(""卡片标题"");
card.SetContent(""卡片内容文本"");

// 设置卡片样式
card.SetBordered();     // 边框样式
card.SetCompact();      // 紧凑样式
card.SetGlass();        // 玻璃效果

// 添加操作按钮
var button = DaisyBuilder.Button(""操作"")
    .SetPrimary()
    .OnClick(() => Debug.Log(""卡片操作""));
card.AddActions(button);

// 添加多个操作
var editBtn = DaisyBuilder.Button(""编辑"").SetSecondary();
var deleteBtn = DaisyBuilder.Button(""删除"").SetError();
card.AddActions(editBtn, deleteBtn);

// 设置卡片内容（支持HTML）
card.SetContent(""<b>粗体文本</b> 和 <i>斜体文本</i>"");

// 清空操作按钮
card.ClearActions();

// 添加到容器
container.Add(card);";
        }

        public override string GetUsageInstructions()
        {
            return @"卡片组件使用说明：

1. 基本用法：
   - 使用 DaisyCard.Create() 创建卡片
   - 设置标题和内容文本

2. 样式选项：
   - SetBordered()：设置边框样式
   - SetCompact()：设置紧凑样式
   - SetGlass()：设置玻璃效果

3. 内容管理：
   - SetContent(string)：设置卡片内容
   - 支持富文本格式

4. 操作按钮：
   - AddAction(Button)：添加操作按钮
   - ClearActions()：清空所有操作按钮
   - 支持多个操作按钮

5. 布局建议：
   - 卡片适合展示结构化内容
   - 合理使用操作按钮数量
   - 考虑卡片在不同屏幕尺寸下的表现

6. 最佳实践：
   - 保持卡片内容简洁明了
   - 使用语义化的操作按钮
   - 合理组织卡片布局
   - 考虑卡片的可访问性";
        }

        public override string[] GetSupportedVariants()
        {
            return new string[]
            {
                "basic",
                "bordered",
                "compact",
                "glass",
                "with-actions"
            };
        }

        public override VisualElement CreateVariantPreview(string variant)
        {
            var container = CreatePreviewContainer("flex", "flex-col", "gap-2");

            var card = DaisyCard.Create($"{variant.ToUpper()} Card");
            card.SetContent($"这是一个 {variant} 样式的卡片示例。");

            switch (variant.ToLower())
            {
                case "basic":
                    // 基本样式，无需额外设置
                    break;
                case "bordered":
                    card.SetBordered();
                    break;
                case "compact":
                    card.SetCompact();
                    break;
                case "glass":
                    card.SetGlass();
                    break;
                case "with-actions":
                    var actionBtn = DaisyBuilder.Button("操作")
                        .SetPrimary()
                        .SetSmall();
                    card.AddActions(actionBtn);
                    break;
                default:
                    return null;
            }

            container.Add(card);
            return container;
        }

        public override string GetVariantCodeExample(string variant)
        {
            return variant.ToLower() switch
            {
                "basic" => @"var card = DaisyCard.Create(""基本卡片"");
card.SetContent(""基本卡片内容"");",
                "bordered" => @"var card = DaisyCard.Create(""边框卡片"");
card.SetContent(""边框卡片内容"");
card.SetBordered();",
                "compact" => @"var card = DaisyCard.Create(""紧凑卡片"");
card.SetContent(""紧凑卡片内容"");
card.SetCompact();",
                "glass" => @"var card = DaisyCard.Create(""玻璃卡片"");
card.SetContent(""玻璃卡片内容"");
card.SetGlass();",
                "with-actions" => @"var card = DaisyCard.Create(""操作卡片"");
card.SetContent(""带操作的卡片内容"");
var button = DaisyBuilder.Button(""操作"").SetPrimary();
card.AddActions(button);",
                _ => null
            };
        }
    }
}
