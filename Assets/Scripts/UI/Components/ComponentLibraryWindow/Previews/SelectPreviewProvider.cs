using System.Collections.Generic;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Components.DataInput;
using BlastingDesign.UI.Core;

namespace BlastingDesign.UI.Components.Previews
{
    /// <summary>
    /// 选择器组件预览提供者
    /// </summary>
    public class SelectPreviewProvider : BaseComponentPreviewProvider
    {
        public override string ComponentId => "select-item";
        public override string ComponentName => "选择器 (Select)";
        public override string ComponentDescription => "选择器组件用于下拉选择，支持单选和多选，提供多种样式和尺寸";

        public override VisualElement CreatePreview()
        {
            var container = CreatePreviewContainer("flex", "flex-col", "gap-4");

            // 基本选择器组
            var basicSection = new VisualElement();
            basicSection.AddToClassList("preview-section");

            var basicTitle = CreatePreviewTitle("基本选择器");
            basicSection.Add(basicTitle);

            var basicSelectContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 基本选择器
            var basicSelect = DaisySelect.Create("选择选项", new List<string> { "选项1", "选项2", "选项3" });
            basicSelect.OnValueChanged(value => UIEventSystem.TriggerStatusMessage($"选择了: {value}"));
            basicSelectContainer.Add(basicSelect);

            // 带默认值的选择器
            var defaultSelect = DaisySelect.Create("默认选择", "类型A", "类型B", "类型C");
            defaultSelect.SetValue("类型B");
            basicSelectContainer.Add(defaultSelect);

            // 多选选择器
            var multiSelect = DaisySelect.Create("多选选择器", new List<string> { "选项A", "选项B", "选项C", "选项D" });
            multiSelect.AllowMultiple = true;
            multiSelect.OnValueChanged(value =>
                UIEventSystem.TriggerStatusMessage($"选择了: {value}"));
            basicSelectContainer.Add(multiSelect);

            basicSection.Add(basicSelectContainer);
            container.Add(basicSection);

            // 选择器尺寸组
            var sizeSection = new VisualElement();
            sizeSection.AddToClassList("preview-section");

            var sizeTitle = CreatePreviewTitle("选择器尺寸");
            sizeSection.Add(sizeTitle);

            var sizeSelectContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 小选择器
            var smallSelect = DaisySelect.Create("小尺寸", "选项1", "选项2", "选项3");
            smallSelect.WithSize("sm");
            sizeSelectContainer.Add(smallSelect);

            // 中等选择器
            var mediumSelect = DaisySelect.Create("中等尺寸", "选项1", "选项2", "选项3");
            mediumSelect.WithSize("md");
            sizeSelectContainer.Add(mediumSelect);

            // 大选择器
            var largeSelect = DaisySelect.Create("大尺寸", "选项1", "选项2", "选项3");
            largeSelect.WithSize("lg");
            sizeSelectContainer.Add(largeSelect);

            sizeSection.Add(sizeSelectContainer);
            container.Add(sizeSection);

            // 选择器状态组
            var stateSection = new VisualElement();
            stateSection.AddToClassList("preview-section");

            var stateTitle = CreatePreviewTitle("选择器状态");
            stateSection.Add(stateTitle);

            var stateSelectContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 正常状态
            var normalSelect = DaisySelect.Create("正常状态", "选项1", "选项2", "选项3");
            stateSelectContainer.Add(normalSelect);

            // 禁用状态
            var disabledSelect = DaisySelect.Create("禁用状态", "选项1", "选项2", "选项3");
            disabledSelect.SetDisabled(true);
            stateSelectContainer.Add(disabledSelect);

            // 错误状态
            var errorSelect = DaisySelect.Create("错误状态", "选项1", "选项2", "选项3");
            errorSelect.SetModifier("error");
            stateSelectContainer.Add(errorSelect);

            stateSection.Add(stateSelectContainer);
            container.Add(stateSection);

            return container;
        }

        public override string GetCodeExample()
        {
            return @"// 基本用法
using BlastingDesign.UI.DaisyUI.Components.DataInput;
using System.Collections.Generic;

// 方式一：使用字符串数组
var select = DaisySelect.Create(""选择选项"", ""选项1"", ""选项2"", ""选项3"");

// 方式二：使用List<string>
var options = new List<string> { ""选项A"", ""选项B"", ""选项C"" };
var select2 = DaisySelect.Create(""选择选项"", options);

// 设置选择器样式
select.SetModifier(""bordered"");       // 边框样式
select.SetModifier(""ghost"");          // 幽灵样式

// 设置选择器尺寸
select.WithSize(""sm"");          // 小尺寸
select.WithSize(""md"");          // 中等尺寸
select.WithSize(""lg"");          // 大尺寸

// 设置选择器状态
select.SetDisabled(true);   // 禁用状态
select.SetModifier(""error"");      // 错误状态

// 设置和获取选中值
select.SetValue(""选项2"");
string selectedValue = select.Value;

// 多选选择器
var multiSelect = DaisySelect.Create(""多选"", options);
multiSelect.AllowMultiple = true;
multiSelect.SelectedValues = new List<string> { ""选项A"", ""选项C"" };
List<string> selectedValues = multiSelect.SelectedValues;

// 事件处理
select.OnValueChanged(value => Debug.Log($""选择了: {value}""));
multiSelect.OnValueChanged(value =>
    Debug.Log($""选择了: {value}""));

// 动态添加选项
select.AddOption(""新选项"");
select.RemoveOption(""选项1"");
select.ClearOptions();

// 添加到容器
container.Add(select);";
        }

        public override string GetUsageInstructions()
        {
            return @"选择器组件使用说明：

1. 基本用法：
   - 使用 DaisySelect.Create() 创建单选选择器
   - 使用 DaisySelect.CreateMultiple() 创建多选选择器

2. 选项管理：
   - 支持字符串数组或List<string>初始化
   - AddOption(string)：动态添加选项
   - RemoveOption(string)：移除选项
   - ClearOptions()：清空所有选项

3. 样式选项：
   - SetBordered()：边框样式
   - SetGhost()：幽灵样式（透明背景）

4. 尺寸选项：
   - SetSmall()：小尺寸选择器
   - SetMedium()：中等尺寸选择器（默认）
   - SetLarge()：大尺寸选择器

5. 状态控制：
   - SetDisabled(bool)：设置禁用状态
   - SetError(bool)：设置错误状态

6. 值操作：
   - SetSelectedValue(string)：设置选中值（单选）
   - GetSelectedValue()：获取选中值（单选）
   - SetSelectedValues(string[])：设置选中值（多选）
   - GetSelectedValues()：获取选中值（多选）

7. 事件处理：
   - OnSelectionChanged：单选值改变事件
   - OnMultiSelectionChanged：多选值改变事件

8. 最佳实践：
   - 提供清晰的选项标签
   - 合理设置默认选中值
   - 为必选字段提供验证提示
   - 考虑选项数量对性能的影响";
        }

        public override string[] GetSupportedVariants()
        {
            return new string[]
            {
                "single",
                "multiple",
                "bordered",
                "ghost",
                "primary",
                "secondary",
                "success",
                "warning",
                "error"
            };
        }

        public override VisualElement CreateVariantPreview(string variant)
        {
            var container = CreatePreviewContainer("flex", "flex-col", "gap-2");

            switch (variant.ToLower())
            {
                case "single":
                    var singleSelect = DaisySelect.Create("单选选择器", "选项1", "选项2", "选项3");
                    container.Add(singleSelect);
                    break;
                case "multiple":
                    var multiSelect = DaisySelect.Create("多选选择器", new List<string> { "选项A", "选项B", "选项C" });
                    multiSelect.AllowMultiple = true;
                    container.Add(multiSelect);
                    break;
                case "bordered":
                    var borderedSelect = DaisySelect.Create("边框选择器", "选项1", "选项2", "选项3");
                    borderedSelect.SetModifier("bordered");
                    container.Add(borderedSelect);
                    break;
                case "ghost":
                    var ghostSelect = DaisySelect.Create("幽灵选择器", "选项1", "选项2", "选项3");
                    ghostSelect.SetModifier("ghost");
                    container.Add(ghostSelect);
                    break;
                case "primary":
                    var primarySelect = DaisySelect.Create("主要选择器", "选项1", "选项2", "选项3");
                    primarySelect.SetModifier("primary");
                    container.Add(primarySelect);
                    break;
                case "secondary":
                    var secondarySelect = DaisySelect.Create("次要选择器", "选项1", "选项2", "选项3");
                    secondarySelect.SetModifier("secondary");
                    container.Add(secondarySelect);
                    break;
                case "success":
                    var successSelect = DaisySelect.Create("成功选择器", "选项1", "选项2", "选项3");
                    successSelect.SetModifier("success");
                    container.Add(successSelect);
                    break;
                case "warning":
                    var warningSelect = DaisySelect.Create("警告选择器", "选项1", "选项2", "选项3");
                    warningSelect.SetModifier("warning");
                    container.Add(warningSelect);
                    break;
                case "error":
                    var errorSelect = DaisySelect.Create("错误选择器", "选项1", "选项2", "选项3");
                    errorSelect.SetModifier("error");
                    container.Add(errorSelect);
                    break;
                default:
                    return null;
            }

            return container;
        }

        public override string GetVariantCodeExample(string variant)
        {
            return variant.ToLower() switch
            {
                "single" => @"var select = DaisySelect.Create(""单选"", ""选项1"", ""选项2"", ""选项3"");",
                "multiple" => @"var select = DaisySelect.Create(""多选"", new[] { ""选项A"", ""选项B"", ""选项C"" });
select.AllowMultiple = true;",
                "bordered" => @"var select = DaisySelect.Create(""边框"", ""选项1"", ""选项2"", ""选项3"").SetModifier(""bordered"");",
                "ghost" => @"var select = DaisySelect.Create(""幽灵"", ""选项1"", ""选项2"", ""选项3"").SetModifier(""ghost"");",
                "primary" => @"var select = DaisySelect.Create(""主要"", ""选项1"", ""选项2"", ""选项3"").SetModifier(""primary"");",
                "secondary" => @"var select = DaisySelect.Create(""次要"", ""选项1"", ""选项2"", ""选项3"").SetModifier(""secondary"");",
                "success" => @"var select = DaisySelect.Create(""成功"", ""选项1"", ""选项2"", ""选项3"").SetModifier(""success"");",
                "warning" => @"var select = DaisySelect.Create(""警告"", ""选项1"", ""选项2"", ""选项3"").SetModifier(""warning"");",
                "error" => @"var select = DaisySelect.Create(""错误"", ""选项1"", ""选项2"", ""选项3"").SetModifier(""error"");",
                _ => null
            };
        }
    }
}
