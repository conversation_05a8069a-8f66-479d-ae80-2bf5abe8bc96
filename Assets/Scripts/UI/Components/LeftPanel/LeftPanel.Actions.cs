using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Data;
using BlastingDesign.Utils;
using BlastingDesign.UI.Core;
using System.Linq;


namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// LeftPanel - 操作处理部分
    /// </summary>
    public partial class LeftPanel
    {
        #region Element Actions

        /// <summary>
        /// 切换元素可见性
        /// </summary>
        private void ToggleElementVisibility(BlastingElement element)
        {
            element.isVisible = !element.isVisible;

            // 触发可见性变化事件
            UIEventSystem.TriggerMenuItemClicked($"toggle-visibility-{element.id}");
            UIEventSystem.TriggerStatusMessage($"{element.name} {(element.isVisible ? "已显示" : "已隐藏")}");

            // 更新树显示
            UpdateTreeData();

            Logging.LogInfo("LeftPanel", $"切换 {element.name} 可见性: {element.isVisible}");
        }

        /// <summary>
        /// 编辑元素
        /// </summary>
        private void EditElement(BlastingElement element)
        {
            UIEventSystem.TriggerMenuItemClicked($"edit-{element.type.ToString().ToLower()}");
            UIEventSystem.TriggerStatusMessage($"编辑 {element.name}");

            Logging.LogInfo("LeftPanel", $"编辑元素: {element.name} ({element.type})");
        }

        /// <summary>
        /// 重命名元素
        /// </summary>
        private void RenameElement(BlastingElement element)
        {
            UIEventSystem.TriggerMenuItemClicked($"rename-{element.id}");
            UIEventSystem.TriggerStatusMessage($"重命名 {element.name}");

            Logging.LogInfo("LeftPanel", $"重命名元素: {element.name}");
        }

        /// <summary>
        /// 删除元素
        /// </summary>
        private void DeleteElement(BlastingElement element)
        {
            if (element.isLocked)
            {
                UIEventSystem.TriggerStatusMessage($"{element.name} 已锁定，无法删除");
                return;
            }

            UIEventSystem.TriggerMenuItemClicked($"delete-{element.id}");
            UIEventSystem.TriggerStatusMessage($"删除 {element.name}");

            Logging.LogInfo("LeftPanel", $"删除元素: {element.name} ({element.type})");
        }

        /// <summary>
        /// 显示元素属性
        /// </summary>
        private void ShowElementProperties(BlastingElement element)
        {
            UIEventSystem.TriggerMenuItemClicked($"properties-{element.id}");
            UIEventSystem.TriggerStatusMessage($"查看 {element.name} 属性");

            Logging.LogInfo("LeftPanel", $"显示元素属性: {element.name}");
        }

        /// <summary>
        /// 计算元素（爆区特有）
        /// </summary>
        private void CalculateElement(BlastingElement element)
        {
            if (element is BlastingAreaData area)
            {
                UIEventSystem.TriggerMenuItemClicked($"calculate-blasting-area-{area.id}");
                UIEventSystem.TriggerStatusMessage($"计算爆区 {area.name}");

                Logging.LogInfo("LeftPanel", $"计算爆区: {area.name}");
            }
        }

        /// <summary>
        /// 导出元素（爆区特有）
        /// </summary>
        private void ExportElement(BlastingElement element)
        {
            if (element is BlastingAreaData area)
            {
                UIEventSystem.TriggerMenuItemClicked($"export-blasting-area-{area.id}");
                UIEventSystem.TriggerStatusMessage($"导出爆区 {area.name}");

                Logging.LogInfo("LeftPanel", $"导出爆区: {area.name}");
            }
        }

        /// <summary>
        /// 处理自定义操作
        /// </summary>
        private void HandleCustomAction(BlastingElement element, string actionId)
        {
            switch (actionId)
            {
                case "design":
                    HandleDesignAction(element);
                    break;
                case "charge":
                    HandleChargeAction(element);
                    break;
                case "timing":
                    HandleTimingAction(element);
                    break;
                case "sequence":
                    HandleSequenceAction(element);
                    break;
                case "simulate":
                    HandleSimulateAction(element);
                    break;
                case "add":
                    HandleAddAction(element);
                    break;
                default:
                    UIEventSystem.TriggerMenuItemClicked($"{actionId}-{element.id}");
                    break;
            }
        }

        /// <summary>
        /// 处理设计操作
        /// </summary>
        private void HandleDesignAction(BlastingElement element)
        {
            if (element is DrillHoleData hole)
            {
                UIEventSystem.TriggerMenuItemClicked($"design-drill-hole-{hole.id}");
                UIEventSystem.TriggerStatusMessage($"设计钻孔 {hole.name}");

                Logging.LogInfo("LeftPanel", $"设计钻孔: {hole.name}");
            }
        }

        /// <summary>
        /// 处理装药操作
        /// </summary>
        private void HandleChargeAction(BlastingElement element)
        {
            if (element is DrillHoleData hole)
            {
                UIEventSystem.TriggerMenuItemClicked($"charge-drill-hole-{hole.id}");
                UIEventSystem.TriggerStatusMessage($"设计 {hole.name} 装药");

                Logging.LogInfo("LeftPanel", $"设计钻孔装药: {hole.name}");
            }
        }

        /// <summary>
        /// 处理时序操作
        /// </summary>
        private void HandleTimingAction(BlastingElement element)
        {
            switch (element)
            {
                case DrillHoleData hole:
                    UIEventSystem.TriggerMenuItemClicked($"timing-drill-hole-{hole.id}");
                    UIEventSystem.TriggerStatusMessage($"设置 {hole.name} 起爆时序");
                    break;
                case BlastBlockData block:
                    UIEventSystem.TriggerMenuItemClicked($"timing-blast-block-{block.id}");
                    UIEventSystem.TriggerStatusMessage($"设置 {block.name} 起爆时序");
                    break;
            }

            Logging.LogInfo("LeftPanel", $"设置起爆时序: {element.name}");
        }

        /// <summary>
        /// 处理起爆顺序操作
        /// </summary>
        private void HandleSequenceAction(BlastingElement element)
        {
            if (element is BlastBlockData block)
            {
                UIEventSystem.TriggerMenuItemClicked($"sequence-blast-block-{block.id}");
                UIEventSystem.TriggerStatusMessage($"设置 {block.name} 起爆顺序");

                Logging.LogInfo("LeftPanel", $"设置起爆顺序: {block.name}");
            }
        }

        /// <summary>
        /// 处理模拟操作
        /// </summary>
        private void HandleSimulateAction(BlastingElement element)
        {
            if (element is BlastBlockData block)
            {
                UIEventSystem.TriggerMenuItemClicked($"simulate-blast-block-{block.id}");
                UIEventSystem.TriggerStatusMessage($"模拟 {block.name} 爆破效果");

                Logging.LogInfo("LeftPanel", $"模拟爆破: {block.name}");
            }
        }

        /// <summary>
        /// 处理添加操作
        /// </summary>
        private void HandleAddAction(BlastingElement element)
        {
            var actionType = element.type switch
            {
                BlastingElementType.BlastingArea => "add-element-to-area",
                BlastingElementType.Project => "add-blasting-area",
                _ => "add-element"
            };

            UIEventSystem.TriggerMenuItemClicked($"{actionType}-{element.id}");
            UIEventSystem.TriggerStatusMessage($"向 {element.name} 添加元素");

            Logging.LogInfo("LeftPanel", $"添加元素到: {element.name}");
        }

        #endregion

        #region Interface Implementation

        // ICollapsible 接口实现
        public void SetCollapsed(bool collapsed)
        {
            isCollapsed = collapsed;

            if (collapsed)
            {
                AddToClassList("collapsed");
                style.width = 0;
            }
            else
            {
                RemoveFromClassList("collapsed");
                style.width = currentWidth;
            }

            UIEventSystem.TriggerPanelCollapsed(elementName, collapsed);
            Logging.LogInfo("LeftPanel", $"面板折叠状态: {collapsed}");
        }

        public void ToggleCollapsed()
        {
            SetCollapsed(!isCollapsed);
        }

        // IResizable 接口实现
        public void SetSize(float size)
        {
            currentWidth = Mathf.Clamp(size, minWidth, maxWidth);
            if (!isCollapsed)
            {
                style.width = currentWidth;
            }

            UIEventSystem.TriggerPanelResized(elementName, currentWidth);
        }

        public void ResetSize()
        {
            SetSize(defaultWidth);
        }

        #endregion

        #region Display Updates

        /// <summary>
        /// 更新显示
        /// </summary>
        private void UpdateDisplay()
        {
            UpdateStatusDisplay();
            ShowEmptyStateIfNeeded();
        }

        /// <summary>
        /// 更新状态显示
        /// </summary>
        private void UpdateStatusDisplay()
        {
            int totalItems = CountTotalElements();
            // 注意：DaisyTree的选择API可能不同，这里先使用0作为占位符
            int selectedItems = 0; // TODO: 获取DaisyTree的选中项数量

            if (itemCountLabel != null)
            {
                itemCountLabel.text = $"{totalItems} 项";
            }

            if (selectionCountLabel != null)
            {
                selectionCountLabel.text = selectedItems > 0 ? $"已选择 {selectedItems}" : "未选择";
            }
        }

        /// <summary>
        /// 计算总元素数量
        /// </summary>
        private int CountTotalElements()
        {
            if (currentProject == null) return 0;

            int count = 1; // 项目本身

            foreach (var area in currentProject.blastingAreas)
            {
                count++; // 爆区
                count += area.measurePoints.Count;
                count += area.boundaryLines.Count;
                count += area.drillHoles.Count;
                count += area.blastBlocks.Count;
            }

            return count;
        }

        /// <summary>
        /// 根据需要显示空状态
        /// </summary>
        private void ShowEmptyStateIfNeeded()
        {
            bool isEmpty = currentProject == null || !currentProject.blastingAreas.Any();

            if (emptyState != null)
            {
                emptyState.style.display = isEmpty ? DisplayStyle.Flex : DisplayStyle.None;
            }

            // 控制树组件的显示
            if (blastingTree != null)
            {
                blastingTree.style.display = isEmpty ? DisplayStyle.None : DisplayStyle.Flex;
            }
            else if (fallbackTreeView != null)
            {
                fallbackTreeView.style.display = isEmpty ? DisplayStyle.None : DisplayStyle.Flex;
            }
        }

        #endregion
    }
}