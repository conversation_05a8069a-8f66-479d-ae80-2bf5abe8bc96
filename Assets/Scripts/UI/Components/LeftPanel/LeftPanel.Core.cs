using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Core;
using BlastingDesign.Data;
using System.Collections.Generic;
using BlastingDesign.Utils;
using BlastingDesign.UI.DaisyUI.Components.Navigation.Tree;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 左侧面板组件 - 爆破设计项目层次结构
    /// 使用DaisyTree组件展示爆破设计的核心数据元素
    /// </summary>
    [UxmlElement]
    public partial class LeftPanel : UIElementBase, ICollapsible, IResizable
    {
        #region Constants and Configuration

        protected override string TemplatePath => "UI/LeftPanel";

        [Header("Panel Settings")]
        private readonly float defaultWidth = 300f;
        private readonly float minWidth = 200f;
        private readonly float maxWidth = 500f;

        #endregion

        #region Private Fields

        // 状态
        private bool isCollapsed = false;
        private bool isDragging = false;
        private float currentWidth;
        
        // Resize状态跟踪
        private Vector2 resizeStartPosition;
        private float resizeStartWidth;

        // UI元素引用
        private DaisyTree blastingTree;
        private TreeView fallbackTreeView; // 备用TreeView
        private Button collapseButton;
        private Button refreshButton;
        private Button settingsButton;
        private Label itemCountLabel;
        private Label selectionCountLabel;
        private VisualElement emptyState;
        private VisualElement resizeHandle;
        private VisualElement treeContainer;

        // 数据
        private ProjectData currentProject;
        private List<DaisyTreeData> treeDataCache;

        #endregion

        #region Properties

        /// <summary>
        /// 当前项目数据
        /// </summary>
        public ProjectData CurrentProject
        {
            get => currentProject;
            set => SetProject(value);
        }

        // ICollapsible 接口实现
        public bool IsCollapsed => isCollapsed;

        // IResizable 接口实现
        public float Size => currentWidth;
        public float MinSize => minWidth;
        public float MaxSize => maxWidth;

        #endregion

        #region Constructor

        public LeftPanel()
        {
            elementName = "LeftPanel";
            currentWidth = defaultWidth;
            treeDataCache = new List<DaisyTreeData>();
        }

        #endregion

        #region Initialization

        protected override void CacheUIElements()
        {
            // 缓存其他UI元素
            collapseButton = SafeQuery<Button>("collapse-button");
            refreshButton = SafeQuery<Button>("refresh-button");
            settingsButton = SafeQuery<Button>("settings-button");
            itemCountLabel = SafeQuery<Label>("item-count");
            selectionCountLabel = SafeQuery<Label>("selection-count");
            emptyState = SafeQuery<VisualElement>("empty-state");
            resizeHandle = SafeQuery<VisualElement>("resize-handle");

            // 缓存树容器
            treeContainer = SafeQuery<VisualElement>("blasting-tree-container");

            // 通过代码创建树组件
            CreateTreeComponent();
        }

        /// <summary>
        /// 通过代码创建树组件（优先使用DaisyTree，失败时回退到TreeView）
        /// </summary>
        private void CreateTreeComponent()
        {
            if (treeContainer == null)
            {
                Logging.LogError("LeftPanel", "找不到blasting-tree-container容器");
                return;
            }

            // 首先尝试创建DaisyTree
            if (TryCreateDaisyTree())
            {
                Logging.LogInfo("LeftPanel", "使用DaisyTree组件");
                return;
            }

            // 如果DaisyTree失败，回退到TreeView
            CreateFallbackTreeView();
            Logging.LogInfo("LeftPanel", "回退使用Unity TreeView组件");
        }

        /// <summary>
        /// 尝试创建DaisyTree组件
        /// </summary>
        private bool TryCreateDaisyTree()
        {
            try
            {
                // 创建DaisyTree实例
                blastingTree = new DaisyTree();
                blastingTree.name = "blasting-tree";
                blastingTree.AddToClassList("blasting-design-tree");
                blastingTree.AddToClassList("dark");
                blastingTree.AddToClassList("grow");

                // 添加到容器
                treeContainer.Add(blastingTree);

                Logging.LogInfo("LeftPanel", "DaisyTree组件创建成功");
                return true;
            }
            catch (System.Exception ex)
            {
                Logging.LogWarning("LeftPanel", $"创建DaisyTree组件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建备用TreeView组件
        /// </summary>
        private void CreateFallbackTreeView()
        {
            fallbackTreeView = new TreeView();
            fallbackTreeView.name = "fallback-tree";
            fallbackTreeView.AddToClassList("fallback-tree-view");
            fallbackTreeView.AddToClassList("blasting-design-tree");

            // 添加到容器
            treeContainer.Add(fallbackTreeView);

            Logging.LogInfo("LeftPanel", "备用TreeView组件创建成功");
        }

        protected override void InitializeData()
        {
            SetupInitialState();
            InitializeTree();
            LoadSampleProject();

            Logging.LogInfo("LeftPanel", "爆破设计左侧面板初始化完成");
        }

        private void SetupInitialState()
        {
            // 设置面板宽度
            style.width = currentWidth;

            // 设置折叠状态
            if (isCollapsed)
            {
                SetCollapsed(true);
            }

            // 隐藏空状态
            if (emptyState != null)
            {
                emptyState.style.display = DisplayStyle.None;
            }
        }

        private void InitializeTree()
        {
            if (blastingTree == null)
            {
                Logging.LogWarning("LeftPanel", "DaisyTree组件未创建，跳过初始化");
                return;
            }

            try
            {
                // 配置DaisyTree
                blastingTree
                    .SetMultiSelect(true)
                    .SetAllowSearch(true)
                    .SetSearchPlaceholder("搜索爆破元素...")
                    .SetShowIcons(true)
                    .SetShowActions(true);

                Logging.LogInfo("LeftPanel", "DaisyTree配置完成");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("LeftPanel", $"DaisyTree配置失败: {ex.Message}");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 设置项目数据
        /// </summary>
        public void SetProject(ProjectData project)
        {
            currentProject = project;
            UpdateTreeData();
            UpdateDisplay();
        }

        /// <summary>
        /// 刷新数据
        /// </summary>
        public void RefreshData()
        {
            if (currentProject != null)
            {
                UpdateTreeData();
                UpdateDisplay();
            }
            else
            {
                LoadSampleProject();
            }
        }

        #endregion

        #region Cleanup

        public override void Cleanup()
        {
            // 强制停止resize操作
            if (isDragging)
            {
                isDragging = false;
                RemoveFromClassList("resizing");
                // 确保清理安全的全局事件监听
                UnregisterSafeGlobalResizeEvents();
            }
            
            base.Cleanup();

            treeDataCache?.Clear();
            currentProject = null;

            Logging.LogInfo("LeftPanel", "左侧面板清理完成");
        }

        #endregion
    }
}