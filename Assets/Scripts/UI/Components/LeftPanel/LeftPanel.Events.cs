using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Utils;
using BlastingDesign.Data;
using BlastingDesign.Events;
using BlastingDesign.Events.Core;
using System.Collections.Generic;
using System.Linq;
using BlastingDesign.UI.DaisyUI.Components.Navigation.Tree;
using BlastingDesign.UI.Core;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// LeftPanel - 事件处理部分
    /// </summary>
    public partial class LeftPanel
    {
        #region Event Setup

        protected override void SetupEventListeners()
        {
            // 设置按钮事件
            SetupButtonEvents();

            // 设置树组件事件
            SetupTreeEvents();

            // 设置拖拽调整大小事件
            SetupResizeEvents();

            Logging.LogInfo("LeftPanel", "事件监听器已设置");
        }

        protected override void RemoveEventListeners()
        {
            // 移除按钮事件
            RemoveButtonEvents();

            // 移除树组件事件  
            RemoveTreeEvents();

            // 移除拖拽事件
            RemoveResizeEvents();

            Logging.LogInfo("LeftPanel", "事件监听器已移除");
        }

        private void SetupButtonEvents()
        {
            if (collapseButton != null)
            {
                collapseButton.clicked += ToggleCollapsed;
            }

            if (refreshButton != null)
            {
                refreshButton.clicked += OnRefreshClicked;
            }

            if (settingsButton != null)
            {
                settingsButton.clicked += OnSettingsClicked;
            }
        }

        private void RemoveButtonEvents()
        {
            if (collapseButton != null)
            {
                collapseButton.clicked -= ToggleCollapsed;
            }

            if (refreshButton != null)
            {
                refreshButton.clicked -= OnRefreshClicked;
            }

            if (settingsButton != null)
            {
                settingsButton.clicked -= OnSettingsClicked;
            }
        }

        private void SetupTreeEvents()
        {
            if (blastingTree != null)
            {
                // 设置DaisyTree事件
                SetupDaisyTreeEvents();
            }
            else if (fallbackTreeView != null)
            {
                // 设置备用TreeView事件
                SetupFallbackTreeEvents();
            }
        }

        private void SetupDaisyTreeEvents()
        {
            try
            {
                blastingTree
                    .OnItemClick(OnTreeItemClicked)
                    .OnItemSelect(OnTreeItemSelected)
                    .OnAction(OnTreeItemAction);

                // 注意：OnMultiSelect可能需要不同的设置方式
                // 暂时注释掉，等确认DaisyTree的多选API
                // .OnMultiSelect(OnTreeMultiSelect);

                Logging.LogInfo("LeftPanel", "DaisyTree事件设置完成");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("LeftPanel", $"设置DaisyTree事件失败: {ex.Message}");
            }
        }

        private void SetupFallbackTreeEvents()
        {
            try
            {
                fallbackTreeView.selectionChanged += OnFallbackTreeSelectionChanged;
                fallbackTreeView.itemsChosen += OnFallbackTreeItemsChosen;

                Logging.LogInfo("LeftPanel", "备用TreeView事件设置完成");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("LeftPanel", $"设置备用TreeView事件失败: {ex.Message}");
            }
        }

        private void RemoveTreeEvents()
        {
            if (blastingTree != null)
            {
                // DaisyTree的事件会在组件销毁时自动清理
            }

            if (fallbackTreeView != null)
            {
                try
                {
                    fallbackTreeView.selectionChanged -= OnFallbackTreeSelectionChanged;
                    fallbackTreeView.itemsChosen -= OnFallbackTreeItemsChosen;

                    Logging.LogInfo("LeftPanel", "备用TreeView事件清理完成");
                }
                catch (System.Exception ex)
                {
                    Logging.LogError("LeftPanel", $"清理备用TreeView事件失败: {ex.Message}");
                }
            }
        }

        private void SetupResizeEvents()
        {
            if (resizeHandle != null)
            {
                resizeHandle.RegisterCallback<MouseDownEvent>(OnResizeStart);
            }
        }

        private void RemoveResizeEvents()
        {
            if (resizeHandle != null)
            {
                resizeHandle.UnregisterCallback<MouseDownEvent>(OnResizeStart);
            }

            // 确保清理安全的全局事件监听
            UnregisterSafeGlobalResizeEvents();
        }

        #endregion

        #region Button Event Handlers

        private void OnRefreshClicked()
        {
            RefreshData();
            UIEventSystem.TriggerStatusMessage("项目数据已刷新");
            Logging.LogInfo("LeftPanel", "刷新项目数据");
        }

        private void OnSettingsClicked()
        {
            UIEventSystem.TriggerMenuItemClicked("panel-settings");
            Logging.LogInfo("LeftPanel", "打开面板设置");
        }

        #endregion

        #region Tree Event Handlers

        private void OnTreeItemClicked(DaisyTreeData data)
        {
            try
            {
                var element = GetBlastingElementFromTreeData(data);
                if (element != null)
                {
                    // 触发对象选择事件
                    var hierarchyItem = ConvertToHierarchyItem(element);
                    UIEventSystem.TriggerObjectSelected(hierarchyItem);

                    Logging.LogInfo("LeftPanel", $"点击爆破元素: {element.name} ({element.type})");
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("LeftPanel", $"处理树项点击事件时出错: {ex.Message}");
            }
        }

        private void OnTreeItemSelected(DaisyTreeData data)
        {
            try
            {
                var element = GetBlastingElementFromTreeData(data);
                if (element != null)
                {
                    var hierarchyItem = ConvertToHierarchyItem(element);

                    // 使用新事件系统（优先）
                    if (BlastingDesign.Events.EventSystemManager.Instance != null && BlastingDesign.Events.EventSystemManager.Instance.EventBus != null)
                    {
                        BlastingDesign.Events.EventSystemManager.Instance.EventBus.Publish(new ObjectSelectedEvent(hierarchyItem));
                    }
                    else
                    {
                        // 回退到旧事件系统
                        UIEventSystem.TriggerObjectSelected(hierarchyItem);
                    }

                    UpdateStatusDisplay();
                    Logging.LogInfo("LeftPanel", $"选择爆破元素: {element.name}");
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("LeftPanel", $"处理树项选择事件时出错: {ex.Message}");
            }
        }

        private void OnTreeItemAction(DaisyTreeData data, string actionId)
        {
            try
            {
                var element = GetBlastingElementFromTreeData(data);
                if (element == null) return;

                switch (actionId)
                {
                    case "visibility":
                        ToggleElementVisibility(element);
                        break;
                    case "edit":
                        EditElement(element);
                        break;
                    case "rename":
                        RenameElement(element);
                        break;
                    case "delete":
                        DeleteElement(element);
                        break;
                    case "properties":
                        ShowElementProperties(element);
                        break;
                    case "calculate":
                        CalculateElement(element);
                        break;
                    case "export":
                        ExportElement(element);
                        break;
                    default:
                        HandleCustomAction(element, actionId);
                        break;
                }

                Logging.LogInfo("LeftPanel", $"执行操作 {actionId} 于元素 {element.name}");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("LeftPanel", $"处理树项操作事件时出错: {ex.Message}");
            }
        }

        #endregion

        #region Fallback TreeView Event Handlers

        private void OnFallbackTreeSelectionChanged(IEnumerable<object> selectedItems)
        {
            try
            {
                // TreeView的selectionChanged事件返回的是TreeViewItemData<T>的id，不是数据本身
                // 我们需要通过id获取实际的数据
                var selectedIds = selectedItems?.Cast<int>().ToArray() ?? new int[0];
                var items = new List<HierarchyItemData>();

                foreach (var id in selectedIds)
                {
                    var itemData = fallbackTreeView.GetItemDataForId<HierarchyItemData>(id);
                    if (itemData != null)
                    {
                        items.Add(itemData);
                    }
                }

                if (items.Count == 1)
                {
                    UIEventSystem.TriggerObjectSelected(items[0]);
                }
                else if (items.Count > 1)
                {
                    if (UIEventSystem.Instance != null)
                    {
                        UIEventSystem.Instance.Selection.OnMultipleObjectsSelected?.Invoke(items.ToArray());
                    }
                }
                else
                {
                    if (UIEventSystem.Instance != null)
                    {
                        UIEventSystem.Instance.Selection.OnSelectionCleared?.Invoke();
                    }
                }

                UpdateStatusDisplay();
                Logging.LogInfo("LeftPanel", $"TreeView选择变化: {items.Count} 项");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("LeftPanel", $"处理TreeView选择事件时出错: {ex.Message}");
            }
        }

        private void OnFallbackTreeItemsChosen(IEnumerable<object> chosenItems)
        {
            try
            {
                // TreeView的itemsChosen事件返回的是id，不是数据本身
                var chosenIds = chosenItems?.Cast<int>().ToArray() ?? new int[0];

                foreach (var id in chosenIds)
                {
                    var itemData = fallbackTreeView.GetItemDataForId<HierarchyItemData>(id);
                    if (itemData != null)
                    {
                        Logging.LogInfo("LeftPanel", $"双击TreeView项目: {itemData.name}");
                        UIEventSystem.TriggerMenuItemClicked("focus-selected");
                    }
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("LeftPanel", $"处理TreeView双击事件时出错: {ex.Message}");
            }
        }

        #endregion

        #region Resize Event Handlers

        private void OnResizeStart(MouseDownEvent evt)
        {
            if (isCollapsed) return;

            isDragging = true;
            resizeStartPosition = evt.mousePosition;
            resizeStartWidth = currentWidth;

            // 添加调整尺寸样式
            AddToClassList("resizing");

            // 使用安全的全局事件监听，确保跨面板连续性
            RegisterSafeGlobalResizeEvents();

            // 不使用CaptureMouse，避免阻塞其他UI
            // resizeHandle?.CaptureMouse();

            evt.StopPropagation();
        }

        private void OnSafeGlobalResizeMove(MouseMoveEvent evt)
        {
            if (!isDragging) return;

            var deltaPosition = evt.mousePosition - resizeStartPosition;
            var newWidth = resizeStartWidth + deltaPosition.x;
            SetSize(newWidth);

            // 只有在resize激活时才阻止事件传播，且只在合理范围内
            if (isDragging && IsResizeReasonable(evt))
            {
                evt.StopPropagation();
            }
        }

        private void OnSafeGlobalResizeEnd(MouseUpEvent evt)
        {
            if (!isDragging) return;

            EndResize();

            // 只有在resize激活时才阻止事件传播
            if (isDragging)
            {
                evt.StopPropagation();
            }
        }

        private void EndResize()
        {
            if (!isDragging) return;

            isDragging = false;

            // 移除调整尺寸样式
            RemoveFromClassList("resizing");

            // 移除安全的全局事件监听
            UnregisterSafeGlobalResizeEvents();
        }

        #endregion

        #region Safe Global Resize Events

        private void RegisterSafeGlobalResizeEvents()
        {
            var rootContainer = GetRootContainer();
            if (rootContainer != null)
            {
                // 使用全局事件监听确保跨面板连续性
                rootContainer.RegisterCallback<MouseMoveEvent>(OnSafeGlobalResizeMove);
                rootContainer.RegisterCallback<MouseUpEvent>(OnSafeGlobalResizeEnd);
            }
        }

        private void UnregisterSafeGlobalResizeEvents()
        {
            var rootContainer = GetRootContainer();
            if (rootContainer != null)
            {
                rootContainer.UnregisterCallback<MouseMoveEvent>(OnSafeGlobalResizeMove);
                rootContainer.UnregisterCallback<MouseUpEvent>(OnSafeGlobalResizeEnd);
            }
        }

        private VisualElement GetRootContainer()
        {
            VisualElement current = this;
            while (current.parent != null)
            {
                current = current.parent;
            }
            return current;
        }

        /// <summary>
        /// 检查resize操作是否合理，避免过度干扰其他UI
        /// </summary>
        private bool IsResizeReasonable(MouseMoveEvent evt)
        {
            // 检查鼠标位置是否在合理的resize范围内
            var mousePosition = evt.mousePosition;
            var panelRect = this.worldBound;

            // 允许在面板右侧一定范围内进行resize
            var resizeZone = new Rect(
                panelRect.x - 50,  // 左侧50px
                panelRect.y,
                panelRect.width + 200,  // 右侧150px
                panelRect.height
            );

            return resizeZone.Contains(mousePosition);
        }

        #endregion
    }
}