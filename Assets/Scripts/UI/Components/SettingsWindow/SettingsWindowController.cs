using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 设置窗口控制器 - 管理设置窗口的显示和交互（支持模块化架构）
    /// </summary>
    public class SettingsWindowController : MonoBehaviour
    {
        #region 私有字段

        private VisualElement rootElement;
        private VisualTreeAsset settingsTemplate;
        private StyleSheet settingsStyleSheet;

        // 配置数据
        private SettingsData settingsData;
        private SettingsData originalSettingsData; // 用于取消操作

        // 模块管理器
        private SettingsModuleManager moduleManager;

        // 控件引用
        private TextField searchField;
        private VisualElement categoryListContainer;
        private VisualElement contentContainer;
        
        // 内容头部引用
        private Label currentCategoryTitle;
        private Label currentCategoryDescription;
        
        // 状态引用
        private Label statusIndicator;
        private Label statusMessage;

        // 按钮引用
        private Button resetButton;
        private Button cancelButton;
        private Button applyButton;

        #endregion

        #region 公共事件

        public event Action<SettingsData> OnSettingsApplied;
        public event Action OnSettingsReset;
        public event Action OnSettingsCancelled;

        #endregion

        #region 生命周期

        private void Awake()
        {
            Initialize();
        }

        private void OnEnable()
        {
            LoadSettings();
        }

        #endregion

        #region 初始化

        private void Initialize()
        {
            try
            {
                // 加载资源
                settingsTemplate = Resources.Load<VisualTreeAsset>("UI/SettingsWindow");
                settingsStyleSheet = Resources.Load<StyleSheet>("UI/SettingsWindowStyles");

                if (settingsTemplate == null)
                {
                    Logging.LogError("SettingsWindow", "无法加载设置窗口模板");
                    return;
                }

                // 创建设置数据
                settingsData = new SettingsData();

                Logging.LogInfo("SettingsWindow", "设置窗口控制器初始化完成");
            }
            catch (Exception ex)
            {
                Logging.LogError("SettingsWindow", $"初始化设置窗口时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 创建设置窗口
        /// </summary>
        /// <param name="parent">父容器</param>
        /// <returns>设置窗口根元素</returns>
        public VisualElement CreateSettingsWindow(VisualElement parent = null)
        {
            try
            {
                if (settingsTemplate == null)
                {
                    Logging.LogError("SettingsWindow", "设置窗口模板未加载");
                    return null;
                }

                // 创建窗口实例
                rootElement = settingsTemplate.Instantiate();

                if (settingsStyleSheet != null)
                {
                    rootElement.styleSheets.Add(settingsStyleSheet);
                }

                // 初始化UI元素
                InitializeUIElements();

                // 绑定事件
                BindEvents();

                // 添加到父容器
                parent?.Add(rootElement);

                Logging.LogInfo("SettingsWindow", "设置窗口创建成功");
                return rootElement;
            }
            catch (Exception ex)
            {
                Logging.LogError("SettingsWindow", $"创建设置窗口时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 销毁设置窗口
        /// </summary>
        public void DestroySettingsWindow()
        {
            try
            {
                if (rootElement?.parent != null)
                {
                    rootElement.parent.Remove(rootElement);
                }

                UnbindEvents();
                rootElement = null;

                Logging.LogInfo("SettingsWindow", "设置窗口已销毁");
            }
            catch (Exception ex)
            {
                Logging.LogError("SettingsWindow", $"销毁设置窗口时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Cleanup()
        {
            try
            {
                UnbindEvents();

                // 清理模块管理器
                if (moduleManager != null)
                {
                    moduleManager.OnModuleSelected -= OnModuleSelected;
                    moduleManager.OnModuleDeselected -= OnModuleDeselected;
                    moduleManager.OnModulesLoaded -= OnModulesLoaded;
                    moduleManager.Cleanup();
                    moduleManager = null;
                }

                rootElement = null;
                settingsData = null;
                originalSettingsData = null;

                Logging.LogInfo("SettingsWindow", "设置窗口控制器已清理");
            }
            catch (Exception ex)
            {
                Logging.LogError("SettingsWindow", $"清理设置窗口控制器时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        private void InitializeUIElements()
        {
            if (rootElement == null)
            {
                Logging.LogError("SettingsWindow", "根元素为空，无法初始化UI元素");
                return;
            }

            // 获取搜索字段
            searchField = rootElement.Q<TextField>("search-field");

            // 获取分类列表容器
            categoryListContainer = rootElement.Q<ScrollView>("category-list");

            // 获取内容容器
            contentContainer = rootElement.Q<VisualElement>("settings-content");
            
            // 获取内容头部引用
            currentCategoryTitle = rootElement.Q<Label>("current-category-title");
            currentCategoryDescription = rootElement.Q<Label>("current-category-description");
            
            // 获取状态引用
            statusIndicator = rootElement.Q<Label>("status-indicator");
            statusMessage = rootElement.Q<Label>("status-message");

            // 获取按钮引用
            resetButton = rootElement.Q<Button>("reset-button");
            cancelButton = rootElement.Q<Button>("cancel-button");
            applyButton = rootElement.Q<Button>("apply-button");

            // 验证关键元素
            if (categoryListContainer == null || contentContainer == null ||
                resetButton == null || cancelButton == null || applyButton == null)
            {
                Logging.LogError("SettingsWindow", "无法找到必需的UI元素");
                return;
            }

            // 初始化模块管理器
            InitializeModuleManager();
        }

        private void BindEvents()
        {
            if (resetButton != null)
            {
                resetButton.clicked += OnResetButtonClicked;
            }

            if (cancelButton != null)
            {
                cancelButton.clicked += OnCancelButtonClicked;
            }

            if (applyButton != null)
            {
                applyButton.clicked += OnApplyButtonClicked;
            }
        }

        private void UnbindEvents()
        {
            if (resetButton != null)
            {
                resetButton.clicked -= OnResetButtonClicked;
            }

            if (cancelButton != null)
            {
                cancelButton.clicked -= OnCancelButtonClicked;
            }

            if (applyButton != null)
            {
                applyButton.clicked -= OnApplyButtonClicked;
            }
        }

        private void InitializeModuleManager()
        {
            try
            {
                moduleManager = new SettingsModuleManager();
                moduleManager.Initialize(categoryListContainer, contentContainer, searchField);

                // 绑定模块事件
                moduleManager.OnModuleSelected += OnModuleSelected;
                moduleManager.OnModuleDeselected += OnModuleDeselected;
                moduleManager.OnModulesLoaded += OnModulesLoaded;

                Logging.LogInfo("SettingsWindow", "模块管理器初始化完成");
            }
            catch (Exception ex)
            {
                Logging.LogError("SettingsWindow", $"初始化模块管理器时发生错误: {ex.Message}");
            }
        }

        private void LoadSettings()
        {
            try
            {
                settingsData ??= new SettingsData();

                // 加载默认值
                settingsData.LoadDefaults();

                // 创建原始数据的副本用于取消操作
                originalSettingsData = settingsData.Clone();

                // 更新所有模块的UI
                moduleManager?.LoadSettingsToModules(settingsData);

                Logging.LogInfo("SettingsWindow", "设置加载完成");
            }
            catch (Exception ex)
            {
                Logging.LogError("SettingsWindow", $"加载设置时发生错误: {ex.Message}");
            }
        }

        private void SaveSettings()
        {
            if (settingsData == null) return;

            try
            {
                // 从所有模块保存设置
                moduleManager?.SaveSettingsFromModules(settingsData);

                // 验证设置
                List<string> errorMessages = null;
                if (moduleManager != null && moduleManager.ValidateAllModules(settingsData, out errorMessages))
                {
                    // 持久化设置
                    settingsData.Save();

                    // 更新原始数据
                    originalSettingsData = settingsData.Clone();

                    // 更新状态为成功
                    UpdateStatus("● 已保存", "设置已成功保存", "status-ready");

                    Logging.LogInfo("SettingsWindow", "设置保存完成");
                }
                else
                {
                    string errorMessage = errorMessages != null && errorMessages.Count > 0 ? string.Join("\n", errorMessages) : "未知验证错误";
                    
                    // 更新状态为错误
                    UpdateStatus("● 错误", $"验证失败: {errorMessages?.FirstOrDefault() ?? "未知错误"}", "status-error");

                    Logging.LogError("SettingsWindow", $"设置验证失败: {errorMessage}");

                    // 可以在这里显示错误对话框
                    Debug.LogError($"设置验证失败:\n{errorMessage}");
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("SettingsWindow", $"保存设置时发生错误: {ex.Message}");
            }
        }

        private void CancelSettings()
        {
            if (originalSettingsData != null)
            {
                // 恢复原始设置
                settingsData.CopyFrom(originalSettingsData);

                // 更新所有模块的UI
                moduleManager?.LoadSettingsToModules(settingsData);

                Logging.LogInfo("SettingsWindow", "设置已取消并恢复");
            }
        }

        private void ResetSettings()
        {
            if (settingsData == null) return;

            try
            {
                // 重置所有模块到默认值
                moduleManager?.ResetAllModulesToDefaults(settingsData);

                Logging.LogInfo("SettingsWindow", "设置已重置为默认值");
            }
            catch (Exception ex)
            {
                Logging.LogError("SettingsWindow", $"重置设置时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region 事件处理

        private void OnResetButtonClicked()
        {
            try
            {
                ResetSettings();
                OnSettingsReset?.Invoke();
            }
            catch (Exception ex)
            {
                Logging.LogError("SettingsWindow", $"重置设置时发生错误: {ex.Message}");
            }
        }

        private void OnCancelButtonClicked()
        {
            try
            {
                CancelSettings();
                OnSettingsCancelled?.Invoke();
                Logging.LogInfo("SettingsWindow", "设置取消");
            }
            catch (Exception ex)
            {
                Logging.LogError("SettingsWindow", $"取消设置时发生错误: {ex.Message}");
            }
        }

        private void OnApplyButtonClicked()
        {
            try
            {
                SaveSettings();
                OnSettingsApplied?.Invoke(settingsData);
                Logging.LogInfo("SettingsWindow", "设置已应用");
            }
            catch (Exception ex)
            {
                Logging.LogError("SettingsWindow", $"应用设置时发生错误: {ex.Message}");
            }
        }

        private void OnModuleSelected(SettingsModule module)
        {
            Logging.LogInfo("SettingsWindow", $"选择模块: {module.DisplayName}");
            
            // 更新内容头部信息
            UpdateContentHeader(module.DisplayName, module.Description);
            
            // 更新状态为正常
            UpdateStatus("● 就绪", "", "status-ready");
        }

        private void OnModuleDeselected(SettingsModule module)
        {
            Logging.LogInfo("SettingsWindow", $"取消选择模块: {module.DisplayName}");
        }

        private void OnModulesLoaded(List<SettingsModule> modules)
        {
            Logging.LogInfo("SettingsWindow", $"加载了 {modules.Count} 个设置模块");
            
            // 如果有模块加载，显示第一个模块的信息
            if (modules.Count > 0)
            {
                var firstModule = modules[0];
                UpdateContentHeader(firstModule.DisplayName, firstModule.Description);
            }
        }

        /// <summary>
        /// 更新内容头部信息
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="description">描述</param>
        private void UpdateContentHeader(string title, string description)
        {
            if (currentCategoryTitle != null)
                currentCategoryTitle.text = title;
                
            if (currentCategoryDescription != null)
                currentCategoryDescription.text = description;
        }

        /// <summary>
        /// 更新状态指示器
        /// </summary>
        /// <param name="indicator">状态指示器文本</param>
        /// <param name="message">状态消息</param>
        /// <param name="statusClass">状态样式类</param>
        private void UpdateStatus(string indicator, string message, string statusClass)
        {
            if (statusIndicator != null)
            {
                statusIndicator.text = indicator;
                // 移除所有状态类
                statusIndicator.RemoveFromClassList("status-ready");
                statusIndicator.RemoveFromClassList("status-warning");
                statusIndicator.RemoveFromClassList("status-error");
                // 添加新的状态类
                statusIndicator.AddToClassList(statusClass);
            }
                
            if (statusMessage != null)
                statusMessage.text = message;
        }

        #endregion
    }
}