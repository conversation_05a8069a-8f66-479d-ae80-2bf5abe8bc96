using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 设置模块管理器 - 负责管理所有设置模块的加载、显示和交互
    /// </summary>
    public class SettingsModuleManager
    {
        #region 事件

        public event Action<SettingsModule> OnModuleSelected;
        public event Action<SettingsModule> OnModuleDeselected;
        public event Action<List<SettingsModule>> OnModulesLoaded;

        #endregion

        #region 私有字段

        private readonly Dictionary<string, SettingsModule> modules = new Dictionary<string, SettingsModule>();
        private readonly Dictionary<string, Button> categoryButtons = new Dictionary<string, Button>();
        private SettingsModule currentModule;
        private VisualElement categoryListContainer;
        private VisualElement contentContainer;
        private TextField searchField;
        private List<SettingsModule> filteredModules = new List<SettingsModule>();

        #endregion

        #region 公共方法

        /// <summary>
        /// 初始化模块管理器
        /// </summary>
        /// <param name="categoryList">分类列表容器</param>
        /// <param name="contentArea">内容区域容器</param>
        /// <param name="searchField">搜索字段</param>
        public void Initialize(VisualElement categoryList, VisualElement contentArea, TextField searchField = null)
        {
            this.categoryListContainer = categoryList;
            this.contentContainer = contentArea;
            this.searchField = searchField;

            // 绑定搜索事件
            if (this.searchField != null)
            {
                this.searchField.RegisterValueChangedCallback(OnSearchValueChanged);
            }

            // 加载默认模块
            LoadDefaultModules();
        }

        /// <summary>
        /// 添加模块
        /// </summary>
        /// <param name="module">要添加的模块</param>
        public void AddModule(SettingsModule module)
        {
            if (module == null || string.IsNullOrEmpty(module.ModuleId))
                return;

            if (modules.ContainsKey(module.ModuleId))
            {
                Debug.LogWarning($"模块 {module.ModuleId} 已存在，将被替换");
                RemoveModule(module.ModuleId);
            }

            modules[module.ModuleId] = module;
            module.Initialize();

            RefreshCategoryList();
            
            // 如果是第一个模块，自动选中
            if (currentModule == null)
            {
                SelectModule(module.ModuleId);
            }
        }

        /// <summary>
        /// 移除模块
        /// </summary>
        /// <param name="moduleId">模块ID</param>
        public void RemoveModule(string moduleId)
        {
            if (modules.TryGetValue(moduleId, out var module))
            {
                module.Cleanup();
                modules.Remove(moduleId);

                if (categoryButtons.TryGetValue(moduleId, out var button))
                {
                    button.RemoveFromHierarchy();
                    categoryButtons.Remove(moduleId);
                }

                if (currentModule == module)
                {
                    currentModule = null;
                    contentContainer?.Clear();
                    
                    // 选择第一个可用模块
                    if (modules.Count > 0)
                    {
                        SelectModule(modules.Keys.First());
                    }
                }

                RefreshCategoryList();
            }
        }

        /// <summary>
        /// 选择模块
        /// </summary>
        /// <param name="moduleId">模块ID</param>
        public void SelectModule(string moduleId)
        {
            if (!modules.TryGetValue(moduleId, out var module))
                return;

            // 取消选择当前模块
            if (currentModule != null)
            {
                currentModule.OnDeactivated();
                OnModuleDeselected?.Invoke(currentModule);
                
                // 更新按钮状态
                if (categoryButtons.TryGetValue(currentModule.ModuleId, out var oldButton))
                {
                    oldButton.RemoveFromClassList("selected");
                }
            }

            // 选择新模块
            currentModule = module;
            currentModule.OnActivated();
            OnModuleSelected?.Invoke(currentModule);

            // 更新按钮状态
            if (categoryButtons.TryGetValue(moduleId, out var newButton))
            {
                newButton.AddToClassList("selected");
            }

            // 加载模块内容
            LoadModuleContent(currentModule);
        }

        /// <summary>
        /// 获取当前选中的模块
        /// </summary>
        /// <returns>当前模块</returns>
        public SettingsModule GetCurrentModule()
        {
            return currentModule;
        }

        /// <summary>
        /// 获取所有模块
        /// </summary>
        /// <returns>模块列表</returns>
        public List<SettingsModule> GetAllModules()
        {
            return modules.Values.ToList();
        }

        /// <summary>
        /// 获取模块
        /// </summary>
        /// <param name="moduleId">模块ID</param>
        /// <returns>模块实例</returns>
        public SettingsModule GetModule(string moduleId)
        {
            return modules.TryGetValue(moduleId, out var module) ? module : null;
        }

        /// <summary>
        /// 加载设置到所有模块
        /// </summary>
        /// <param name="settingsData">设置数据</param>
        public void LoadSettingsToModules(SettingsData settingsData)
        {
            foreach (var module in modules.Values)
            {
                try
                {
                    module.LoadSettings(settingsData);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"加载模块 {module.ModuleId} 设置时发生错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 从所有模块保存设置
        /// </summary>
        /// <param name="settingsData">设置数据</param>
        public void SaveSettingsFromModules(SettingsData settingsData)
        {
            foreach (var module in modules.Values)
            {
                try
                {
                    module.SaveSettings(settingsData);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"保存模块 {module.ModuleId} 设置时发生错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 重置所有模块到默认值
        /// </summary>
        /// <param name="settingsData">设置数据</param>
        public void ResetAllModulesToDefaults(SettingsData settingsData)
        {
            foreach (var module in modules.Values)
            {
                try
                {
                    module.ResetToDefaults(settingsData);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"重置模块 {module.ModuleId} 到默认值时发生错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 验证所有模块设置
        /// </summary>
        /// <param name="settingsData">设置数据</param>
        /// <param name="errorMessages">错误信息列表</param>
        /// <returns>验证是否通过</returns>
        public bool ValidateAllModules(SettingsData settingsData, out List<string> errorMessages)
        {
            errorMessages = new List<string>();
            bool isValid = true;

            foreach (var module in modules.Values)
            {
                try
                {
                    if (!module.ValidateSettings(settingsData, out string errorMessage))
                    {
                        isValid = false;
                        errorMessages.Add($"[{module.DisplayName}] {errorMessage}");
                    }
                }
                catch (Exception ex)
                {
                    isValid = false;
                    errorMessages.Add($"[{module.DisplayName}] 验证时发生错误: {ex.Message}");
                }
            }

            return isValid;
        }

        /// <summary>
        /// 清理所有模块
        /// </summary>
        public void Cleanup()
        {
            foreach (var module in modules.Values)
            {
                module.Cleanup();
            }

            modules.Clear();
            categoryButtons.Clear();
            currentModule = null;
            filteredModules.Clear();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 加载默认模块
        /// </summary>
        private void LoadDefaultModules()
        {
            // 添加默认模块
            AddModule(new GeneralSettingsModule());
            AddModule(new DatabaseSettingsModule());
            AddModule(new SystemSettingsModule());

            OnModulesLoaded?.Invoke(GetAllModules());
        }

        /// <summary>
        /// 刷新分类列表
        /// </summary>
        private void RefreshCategoryList()
        {
            if (categoryListContainer == null)
                return;

            // 清空现有按钮
            categoryListContainer.Clear();
            categoryButtons.Clear();

            // 获取过滤后的模块
            UpdateFilteredModules();

            // 按优先级排序
            var sortedModules = filteredModules.OrderBy(m => m.Priority).ToList();

            // 创建分类按钮
            foreach (var module in sortedModules)
            {
                if (!module.IsEnabled)
                    continue;

                var button = new Button();
                button.text = module.DisplayName;
                button.AddToClassList("category-button");
                
                // 如果是当前选中的模块，添加选中状态
                if (currentModule?.ModuleId == module.ModuleId)
                {
                    button.AddToClassList("selected");
                }

                button.clicked += () => SelectModule(module.ModuleId);

                categoryListContainer.Add(button);
                categoryButtons[module.ModuleId] = button;
            }
        }

        /// <summary>
        /// 加载模块内容
        /// </summary>
        /// <param name="module">要加载的模块</param>
        private void LoadModuleContent(SettingsModule module)
        {
            if (contentContainer == null || module == null)
                return;

            try
            {
                // 清空现有内容
                contentContainer.Clear();

                // 创建模块UI
                var moduleUI = module.CreateUI();
                if (moduleUI != null)
                {
                    contentContainer.Add(moduleUI);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载模块 {module.ModuleId} 内容时发生错误: {ex.Message}");
                
                // 显示错误信息
                var errorLabel = new Label($"加载模块内容时发生错误: {ex.Message}");
                errorLabel.AddToClassList("error-label");
                contentContainer.Add(errorLabel);
            }
        }

        /// <summary>
        /// 更新过滤后的模块列表
        /// </summary>
        private void UpdateFilteredModules()
        {
            filteredModules.Clear();

            string searchText = searchField?.value?.Trim();
            
            foreach (var module in modules.Values)
            {
                if (string.IsNullOrEmpty(searchText) || module.MatchesSearch(searchText))
                {
                    filteredModules.Add(module);
                }
            }
        }

        /// <summary>
        /// 搜索值变化事件处理
        /// </summary>
        /// <param name="evt">变化事件</param>
        private void OnSearchValueChanged(ChangeEvent<string> evt)
        {
            RefreshCategoryList();
        }

        #endregion
    }
}