using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 设置模块基类 - 定义设置模块的基本接口
    /// </summary>
    public abstract class SettingsModule
    {
        #region 属性

        /// <summary>
        /// 模块ID
        /// </summary>
        public abstract string ModuleId { get; }

        /// <summary>
        /// 模块显示名称
        /// </summary>
        public abstract string DisplayName { get; }

        /// <summary>
        /// 模块描述
        /// </summary>
        public abstract string Description { get; }

        /// <summary>
        /// 模块图标（可选）
        /// </summary>
        public virtual Texture2D Icon { get; }

        /// <summary>
        /// 模块排序优先级（越小越靠前）
        /// </summary>
        public virtual int Priority => 0;

        /// <summary>
        /// 模块是否启用
        /// </summary>
        public virtual bool IsEnabled => true;

        #endregion

        #region 抽象方法

        /// <summary>
        /// 创建模块的UI元素
        /// </summary>
        /// <returns>模块的根UI元素</returns>
        public abstract VisualElement CreateUI();

        /// <summary>
        /// 加载模块设置数据
        /// </summary>
        /// <param name="settingsData">设置数据对象</param>
        public abstract void LoadSettings(SettingsData settingsData);

        /// <summary>
        /// 保存模块设置数据
        /// </summary>
        /// <param name="settingsData">设置数据对象</param>
        public abstract void SaveSettings(SettingsData settingsData);

        /// <summary>
        /// 重置模块设置为默认值
        /// </summary>
        /// <param name="settingsData">设置数据对象</param>
        public abstract void ResetToDefaults(SettingsData settingsData);

        /// <summary>
        /// 验证模块设置数据
        /// </summary>
        /// <param name="settingsData">设置数据对象</param>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>验证是否通过</returns>
        public abstract bool ValidateSettings(SettingsData settingsData, out string errorMessage);

        #endregion

        #region 虚方法

        /// <summary>
        /// 模块初始化
        /// </summary>
        public virtual void Initialize()
        {
            // 默认实现为空
        }

        /// <summary>
        /// 模块清理
        /// </summary>
        public virtual void Cleanup()
        {
            // 默认实现为空
        }

        /// <summary>
        /// 模块激活时调用
        /// </summary>
        public virtual void OnActivated()
        {
            // 默认实现为空
        }

        /// <summary>
        /// 模块停用时调用
        /// </summary>
        public virtual void OnDeactivated()
        {
            // 默认实现为空
        }

        /// <summary>
        /// 处理搜索过滤
        /// </summary>
        /// <param name="searchText">搜索文本</param>
        /// <returns>是否匹配搜索条件</returns>
        public virtual bool MatchesSearch(string searchText)
        {
            if (string.IsNullOrEmpty(searchText))
                return true;

            var lowerSearchText = searchText.ToLower();
            return DisplayName.ToLower().Contains(lowerSearchText) ||
                   Description.ToLower().Contains(lowerSearchText);
        }

        #endregion
    }

    /// <summary>
    /// 设置模块配置 - 用于ScriptableObject配置
    /// </summary>
    [Serializable]
    public class SettingsModuleConfig
    {
        [Header("模块信息")]
        public string moduleId;
        public string displayName;
        public string description;
        public Texture2D icon;
        public int priority;
        public bool isEnabled = true;

        [Header("UI资源")]
        public VisualTreeAsset uiTemplate;
        public StyleSheet styleSheet;

        [Header("模块类型")]
        public string moduleClassName;
    }

    /// <summary>
    /// 设置模块注册器 - 管理所有设置模块
    /// </summary>
    [CreateAssetMenu(fileName = "SettingsModuleRegistry", menuName = "BlastingDesign/Settings/Module Registry")]
    public class SettingsModuleRegistry : ScriptableObject
    {
        [Header("模块配置")]
        [SerializeField] private List<SettingsModuleConfig> moduleConfigs = new List<SettingsModuleConfig>();

        /// <summary>
        /// 获取所有模块配置
        /// </summary>
        public List<SettingsModuleConfig> GetModuleConfigs()
        {
            return new List<SettingsModuleConfig>(moduleConfigs);
        }

        /// <summary>
        /// 根据ID获取模块配置
        /// </summary>
        /// <param name="moduleId">模块ID</param>
        /// <returns>模块配置</returns>
        public SettingsModuleConfig GetModuleConfig(string moduleId)
        {
            return moduleConfigs.Find(config => config.moduleId == moduleId);
        }

        /// <summary>
        /// 添加模块配置
        /// </summary>
        /// <param name="config">模块配置</param>
        public void AddModuleConfig(SettingsModuleConfig config)
        {
            if (config != null && !moduleConfigs.Exists(c => c.moduleId == config.moduleId))
            {
                moduleConfigs.Add(config);
            }
        }

        /// <summary>
        /// 移除模块配置
        /// </summary>
        /// <param name="moduleId">模块ID</param>
        public void RemoveModuleConfig(string moduleId)
        {
            moduleConfigs.RemoveAll(config => config.moduleId == moduleId);
        }

        /// <summary>
        /// 获取已启用的模块配置（按优先级排序）
        /// </summary>
        /// <returns>已启用的模块配置列表</returns>
        public List<SettingsModuleConfig> GetEnabledModuleConfigs()
        {
            var enabledConfigs = moduleConfigs.FindAll(config => config.isEnabled);
            enabledConfigs.Sort((a, b) => a.priority.CompareTo(b.priority));
            return enabledConfigs;
        }

        #region 编辑器方法

#if UNITY_EDITOR
        /// <summary>
        /// 验证模块配置
        /// </summary>
        public void ValidateConfigs()
        {
            for (int i = moduleConfigs.Count - 1; i >= 0; i--)
            {
                var config = moduleConfigs[i];
                if (config == null || string.IsNullOrEmpty(config.moduleId) || string.IsNullOrEmpty(config.displayName))
                {
                    Debug.LogWarning($"移除无效的模块配置: {config?.moduleId}");
                    moduleConfigs.RemoveAt(i);
                }
            }

            // 检查重复的模块ID
            var duplicateIds = new HashSet<string>();
            for (int i = 0; i < moduleConfigs.Count; i++)
            {
                if (duplicateIds.Contains(moduleConfigs[i].moduleId))
                {
                    Debug.LogError($"发现重复的模块ID: {moduleConfigs[i].moduleId}");
                }
                else
                {
                    duplicateIds.Add(moduleConfigs[i].moduleId);
                }
            }
        }
#endif

        #endregion
    }
}