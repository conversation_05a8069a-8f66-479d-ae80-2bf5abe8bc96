using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Core;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 数据库设置模块
    /// </summary>
    public class DatabaseSettingsModule : SettingsModule
    {
        #region 属性

        public override string ModuleId => "database";
        public override string DisplayName => "🗄 数据库";
        public override string Description => "数据库连接和配置设置，支持SQLite和SQL Server";
        public override int Priority => 1;

        #endregion

        #region 私有字段

        private TextField sqlitePathField;
        private Toggle sqliteSqlPrintToggle;
        private Toggle sqlServerEnabledToggle;
        private TextField sqlServerHostField;
        private TextField sqlServerPortField;
        private TextField sqlServerDatabaseField;
        private TextField sqlServerUsernameField;
        private TextField sqlServerPasswordField;
        private VisualElement sqlServerContainer;

        #endregion

        #region 实现抽象方法

        public override VisualElement CreateUI()
        {
            var root = new VisualElement();
            root.AddToClassList("settings-module");

            // 标题
            var title = new Label("数据库设置");
            title.AddToClassList("module-title");
            root.Add(title);

            // SQLite 设置组
            var sqliteGroup = new VisualElement();
            sqliteGroup.AddToClassList("settings-group");
            
            var sqliteGroupTitle = new Label("SQLite 设置");
            sqliteGroupTitle.AddToClassList("group-title");
            sqliteGroup.Add(sqliteGroupTitle);

            // SQLite 路径
            var sqlitePathContainer = new VisualElement();
            sqlitePathContainer.AddToClassList("setting-item");
            
            var sqlitePathLabel = new Label("数据库路径");
            sqlitePathLabel.AddToClassList("setting-label");
            sqlitePathContainer.Add(sqlitePathLabel);
            
            sqlitePathField = new TextField().AddFocusBasedKeyboardEventBlocker();
            sqlitePathField.AddToClassList("setting-textfield");
            sqlitePathContainer.Add(sqlitePathField);
            
            sqliteGroup.Add(sqlitePathContainer);

            // SQLite SQL打印
            var sqliteSqlPrintContainer = new VisualElement();
            sqliteSqlPrintContainer.AddToClassList("setting-item");
            
            var sqliteSqlPrintLabel = new Label("启用SQL日志");
            sqliteSqlPrintLabel.AddToClassList("setting-label");
            sqliteSqlPrintContainer.Add(sqliteSqlPrintLabel);
            
            sqliteSqlPrintToggle = new Toggle();
            sqliteSqlPrintToggle.AddToClassList("setting-toggle");
            sqliteSqlPrintContainer.Add(sqliteSqlPrintToggle);
            
            sqliteGroup.Add(sqliteSqlPrintContainer);

            root.Add(sqliteGroup);

            // SQL Server 设置组
            var sqlServerGroup = new VisualElement();
            sqlServerGroup.AddToClassList("settings-group");
            
            var sqlServerGroupTitle = new Label("SQL Server 设置");
            sqlServerGroupTitle.AddToClassList("group-title");
            sqlServerGroup.Add(sqlServerGroupTitle);

            // SQL Server 启用
            var sqlServerEnabledContainer = new VisualElement();
            sqlServerEnabledContainer.AddToClassList("setting-item");
            
            var sqlServerEnabledLabel = new Label("启用SQL Server");
            sqlServerEnabledLabel.AddToClassList("setting-label");
            sqlServerEnabledContainer.Add(sqlServerEnabledLabel);
            
            sqlServerEnabledToggle = new Toggle();
            sqlServerEnabledToggle.AddToClassList("setting-toggle");
            sqlServerEnabledContainer.Add(sqlServerEnabledToggle);
            
            sqlServerGroup.Add(sqlServerEnabledContainer);

            // SQL Server 详细设置容器
            sqlServerContainer = new VisualElement();
            sqlServerContainer.AddToClassList("settings-sub-group");

            // 主机
            var hostContainer = new VisualElement();
            hostContainer.AddToClassList("setting-item");
            
            var hostLabel = new Label("服务器地址");
            hostLabel.AddToClassList("setting-label");
            hostContainer.Add(hostLabel);
            
            sqlServerHostField = new TextField().AddFocusBasedKeyboardEventBlocker();
            sqlServerHostField.AddToClassList("setting-textfield");
            hostContainer.Add(sqlServerHostField);
            
            sqlServerContainer.Add(hostContainer);

            // 端口
            var portContainer = new VisualElement();
            portContainer.AddToClassList("setting-item");
            
            var portLabel = new Label("端口");
            portLabel.AddToClassList("setting-label");
            portContainer.Add(portLabel);
            
            sqlServerPortField = new TextField().AddFocusBasedKeyboardEventBlocker();
            sqlServerPortField.AddToClassList("setting-textfield");
            portContainer.Add(sqlServerPortField);
            
            sqlServerContainer.Add(portContainer);

            // 数据库
            var databaseContainer = new VisualElement();
            databaseContainer.AddToClassList("setting-item");
            
            var databaseLabel = new Label("数据库名");
            databaseLabel.AddToClassList("setting-label");
            databaseContainer.Add(databaseLabel);
            
            sqlServerDatabaseField = new TextField().AddFocusBasedKeyboardEventBlocker();
            sqlServerDatabaseField.AddToClassList("setting-textfield");
            databaseContainer.Add(sqlServerDatabaseField);
            
            sqlServerContainer.Add(databaseContainer);

            // 用户名
            var usernameContainer = new VisualElement();
            usernameContainer.AddToClassList("setting-item");
            
            var usernameLabel = new Label("用户名");
            usernameLabel.AddToClassList("setting-label");
            usernameContainer.Add(usernameLabel);
            
            sqlServerUsernameField = new TextField().AddFocusBasedKeyboardEventBlocker();
            sqlServerUsernameField.AddToClassList("setting-textfield");
            usernameContainer.Add(sqlServerUsernameField);
            
            sqlServerContainer.Add(usernameContainer);

            // 密码
            var passwordContainer = new VisualElement();
            passwordContainer.AddToClassList("setting-item");
            
            var passwordLabel = new Label("密码");
            passwordLabel.AddToClassList("setting-label");
            passwordContainer.Add(passwordLabel);
            
            sqlServerPasswordField = new TextField().AddFocusBasedKeyboardEventBlocker();
            sqlServerPasswordField.AddToClassList("setting-textfield");
            sqlServerPasswordField.isPasswordField = true;
            passwordContainer.Add(sqlServerPasswordField);
            
            sqlServerContainer.Add(passwordContainer);

            sqlServerGroup.Add(sqlServerContainer);
            root.Add(sqlServerGroup);

            // 绑定事件
            sqlServerEnabledToggle.RegisterValueChangedCallback(evt =>
            {
                sqlServerContainer.style.display = evt.newValue ? DisplayStyle.Flex : DisplayStyle.None;
            });

            return root;
        }

        public override void LoadSettings(SettingsData settingsData)
        {
            if (settingsData == null) return;

            sqlitePathField?.SetValueWithoutNotify(settingsData.SqlitePath);
            sqliteSqlPrintToggle?.SetValueWithoutNotify(settingsData.SqliteSqlPrint);
            sqlServerEnabledToggle?.SetValueWithoutNotify(settingsData.SqlServerEnabled);
            sqlServerHostField?.SetValueWithoutNotify(settingsData.SqlServerHost);
            sqlServerPortField?.SetValueWithoutNotify(settingsData.SqlServerPort);
            sqlServerDatabaseField?.SetValueWithoutNotify(settingsData.SqlServerDatabase);
            sqlServerUsernameField?.SetValueWithoutNotify(settingsData.SqlServerUsername);
            sqlServerPasswordField?.SetValueWithoutNotify(settingsData.SqlServerPassword);

            // 更新SQL Server容器显示状态
            if (sqlServerContainer != null)
            {
                sqlServerContainer.style.display = settingsData.SqlServerEnabled ? DisplayStyle.Flex : DisplayStyle.None;
            }
        }

        public override void SaveSettings(SettingsData settingsData)
        {
            if (settingsData == null) return;

            settingsData.SqlitePath = sqlitePathField?.value ?? settingsData.SqlitePath;
            settingsData.SqliteSqlPrint = sqliteSqlPrintToggle?.value ?? settingsData.SqliteSqlPrint;
            settingsData.SqlServerEnabled = sqlServerEnabledToggle?.value ?? settingsData.SqlServerEnabled;
            settingsData.SqlServerHost = sqlServerHostField?.value ?? settingsData.SqlServerHost;
            settingsData.SqlServerPort = sqlServerPortField?.value ?? settingsData.SqlServerPort;
            settingsData.SqlServerDatabase = sqlServerDatabaseField?.value ?? settingsData.SqlServerDatabase;
            settingsData.SqlServerUsername = sqlServerUsernameField?.value ?? settingsData.SqlServerUsername;
            settingsData.SqlServerPassword = sqlServerPasswordField?.value ?? settingsData.SqlServerPassword;
        }

        public override void ResetToDefaults(SettingsData settingsData)
        {
            if (settingsData == null) return;

            settingsData.SqlitePath = "data/database.db";
            settingsData.SqliteSqlPrint = false;
            settingsData.SqlServerEnabled = false;
            settingsData.SqlServerHost = "localhost";
            settingsData.SqlServerPort = "1433";
            settingsData.SqlServerDatabase = "BlastingDesign";
            settingsData.SqlServerUsername = "";
            settingsData.SqlServerPassword = "";

            LoadSettings(settingsData);
        }

        public override bool ValidateSettings(SettingsData settingsData, out string errorMessage)
        {
            errorMessage = "";

            if (settingsData == null)
            {
                errorMessage = "设置数据为空";
                return false;
            }

            if (string.IsNullOrWhiteSpace(settingsData.SqlitePath))
            {
                errorMessage = "SQLite数据库路径不能为空";
                return false;
            }

            if (settingsData.SqlServerEnabled)
            {
                if (string.IsNullOrWhiteSpace(settingsData.SqlServerHost))
                {
                    errorMessage = "启用SQL Server时，服务器地址不能为空";
                    return false;
                }

                if (string.IsNullOrWhiteSpace(settingsData.SqlServerDatabase))
                {
                    errorMessage = "启用SQL Server时，数据库名不能为空";
                    return false;
                }

                if (!int.TryParse(settingsData.SqlServerPort, out int port) || port < 1 || port > 65535)
                {
                    errorMessage = "数据库端口必须是1-65535之间的有效数字";
                    return false;
                }
            }

            return true;
        }

        #endregion
    }
}