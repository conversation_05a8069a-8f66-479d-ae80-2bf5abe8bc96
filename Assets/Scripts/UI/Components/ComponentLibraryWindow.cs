using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Utils;
using BlastingDesign.UI.Core;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// DaisyUI组件库窗口 - 展示和管理DaisyUI组件的专用窗口
    /// </summary>
    public partial class ComponentLibraryWindow : UIComponentBase
    {
        #region 配置和设置

        [Header("Component Library Settings")]
        [SerializeField] private string windowTitle = "DaisyUI组件库";
        [SerializeField] private Vector2 windowSize = new Vector2(900, 700);
        [SerializeField] private bool showOnStart = false;

        #endregion

        #region 私有字段

        private ModalWindow modalWindow;
        private string selectedComponentId = "";
        private List<Button> categoryButtons = new List<Button>();

        // UI元素缓存
        private Button closeButton;
        private Button refreshButton;
        private Button themeToggleButton;
        private Label componentNameLabel;
        private Label componentDescriptionLabel;
        private ScrollView contentBodyScrollView;
        private Label statusLabel;

        #endregion

        #region 事件定义

        public event Action<string> OnComponentSelected;
        public event Action OnWindowClosed;
        public event Action<bool> OnThemeChanged;

        #endregion

        #region 生命周期管理

        protected override void Awake()
        {
            base.Awake();

            // 设置组件名称
            if (string.IsNullOrEmpty(componentName))
            {
                componentName = "ComponentLibraryWindow";
            }
        }

        private void Start()
        {
            if (showOnStart)
            {
                ShowWindow();
            }
        }

        protected override void OnDestroy()
        {
            // 清理资源
            CleanupResources();
            base.OnDestroy();
        }

        #endregion
    }
}