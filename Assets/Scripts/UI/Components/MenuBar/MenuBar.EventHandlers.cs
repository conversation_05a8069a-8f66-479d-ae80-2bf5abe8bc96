using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Config;
using BlastingDesign.UI.Core;
using BlastingDesign.Utils;
using BlastingDesign.Events;
using BlastingDesign.Events.Core;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// MenuBar事件处理类 - 负责各种用户交互事件的处理
    /// </summary>
    public partial class MenuBar : UIElementBase, IMenuComponent
    {
        #region 事件处理

        /// <summary>
        /// 菜单按钮点击处理
        /// </summary>
        private void OnMenuButtonClicked(string categoryName, Button button)
        {
            Logging.LogInfo("MenuBar", $"菜单按钮点击: {categoryName}");

            if (dropdownMenus.ContainsKey(categoryName))
            {
                var dropdown = dropdownMenus[categoryName];
                Logging.LogInfo("MenuBar", $"找到下拉菜单: {categoryName}, dropdown: {dropdown != null}");

                if (currentOpenDropdown == dropdown)
                {
                    // 关闭当前下拉菜单
                    Logging.LogInfo("MenuBar", $"关闭当前下拉菜单: {categoryName}");
                    HideDropdown(dropdown);
                    SetMenuButtonActive(categoryName, false);
                    currentOpenDropdown = null;
                }
                else
                {
                    // 关闭其他下拉菜单
                    HideAllDropdowns();

                    // 显示当前下拉菜单
                    Logging.LogInfo("MenuBar", $"显示下拉菜单: {categoryName}");
                    ShowDropdown(dropdown, button);
                    SetMenuButtonActive(categoryName, true);
                    currentOpenDropdown = dropdown;
                }
            }
            else
            {
                Logging.LogWarning("MenuBar", $"未找到下拉菜单: {categoryName}, 可用菜单: {string.Join(", ", dropdownMenus.Keys)}");
            }
        }

        /// <summary>
        /// 菜单项点击处理
        /// </summary>
        private void OnMenuItemClicked(MenuBarConfig.MenuItem item)
        {
            // 关闭下拉菜单
            HideAllDropdowns();

            // 执行回调
            if (!string.IsNullOrEmpty(item.callbackName) && callbackSystem != null)
            {
                callbackSystem.ExecuteCallback(item.callbackName, item.Parameters);
            }

            // 使用新事件系统（优先）
            if (BlastingDesign.Events.EventSystemManager.Instance != null && BlastingDesign.Events.EventSystemManager.Instance.EventBus != null)
            {
                // 发布新事件系统事件
                BlastingDesign.Events.EventSystemManager.Instance.EventBus.Publish(new MenuItemClickedEvent(item.name, item.Parameters));
                BlastingDesign.Events.EventSystemManager.Instance.EventBus.Publish(new StatusMessageEvent($"执行菜单项: {item.displayName}", StatusMessageType.Info));
            }
            else
            {
                // 回退到旧事件系统
                UIEventSystem.TriggerMenuItemClicked(item.name);
                UIEventSystem.TriggerStatusMessage($"执行菜单项: {item.displayName}");
            }

            Logging.LogInfo("MenuBar", $"菜单项点击: {item.displayName}");
        }

        /// <summary>
        /// 全局点击事件处理（关闭下拉菜单）
        /// </summary>
        private void OnGlobalClick(ClickEvent evt)
        {
            // 检查点击是否在菜单区域外
            if (currentOpenDropdown != null)
            {
                if (evt.target is VisualElement clickedElement && !IsClickInsideMenu(clickedElement))
                {
                    HideAllDropdowns();
                }
            }
        }

        /// <summary>
        /// 菜单项鼠标进入事件
        /// </summary>
        private void OnMenuItemHover(MouseEnterEvent evt)
        {
            if (evt.target is Button menuItem)
            {
                menuItem.style.backgroundColor = new Color(0.24f, 0.37f, 0.59f, 1f); // rgb(62, 95, 150)
                menuItem.style.color = Color.white;
            }
        }

        /// <summary>
        /// 菜单项鼠标离开事件
        /// </summary>
        private void OnMenuItemLeave(MouseLeaveEvent evt)
        {
            if (evt.target is Button menuItem)
            {
                menuItem.style.backgroundColor = Color.clear;
                menuItem.style.color = new Color(0.82f, 0.82f, 0.82f, 1f); // rgb(210, 210, 210)
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 检查点击是否在菜单内部
        /// </summary>
        private bool IsClickInsideMenu(VisualElement clickedElement)
        {
            var current = clickedElement;
            while (current != null)
            {
                if (current == this || current == currentOpenDropdown)
                {
                    return true;
                }
                current = current.parent;
            }
            return false;
        }

        /// <summary>
        /// 设置菜单按钮激活状态
        /// </summary>
        private void SetMenuButtonActive(string menuName, bool isActive)
        {
            if (menuButtons.ContainsKey(menuName))
            {
                var button = menuButtons[menuName];
                if (isActive)
                {
                    button.AddToClassList("active");
                }
                else
                {
                    button.RemoveFromClassList("active");
                }
            }
        }

        /// <summary>
        /// 为菜单项设置hover事件
        /// </summary>
        private void SetupMenuItemHoverEvents(VisualElement dropdown)
        {
            try
            {
                foreach (var child in dropdown.Children())
                {
                    if (child is Button menuItem && child.ClassListContains("dropdown-item"))
                    {
                        // 移除现有的hover事件（避免重复注册）
                        menuItem.UnregisterCallback<MouseEnterEvent>(OnMenuItemHover);
                        menuItem.UnregisterCallback<MouseLeaveEvent>(OnMenuItemLeave);

                        // 注册新的hover事件
                        menuItem.RegisterCallback<MouseEnterEvent>(OnMenuItemHover);
                        menuItem.RegisterCallback<MouseLeaveEvent>(OnMenuItemLeave);
                    }
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("MenuBar", $"设置菜单项hover事件时发生错误: {ex.Message}");
            }
        }

        #endregion
    }
}