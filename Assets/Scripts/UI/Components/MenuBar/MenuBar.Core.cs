using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Core;
using BlastingDesign.UI.Config;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// MenuBar核心功能类 - 字段定义和生命周期管理
    /// </summary>
    public partial class MenuBar : UIElementBase, IMenuComponent
    {
        #region 字段定义

        [Header("MenuBar Settings")]
        private MenuBarConfig config;
        private readonly string configPath = "UI/MenuBarConfig";

        // UI元素引用
        private VisualElement menuCategories;
        private readonly Dictionary<string, Button> menuButtons = new Dictionary<string, Button>();
        private readonly Dictionary<string, VisualElement> dropdownMenus = new Dictionary<string, VisualElement>();
        private VisualElement currentOpenDropdown;
        private VisualElement rootElement;

        // 回调系统
        private UICallbackSystem callbackSystem;

        #endregion

        #region 生命周期方法

        protected override void CacheUIElements()
        {
            // 缓存主要UI元素
            menuCategories = SafeQuery<VisualElement>("menu-categories");

            // 获取回调系统引用
            callbackSystem = UICallbackSystem.Instance;

            // 获取根元素
            rootElement = GetRootElement();

            // 调试信息
            Logging.LogInfo("MenuBar", $"缓存UI元素完成 - menuCategories: {menuCategories != null}, rootElement: {rootElement != null}");
        }

        protected override void InitializeData()
        {
            // 加载配置
            LoadConfig();

            // 根据配置生成菜单
            if (config != null && config.ValidateConfig())
            {
                GenerateMenuFromConfig();
            }
            else
            {
                Logging.LogWarning("MenuBar", "配置无效，使用默认菜单");
                CreateDefaultMenu();
            }
        }

        protected override void SetupEventListeners()
        {
            // 在根元素上注册全局点击事件监听
            if (rootElement != null)
            {
                rootElement.RegisterCallback<ClickEvent>(OnGlobalClick);
                Logging.LogInfo("MenuBar", "根元素全局点击事件监听已注册");
            }
            else
            {
                Logging.LogWarning("MenuBar", "根元素为空，无法注册全局点击事件");
            }
        }

        protected override void RemoveEventListeners()
        {
            // 清理事件监听器
            rootElement?.UnregisterCallback<ClickEvent>(OnGlobalClick);

            foreach (var button in menuButtons.Values)
            {
                button?.UnregisterCallback<ClickEvent>(evt => { });
            }
        }

        #endregion

        #region 默认菜单创建

        /// <summary>
        /// 创建默认菜单（备用方案）
        /// </summary>
        private void CreateDefaultMenu()
        {
            // 简单的默认菜单实现
            var fileButton = new Button { text = "文件" };
            fileButton.AddToClassList("menu-button");
            menuCategories.Add(fileButton);

            var editButton = new Button { text = "编辑" };
            editButton.AddToClassList("menu-button");
            menuCategories.Add(editButton);

            Logging.LogInfo("MenuBar", "创建了默认菜单");
        }

        #endregion
    }
}