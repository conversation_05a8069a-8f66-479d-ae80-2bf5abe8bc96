using System;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Core;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// MenuBar样式管理类 - 负责样式的应用和管理
    /// </summary>
    public partial class MenuBar : UIElementBase, IMenuComponent
    {
        #region 样式管理

        /// <summary>
        /// 为下拉菜单应用样式表
        /// </summary>
        private void ApplyStyleSheetToDropdown(VisualElement dropdown)
        {
            try
            {
                // 获取MenuBar的样式表
                var menuBarStyleSheet = GetMenuBarStyleSheet();
                if (menuBarStyleSheet != null)
                {
                    dropdown.styleSheets.Add(menuBarStyleSheet);
                    Logging.LogInfo("MenuBar", $"已为下拉菜单 {dropdown.name} 应用样式表");
                }
                else
                {
                    Logging.LogWarning("MenuBar", "无法获取MenuBar样式表，下拉菜单可能没有正确的样式");
                    // 尝试手动应用基础样式
                    ApplyFallbackDropdownStyles(dropdown);
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("MenuBar", $"为下拉菜单应用样式表时发生错误: {ex.Message}");
                ApplyFallbackDropdownStyles(dropdown);
            }
        }

        /// <summary>
        /// 获取MenuBar的样式表
        /// </summary>
        private StyleSheet GetMenuBarStyleSheet()
        {
            try
            {
                // 首先尝试从当前元素的样式表中获取
                if (styleSheets.count > 0)
                {
                    return styleSheets[0];
                }

                // 尝试从资源中加载
                var styleSheet = Resources.Load<StyleSheet>("UI/MenuBar/MenuBar");
                if (styleSheet != null)
                {
                    return styleSheet;
                }

                // 尝试不同的路径
                styleSheet = Resources.Load<StyleSheet>("MenuBar/MenuBar");
                if (styleSheet != null)
                {
                    return styleSheet;
                }

                Logging.LogWarning("MenuBar", "无法从资源中加载MenuBar样式表");
                return null;
            }
            catch (Exception ex)
            {
                Logging.LogError("MenuBar", $"获取MenuBar样式表时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 应用备用下拉菜单样式
        /// </summary>
        private void ApplyFallbackDropdownStyles(VisualElement dropdown)
        {
            try
            {
                // 手动设置基础样式，确保下拉菜单至少有基本的外观
                dropdown.style.backgroundColor = new Color(0.22f, 0.22f, 0.22f, 1f); // rgb(56, 56, 56)
                dropdown.style.borderLeftColor = new Color(0.14f, 0.14f, 0.14f, 1f); // rgb(35, 35, 35)
                dropdown.style.borderRightColor = new Color(0.14f, 0.14f, 0.14f, 1f);
                dropdown.style.borderTopColor = new Color(0.14f, 0.14f, 0.14f, 1f);
                dropdown.style.borderBottomColor = new Color(0.14f, 0.14f, 0.14f, 1f);
                dropdown.style.borderLeftWidth = 1;
                dropdown.style.borderRightWidth = 1;
                dropdown.style.borderTopWidth = 1;
                dropdown.style.borderBottomWidth = 1;
                dropdown.style.borderTopLeftRadius = 2;
                dropdown.style.borderTopRightRadius = 2;
                dropdown.style.borderBottomLeftRadius = 2;
                dropdown.style.borderBottomRightRadius = 2;
                dropdown.style.paddingTop = 4;
                dropdown.style.paddingBottom = 4;
                dropdown.style.paddingLeft = 0;
                dropdown.style.paddingRight = 0;
                dropdown.style.marginTop = 2;
                dropdown.style.width = 180;
                dropdown.style.minWidth = 160;

                // 为菜单项应用样式
                ApplyFallbackMenuItemStyles(dropdown);

                Logging.LogInfo("MenuBar", $"已为下拉菜单 {dropdown.name} 应用备用样式");
            }
            catch (Exception ex)
            {
                Logging.LogError("MenuBar", $"应用备用下拉菜单样式时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 为菜单项应用备用样式
        /// </summary>
        private void ApplyFallbackMenuItemStyles(VisualElement dropdown)
        {
            try
            {
                foreach (var child in dropdown.Children())
                {
                    if (child is Button menuItem && child.ClassListContains("dropdown-item"))
                    {
                        // 设置菜单项基础样式
                        menuItem.style.backgroundColor = Color.clear;
                        menuItem.style.borderLeftWidth = 0;
                        menuItem.style.borderRightWidth = 0;
                        menuItem.style.borderTopWidth = 0;
                        menuItem.style.borderBottomWidth = 0;
                        menuItem.style.color = new Color(0.82f, 0.82f, 0.82f, 1f); // rgb(210, 210, 210)
                        menuItem.style.paddingTop = 7;
                        menuItem.style.paddingBottom = 7;
                        menuItem.style.paddingLeft = 16;
                        menuItem.style.paddingRight = 16;
                        menuItem.style.marginLeft = 2;
                        menuItem.style.marginRight = 2;
                        menuItem.style.unityTextAlign = TextAnchor.MiddleLeft;
                        menuItem.style.fontSize = 12;
                        menuItem.style.borderTopLeftRadius = 2;
                        menuItem.style.borderTopRightRadius = 2;
                        menuItem.style.borderBottomLeftRadius = 2;
                        menuItem.style.borderBottomRightRadius = 2;
                        menuItem.style.height = 22;
                        menuItem.style.minHeight = 22;
                    }
                    else if (child.ClassListContains("dropdown-separator"))
                    {
                        // 设置分隔符样式
                        child.style.height = 1;
                        child.style.backgroundColor = new Color(0.14f, 0.14f, 0.14f, 1f); // rgb(35, 35, 35)
                        child.style.marginTop = 4;
                        child.style.marginBottom = 4;
                        child.style.marginLeft = 8;
                        child.style.marginRight = 8;
                    }
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("MenuBar", $"应用备用菜单项样式时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 确保下拉菜单样式正确应用
        /// </summary>
        private void EnsureDropdownStyles(VisualElement dropdown)
        {
            try
            {
                // 检查下拉菜单是否有正确的背景色，如果没有则应用备用样式
                var backgroundColor = dropdown.resolvedStyle.backgroundColor;

                // 如果背景色是透明或默认色，说明样式可能没有正确应用
                if (backgroundColor.a < 0.1f || (backgroundColor.r < 0.1f && backgroundColor.g < 0.1f && backgroundColor.b < 0.1f))
                {
                    Logging.LogWarning("MenuBar", $"下拉菜单 {dropdown.name} 样式可能未正确应用，使用备用样式");
                    ApplyFallbackDropdownStyles(dropdown);
                }
                else
                {
                    // 确保visible类被正确处理
                    if (!dropdown.ClassListContains("visible"))
                    {
                        dropdown.AddToClassList("visible");
                    }

                    // 为新添加的菜单项应用hover事件
                    SetupMenuItemHoverEvents(dropdown);
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("MenuBar", $"确保下拉菜单样式时发生错误: {ex.Message}");
                // 出错时使用备用样式
                ApplyFallbackDropdownStyles(dropdown);
            }
        }

        #endregion
    }
}