using System;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Core;
using BlastingDesign.Utils;
using BlastingDesign.Database;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 数据库连接测试窗口
    /// 基于模态窗口实现，提供数据库连接配置和测试功能
    /// </summary>
    public class DatabaseConnectionWindow : MonoBehaviour
    {
        [Header("Window Settings")]
        [SerializeField] private string windowTitle = "数据库连接测试";
        [SerializeField] private Vector2 windowSize = new Vector2(540, 720);
        [SerializeField] private bool showOnStart = false;

        // UI元素引用
        private ModalWindow modalWindow;
        private VisualTreeAsset uiDocument;
        private VisualElement rootElement;
        private TextField serverField;
        private TextField databaseField;
        private TextField usernameField;
        private TextField passwordField;
        private Toggle encryptToggle;
        private Toggle trustCertificateToggle;
        private Button testButton;
        private Button cancelButton;
        private Label statusLabel;
        private VisualElement loadingIndicator;

        // UI资源路径
        private const string UI_DOCUMENT_PATH = "UI Toolkit/DatabaseConnectionWindow";
        private const string UI_STYLE_PATH = "UI Toolkit/DatabaseConnectionWindow";

        // 数据库管理器
        private DatabaseManager databaseManager;

        // 连接状态
        private bool isConnecting = false;

        #region Unity生命周期

        private void Awake()
        {
            // 初始化数据库管理器
            databaseManager = new DatabaseManager();
        }

        private void Start()
        {
            if (showOnStart)
            {
                ShowWindow();
            }
        }

        private void OnDestroy()
        {
            // 清理资源
            if (modalWindow != null)
            {
                modalWindow.OnWindowClosed -= OnWindowClosed;
            }

            // 清理数据库管理器
            if (databaseManager != null)
            {
                databaseManager.Disconnect();
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 显示数据库连接窗口
        /// </summary>
        public void ShowWindow()
        {
            if (modalWindow == null)
            {
                CreateModalWindow();
            }
            else
            {
                // 如果窗口已存在，将其移到前台
                if (ModalWindowManager.Instance != null)
                {
                    ModalWindowManager.Instance.BringWindowToFront(modalWindow);
                }
            }
        }

        /// <summary>
        /// 隐藏窗口
        /// </summary>
        public void HideWindow()
        {
            if (modalWindow != null)
            {
                modalWindow.CloseWindow();
            }
        }

        #endregion

        #region 窗口创建和初始化

        /// <summary>
        /// 创建模态窗口
        /// </summary>
        private void CreateModalWindow()
        {
            try
            {
                // 通过模态窗口管理器创建窗口
                if (ModalWindowManager.Instance != null)
                {
                    modalWindow = ModalWindowManager.Instance.CreateWindow(windowTitle, windowSize);

                    if (modalWindow != null)
                    {
                        modalWindow.IsDraggable = true;
                        modalWindow.IsClosable = true;

                        // 监听窗口关闭事件
                        modalWindow.OnWindowClosed += OnWindowClosed;

                        // 加载并创建窗口内容
                        LoadUIDocument();

                        Logging.LogInfo("DatabaseConnectionWindow", "数据库连接窗口创建成功");
                    }
                    else
                    {
                        Logging.LogError("DatabaseConnectionWindow", "ModalWindowManager无法创建窗口");
                    }
                }
                else
                {
                    Logging.LogError("DatabaseConnectionWindow", "ModalWindowManager实例不存在");
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("DatabaseConnectionWindow", $"创建模态窗口失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载UI文档
        /// </summary>
        private void LoadUIDocument()
        {
            try
            {
                // 加载UXML文档
                uiDocument = Resources.Load<VisualTreeAsset>(UI_DOCUMENT_PATH);
                if (uiDocument == null)
                {
                    Logging.LogError("DatabaseConnectionWindow", $"无法加载UI文档: {UI_DOCUMENT_PATH}");
                    return;
                }

                // 实例化UI文档
                rootElement = uiDocument.Instantiate();

                // 加载样式表
                var styleSheet = Resources.Load<StyleSheet>(UI_STYLE_PATH);
                if (styleSheet != null)
                {
                    rootElement.styleSheets.Add(styleSheet);
                }
                else
                {
                    Logging.LogWarning("DatabaseConnectionWindow", $"无法加载样式表: {UI_STYLE_PATH}");
                }

                // 设置内容到模态窗口
                modalWindow.SetContent(rootElement);

                // 初始化UI元素引用
                InitializeUIReferences();

                // 设置事件监听
                SetupEventListeners();

                // 加载默认配置
                LoadDefaultConfiguration();
            }
            catch (Exception ex)
            {
                Logging.LogError("DatabaseConnectionWindow", $"加载UI文档失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化UI元素引用
        /// </summary>
        private void InitializeUIReferences()
        {
            try
            {
                // 获取输入字段并添加键盘事件阻断功能
                serverField = rootElement.Q<TextField>("server-field")?.AddFocusBasedKeyboardEventBlocker();
                databaseField = rootElement.Q<TextField>("database-field")?.AddFocusBasedKeyboardEventBlocker();
                usernameField = rootElement.Q<TextField>("username-field")?.AddFocusBasedKeyboardEventBlocker();
                passwordField = rootElement.Q<TextField>("password-field")?.AddFocusBasedKeyboardEventBlocker();

                // 获取开关
                encryptToggle = rootElement.Q<Toggle>("encrypt-toggle");
                trustCertificateToggle = rootElement.Q<Toggle>("trust-certificate-toggle");

                // 获取按钮
                testButton = rootElement.Q<Button>("test-button");
                cancelButton = rootElement.Q<Button>("cancel-button");

                // 获取状态元素
                statusLabel = rootElement.Q<Label>("status-label");
                loadingIndicator = rootElement.Q<VisualElement>("loading-indicator");

                // 验证所有必需的元素都已找到
                if (serverField == null || databaseField == null || usernameField == null ||
                    passwordField == null || encryptToggle == null || trustCertificateToggle == null ||
                    testButton == null || cancelButton == null ||
                    statusLabel == null || loadingIndicator == null)
                {
                    Logging.LogError("DatabaseConnectionWindow", "部分UI元素初始化失败");
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("DatabaseConnectionWindow", $"初始化UI元素引用失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置事件监听
        /// </summary>
        private void SetupEventListeners()
        {
            try
            {
                // 设置按钮点击事件
                if (testButton != null)
                    testButton.clicked += OnTestConnection;

                if (cancelButton != null)
                    cancelButton.clicked += OnCancel;
            }
            catch (Exception ex)
            {
                Logging.LogError("DatabaseConnectionWindow", $"设置事件监听失败: {ex.Message}");
            }
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 加载默认配置
        /// </summary>
        private void LoadDefaultConfiguration()
        {
            // 这里可以从配置文件或PlayerPrefs加载保存的配置
            // 目前使用硬编码的默认值
            Logging.LogInfo("DatabaseConnectionWindow", "加载默认配置");
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        private void SaveConfiguration()
        {
            try
            {
                // 这里可以将配置保存到配置文件或PlayerPrefs
                var config = GetCurrentConfiguration();

                // 示例：保存到PlayerPrefs
                PlayerPrefs.SetString("DB_Server", config.Server);
                PlayerPrefs.SetString("DB_Database", config.Database);
                PlayerPrefs.SetString("DB_Username", config.Username);
                PlayerPrefs.SetString("DB_Password", config.Password);
                PlayerPrefs.SetInt("DB_Encrypt", config.Encrypt ? 1 : 0);
                PlayerPrefs.SetInt("DB_TrustCertificate", config.TrustServerCertificate ? 1 : 0);
                PlayerPrefs.Save();

                UpdateStatus("配置已保存", StatusType.Success);
                Logging.LogInfo("DatabaseConnectionWindow", "数据库配置保存成功");
            }
            catch (Exception ex)
            {
                UpdateStatus($"保存配置失败: {ex.Message}", StatusType.Error);
                Logging.LogError("DatabaseConnectionWindow", $"保存配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前配置
        /// </summary>
        private DatabaseConfiguration GetCurrentConfiguration()
        {
            return new DatabaseConfiguration
            {
                Server = serverField.value,
                Database = databaseField.value,
                Username = usernameField.value,
                Password = passwordField.value,
                Encrypt = encryptToggle.value,
                TrustServerCertificate = trustCertificateToggle.value
            };
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 测试连接按钮点击
        /// </summary>
        private async void OnTestConnection()
        {
            if (isConnecting) return;

            try
            {
                isConnecting = true;
                SetUIEnabled(false);
                UpdateStatus("正在连接数据库...", StatusType.Info);
                ShowLoadingIndicator(true);

                var config = GetCurrentConfiguration();
                bool success = await databaseManager.TestConnectionAsync(config);

                if (success)
                {
                    UpdateStatus("数据库连接成功！", StatusType.Success);
                    Logging.LogInfo("DatabaseConnectionWindow", "数据库连接测试成功");
                }
                else
                {
                    UpdateStatus("数据库连接失败，请检查配置", StatusType.Error);
                    Logging.LogError("DatabaseConnectionWindow", "数据库连接测试失败");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"连接测试异常: {ex.Message}", StatusType.Error);
                Logging.LogError("DatabaseConnectionWindow", $"连接测试异常: {ex.Message}");
            }
            finally
            {
                isConnecting = false;
                SetUIEnabled(true);
                ShowLoadingIndicator(false);
            }
        }

        /// <summary>
        /// 取消按钮点击
        /// </summary>
        private void OnCancel()
        {
            HideWindow();
        }

        /// <summary>
        /// 窗口关闭事件
        /// </summary>
        private void OnWindowClosed(ModalWindow window)
        {
            Logging.LogInfo("DatabaseConnectionWindow", "数据库连接窗口已关闭");

            // 清理窗口引用，以便下次重新创建
            modalWindow = null;

            // 销毁GameObject
            if (gameObject != null)
            {
                Destroy(gameObject);
            }
        }

        #endregion

        #region UI状态管理

        /// <summary>
        /// 设置UI启用状态
        /// </summary>
        private void SetUIEnabled(bool enabled)
        {
            if (serverField != null) serverField.SetEnabled(enabled);
            if (databaseField != null) databaseField.SetEnabled(enabled);
            if (usernameField != null) usernameField.SetEnabled(enabled);
            if (passwordField != null) passwordField.SetEnabled(enabled);
            if (encryptToggle != null) encryptToggle.SetEnabled(enabled);
            if (trustCertificateToggle != null) trustCertificateToggle.SetEnabled(enabled);
            if (testButton != null) testButton.SetEnabled(enabled);
        }

        /// <summary>
        /// 更新状态显示
        /// </summary>
        private void UpdateStatus(string message, StatusType type)
        {
            if (statusLabel != null)
            {
                statusLabel.text = message;

                // 清除之前的样式类
                statusLabel.RemoveFromClassList("status-success");
                statusLabel.RemoveFromClassList("status-error");
                statusLabel.RemoveFromClassList("status-info");

                // 添加对应的样式类
                switch (type)
                {
                    case StatusType.Success:
                        statusLabel.AddToClassList("status-success");
                        break;
                    case StatusType.Error:
                        statusLabel.AddToClassList("status-error");
                        break;
                    case StatusType.Info:
                        statusLabel.AddToClassList("status-info");
                        break;
                }
            }
        }

        /// <summary>
        /// 显示/隐藏加载指示器
        /// </summary>
        private void ShowLoadingIndicator(bool show)
        {
            if (loadingIndicator != null)
            {
                if (show)
                {
                    loadingIndicator.RemoveFromClassList("hidden");
                    loadingIndicator.style.display = DisplayStyle.Flex;
                }
                else
                {
                    loadingIndicator.AddToClassList("hidden");
                    loadingIndicator.style.display = DisplayStyle.None;
                }
            }
        }

        #endregion

        #region 枚举定义

        /// <summary>
        /// 状态类型
        /// </summary>
        private enum StatusType
        {
            Info,
            Success,
            Error
        }

        #endregion
    }
}