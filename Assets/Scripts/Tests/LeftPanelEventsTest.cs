using UnityEngine;
using BlastingDesign.UI.Components;
using BlastingDesign.Data;
using System.Reflection;

namespace BlastingDesign.Tests
{
    /// <summary>
    /// LeftPanel事件处理测试
    /// </summary>
    public class LeftPanelEventsTest : MonoBehaviour
    {
        [Header("测试设置")]
        public bool logTestResults = true;
        
        void Start()
        {
            if (logTestResults)
            {
                TestMethodsExistence();
            }
        }

        /// <summary>
        /// 测试所有需要的方法是否存在
        /// </summary>
        void TestMethodsExistence()
        {
            var leftPanelType = typeof(LeftPanel);
            var bindingFlags = BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Public;

            // 需要检查的方法列表
            string[] requiredMethods = {
                "RefreshData",
                "GetBlastingElementFromTreeData",
                "ConvertToHierarchyItem",
                "ToggleElementVisibility",
                "EditElement",
                "RenameElement",
                "DeleteElement",
                "ShowElementProperties",
                "CalculateElement",
                "ExportElement",
                "HandleCustomAction",
                "UpdateStatusDisplay"
            };

            int existingMethods = 0;
            int totalMethods = requiredMethods.Length;

            foreach (var methodName in requiredMethods)
            {
                var method = leftPanelType.GetMethod(methodName, bindingFlags);
                if (method != null)
                {
                    existingMethods++;
                    if (logTestResults)
                    {
                        Debug.Log($"[LeftPanelEventsTest] ✅ 方法 {methodName} 存在");
                    }
                }
                else
                {
                    if (logTestResults)
                    {
                        Debug.LogWarning($"[LeftPanelEventsTest] ❌ 方法 {methodName} 不存在");
                    }
                }
            }

            if (logTestResults)
            {
                Debug.Log($"[LeftPanelEventsTest] 方法存在性测试完成: {existingMethods}/{totalMethods} 个方法存在");
                
                if (existingMethods == totalMethods)
                {
                    Debug.Log("[LeftPanelEventsTest] ✅ 所有必需方法都存在，Events.cs应该能正常编译");
                }
                else
                {
                    Debug.LogWarning("[LeftPanelEventsTest] ⚠️ 某些方法缺失，可能存在编译错误");
                }
            }
        }
    }
}