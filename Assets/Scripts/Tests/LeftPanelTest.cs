using UnityEngine;
using BlastingDesign.UI.Components;
using BlastingDesign.Data;

namespace BlastingDesign.Tests
{
    /// <summary>
    /// LeftPanel重构测试脚本
    /// 用于验证重构后的LeftPanel组件是否正常工作
    /// </summary>
    public class LeftPanelTest : MonoBehaviour
    {
        [Header("测试设置")]
        public bool autoCreateTestData = true;
        public bool logTestResults = true;
        
        private LeftPanel leftPanel;

        void Start()
        {
            if (autoCreateTestData)
            {
                TestLeftPanelCreation();
                TestDataLoading();
            }
        }

        /// <summary>
        /// 测试LeftPanel创建
        /// </summary>
        void TestLeftPanelCreation()
        {
            try
            {
                // 创建LeftPanel实例
                leftPanel = new LeftPanel();
                
                // 尝试初始化（可能会失败，因为在测试环境中没有UI上下文）
                try
                {
                    leftPanel.Initialize();
                    
                    if (logTestResults)
                    {
                        Debug.Log("[LeftPanelTest] ✅ LeftPanel创建和初始化成功");
                    }
                }
                catch (System.Exception initEx)
                {
                    if (logTestResults)
                    {
                        Debug.LogWarning($"[LeftPanelTest] ⚠️ LeftPanel创建成功，但初始化失败（这在测试环境中是正常的）: {initEx.Message}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                if (logTestResults)
                {
                    Debug.LogError($"[LeftPanelTest] ❌ LeftPanel创建失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 测试数据加载
        /// </summary>
        void TestDataLoading()
        {
            if (leftPanel == null) return;

            try
            {
                // 创建测试项目数据
                var testProject = CreateTestProject();
                
                // 设置项目数据
                leftPanel.CurrentProject = testProject;
                
                if (logTestResults)
                {
                    Debug.Log("[LeftPanelTest] ✅ 测试数据加载成功");
                    Debug.Log($"[LeftPanelTest] 项目名称: {testProject.name}");
                    Debug.Log($"[LeftPanelTest] 爆区数量: {testProject.blastingAreas.Count}");
                }
            }
            catch (System.Exception ex)
            {
                if (logTestResults)
                {
                    Debug.LogError($"[LeftPanelTest] ❌ 数据加载失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 创建测试项目数据
        /// </summary>
        private ProjectData CreateTestProject()
        {
            var project = new ProjectData("test_project", "测试爆破工程")
            {
                description = "用于验证LeftPanel重构的测试项目",
                createdBy = "LeftPanelTest"
            };

            // 创建测试爆区
            var area = new BlastingAreaData("test_area", "测试爆区")
            {
                area = 1000f,
                rockType = "测试岩石"
            };

            // 添加测试测量点
            area.measurePoints.Add(new MeasurePointData("mp_test", "测试测量点") 
            { 
                elevation = 50f, 
                pointType = MeasurePointType.Control 
            });

            // 添加测试边界线
            area.boundaryLines.Add(new BoundaryLineData("bl_test", "测试边界线") 
            { 
                boundaryType = BoundaryType.Excavation,
                length = 100f 
            });

            // 添加测试钻孔
            area.drillHoles.Add(new DrillHoleData("dh_test", "测试钻孔") 
            { 
                depth = 10f, 
                diameter = 0.1f, 
                holeType = DrillHoleType.Production 
            });

            // 添加测试爆破块体
            area.blastBlocks.Add(new BlastBlockData("bb_test", "测试爆破块体") 
            { 
                volume = 100f, 
                sequence = 1 
            });

            project.blastingAreas.Add(area);
            return project;
        }

        /// <summary>
        /// 清理测试资源
        /// </summary>
        void OnDestroy()
        {
            leftPanel?.Cleanup();
        }
    }
}