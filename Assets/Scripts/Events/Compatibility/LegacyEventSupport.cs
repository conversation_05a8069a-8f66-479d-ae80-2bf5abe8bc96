using System;
using BlastingDesign.Events.Core;
using BlastingDesign.UI.Core;
using UnityEngine;

namespace BlastingDesign.Events.Compatibility
{
    /// <summary>
    /// 旧事件系统兼容性支持
    /// 提供静态方法以保持现有API的兼容性
    /// </summary>
    [System.Obsolete("Use IEventBus.Publish instead. This class is for backward compatibility only.")]
    public static class UIEventSystemCompat
    {
        private static IEventBus _eventBus;
        private static bool _isInitialized = false;
        
        /// <summary>
        /// 初始化兼容性支持
        /// </summary>
        /// <param name="eventBus">新的事件总线实例</param>
        public static void Initialize(IEventBus eventBus)
        {
            _eventBus = eventBus ?? throw new ArgumentNullException(nameof(eventBus));
            _isInitialized = true;
            
            Debug.Log("UIEventSystemCompat: 兼容性支持已初始化");
        }
        
        /// <summary>
        /// 检查是否已初始化
        /// </summary>
        private static void EnsureInitialized()
        {
            if (!_isInitialized || _eventBus == null)
            {
                Debug.LogError("UIEventSystemCompat: 未初始化或事件总线为空。请先调用Initialize方法。");
                return;
            }
        }
        
        #region 兼容性API方法
        
        /// <summary>
        /// 触发菜单项点击事件
        /// </summary>
        /// <param name="menuItem">菜单项名称</param>
        [System.Obsolete("Use eventBus.Publish(new MenuItemClickedEvent(menuItem)) instead")]
        public static void TriggerMenuItemClicked(string menuItem)
        {
            EnsureInitialized();
            if (_eventBus == null) return;
            
            var evt = new MenuItemClickedEvent(menuItem);
            _eventBus.Publish(evt);
            
            Debug.Log($"UIEventSystemCompat: 触发菜单项点击事件 - {menuItem}");
        }
        
        /// <summary>
        /// 触发工具选择事件
        /// </summary>
        /// <param name="tool">工具名称</param>
        [System.Obsolete("Use eventBus.Publish(new ToolSelectedEvent(tool)) instead")]
        public static void TriggerToolSelected(string tool)
        {
            EnsureInitialized();
            if (_eventBus == null) return;
            
            var evt = new ToolSelectedEvent(tool);
            _eventBus.Publish(evt);
            
            Debug.Log($"UIEventSystemCompat: 触发工具选择事件 - {tool}");
        }
        
        /// <summary>
        /// 触发对象选择事件
        /// </summary>
        /// <param name="selectedObject">选中的对象</param>
        [System.Obsolete("Use eventBus.Publish(new ObjectSelectedEvent(selectedObject)) instead")]
        public static void TriggerObjectSelected(object selectedObject)
        {
            EnsureInitialized();
            if (_eventBus == null) return;
            
            var evt = new ObjectSelectedEvent(selectedObject);
            _eventBus.Publish(evt);
            
            Debug.Log($"UIEventSystemCompat: 触发对象选择事件 - {selectedObject}");
        }
        
        /// <summary>
        /// 触发状态消息事件
        /// </summary>
        /// <param name="message">状态消息</param>
        [System.Obsolete("Use eventBus.Publish(new StatusMessageEvent(message)) instead")]
        public static void TriggerStatusMessage(string message)
        {
            EnsureInitialized();
            if (_eventBus == null) return;
            
            var evt = new StatusMessageEvent(message);
            _eventBus.Publish(evt);
            
            Debug.Log($"UIEventSystemCompat: 触发状态消息事件 - {message}");
        }
        
        /// <summary>
        /// 触发面板显示事件
        /// </summary>
        /// <param name="panelName">面板名称</param>
        [System.Obsolete("Use eventBus.Publish(new PanelEvent(panelName, PanelAction.Show)) instead")]
        public static void TriggerPanelShown(string panelName)
        {
            EnsureInitialized();
            if (_eventBus == null) return;
            
            var evt = new PanelEvent(panelName, PanelAction.Show);
            _eventBus.Publish(evt);
            
            Debug.Log($"UIEventSystemCompat: 触发面板显示事件 - {panelName}");
        }
        
        /// <summary>
        /// 触发面板隐藏事件
        /// </summary>
        /// <param name="panelName">面板名称</param>
        [System.Obsolete("Use eventBus.Publish(new PanelEvent(panelName, PanelAction.Hide)) instead")]
        public static void TriggerPanelHidden(string panelName)
        {
            EnsureInitialized();
            if (_eventBus == null) return;
            
            var evt = new PanelEvent(panelName, PanelAction.Hide);
            _eventBus.Publish(evt);
            
            Debug.Log($"UIEventSystemCompat: 触发面板隐藏事件 - {panelName}");
        }
        
        /// <summary>
        /// 触发面板切换事件
        /// </summary>
        /// <param name="panelName">面板名称</param>
        [System.Obsolete("Use eventBus.Publish(new PanelEvent(panelName, PanelAction.Toggle)) instead")]
        public static void TriggerPanelToggled(string panelName)
        {
            EnsureInitialized();
            if (_eventBus == null) return;
            
            var evt = new PanelEvent(panelName, PanelAction.Toggle);
            _eventBus.Publish(evt);
            
            Debug.Log($"UIEventSystemCompat: 触发面板切换事件 - {panelName}");
        }
        
        /// <summary>
        /// 触发面板聚焦事件
        /// </summary>
        /// <param name="panelName">面板名称</param>
        [System.Obsolete("Use eventBus.Publish(new PanelEvent(panelName, PanelAction.Focus)) instead")]
        public static void TriggerPanelFocused(string panelName)
        {
            EnsureInitialized();
            if (_eventBus == null) return;
            
            var evt = new PanelEvent(panelName, PanelAction.Focus);
            _eventBus.Publish(evt);
            
            Debug.Log($"UIEventSystemCompat: 触发面板聚焦事件 - {panelName}");
        }
        
        /// <summary>
        /// 触发自定义事件
        /// </summary>
        /// <param name="eventName">事件名称</param>
        /// <param name="data">事件数据</param>
        [System.Obsolete("Use eventBus.Publish(new CustomEvent(eventName, data)) instead")]
        public static void TriggerCustomEvent(string eventName, object data = null)
        {
            EnsureInitialized();
            if (_eventBus == null) return;
            
            var evt = new CustomEvent(eventName, data);
            _eventBus.Publish(evt);
            
            Debug.Log($"UIEventSystemCompat: 触发自定义事件 - {eventName}");
        }
        
        #endregion
        
        #region 订阅兼容性方法
        
        /// <summary>
        /// 订阅菜单项点击事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        [System.Obsolete("Use eventBus.Subscribe<MenuItemClickedEvent>(handler) instead")]
        public static IEventSubscription SubscribeMenuItemClicked(Action<string> handler)
        {
            EnsureInitialized();
            if (_eventBus == null) return null;
            
            return _eventBus.Subscribe<MenuItemClickedEvent>(evt => handler(evt.MenuItem));
        }
        
        /// <summary>
        /// 订阅工具选择事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        [System.Obsolete("Use eventBus.Subscribe<ToolSelectedEvent>(handler) instead")]
        public static IEventSubscription SubscribeToolSelected(Action<string> handler)
        {
            EnsureInitialized();
            if (_eventBus == null) return null;
            
            return _eventBus.Subscribe<ToolSelectedEvent>(evt => handler(evt.ToolName));
        }
        
        /// <summary>
        /// 订阅对象选择事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        [System.Obsolete("Use eventBus.Subscribe<ObjectSelectedEvent>(handler) instead")]
        public static IEventSubscription SubscribeObjectSelected(Action<object> handler)
        {
            EnsureInitialized();
            if (_eventBus == null) return null;
            
            return _eventBus.Subscribe<ObjectSelectedEvent>(evt => handler(evt.SelectedObject));
        }
        
        /// <summary>
        /// 订阅状态消息事件
        /// </summary>
        /// <param name="handler">事件处理器</param>
        [System.Obsolete("Use eventBus.Subscribe<StatusMessageEvent>(handler) instead")]
        public static IEventSubscription SubscribeStatusMessage(Action<string> handler)
        {
            EnsureInitialized();
            if (_eventBus == null) return null;
            
            return _eventBus.Subscribe<StatusMessageEvent>(evt => handler(evt.Message));
        }
        
        #endregion
    }
}
