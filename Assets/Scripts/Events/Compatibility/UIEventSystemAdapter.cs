using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using BlastingDesign.Events.Core;
using BlastingDesign.UI.Core;
using UnityEngine;

namespace BlastingDesign.Events.Compatibility
{
    /// <summary>
    /// UIEventSystem适配器
    /// 提供新旧事件系统之间的兼容性桥梁
    /// </summary>
    public class UIEventSystemAdapter : IEventBus
    {
        private readonly UIEventSystem _legacySystem;
        private readonly IEventBus _newSystem;
        private readonly bool _enableDebugLogging;

        // 事件类型映射
        private readonly Dictionary<Type, Func<IEvent, bool>> _legacyEventHandlers;

        public bool DebugMode
        {
            get => _newSystem.DebugMode;
            set => _newSystem.DebugMode = value;
        }

        public UIEventSystemAdapter(UIEventSystem legacySystem, IEventBus newSystem, bool enableDebugLogging = false)
        {
            if (legacySystem == null) throw new ArgumentNullException(nameof(legacySystem));
            if (newSystem == null) throw new ArgumentNullException(nameof(newSystem));

            _legacySystem = legacySystem;
            _newSystem = newSystem;
            _enableDebugLogging = enableDebugLogging;

            _legacyEventHandlers = new Dictionary<Type, Func<IEvent, bool>>();
            InitializeLegacyEventHandlers();
        }

        private void InitializeLegacyEventHandlers()
        {
            // 菜单项点击事件映射
            _legacyEventHandlers[typeof(MenuItemClickedEvent)] = (evt) =>
            {
                if (evt is MenuItemClickedEvent menuEvent)
                {
                    _legacySystem.Toolbar.OnMenuItemClicked?.Invoke(menuEvent.MenuItem);
                    return true;
                }
                return false;
            };

            // 工具选择事件映射
            _legacyEventHandlers[typeof(ToolSelectedEvent)] = (evt) =>
            {
                if (evt is ToolSelectedEvent toolEvent)
                {
                    _legacySystem.Toolbar.OnToolSelected?.Invoke(toolEvent.ToolName);
                    return true;
                }
                return false;
            };

            // 对象选择事件映射
            _legacyEventHandlers[typeof(ObjectSelectedEvent)] = (evt) =>
            {
                if (evt is ObjectSelectedEvent selectionEvent)
                {
                    _legacySystem.Selection.OnObjectSelected?.Invoke(selectionEvent.SelectedObject);
                    return true;
                }
                return false;
            };

            // 状态消息事件映射
            _legacyEventHandlers[typeof(StatusMessageEvent)] = (evt) =>
            {
                if (evt is StatusMessageEvent statusEvent)
                {
                    _legacySystem.Status.OnStatusMessageChanged?.Invoke(statusEvent.Message);
                    return true;
                }
                return false;
            };

            // 面板事件映射
            _legacyEventHandlers[typeof(PanelEvent)] = (evt) =>
            {
                if (evt is PanelEvent panelEvent)
                {
                    // 由于旧系统可能没有这些面板事件，这里只做日志记录
                    if (_enableDebugLogging)
                    {
                        Debug.Log($"UIEventSystemAdapter: 面板事件 {panelEvent.Action} - {panelEvent.PanelName}");
                    }
                    return true;
                }
                return false;
            };

            // 自定义事件映射
            _legacyEventHandlers[typeof(CustomEvent)] = (evt) =>
            {
                if (evt is CustomEvent customEvent)
                {
                    _legacySystem.Custom.OnCustomEvent?.Invoke(customEvent.EventName, customEvent.Data);
                    return true;
                }
                return false;
            };
        }

        #region IEventBus实现 - 委托给新系统

        public IEventSubscription Subscribe<T>(Action<T> handler) where T : IEvent
        {
            return _newSystem.Subscribe(handler);
        }

        public void Unsubscribe<T>(Action<T> handler) where T : IEvent
        {
            _newSystem.Unsubscribe(handler);
        }

        public void Unsubscribe(IEventSubscription subscription)
        {
            _newSystem.Unsubscribe(subscription);
        }

        public IEventSubscription Subscribe<T>(Action<T> handler, Func<T, bool> filter) where T : IEvent
        {
            return _newSystem.Subscribe(handler, filter);
        }

        public IEventSubscription Subscribe<T>(Action<T> handler, int priority) where T : IEvent
        {
            return _newSystem.Subscribe(handler, priority);
        }

        public IEventSubscription Subscribe<T>(Action<T> handler, Func<T, bool> filter, int priority) where T : IEvent
        {
            return _newSystem.Subscribe(handler, filter, priority);
        }

        public void Publish<T>(T eventData) where T : IEvent
        {
            // 首先尝试通过新系统发布
            _newSystem.Publish(eventData);

            // 然后检查是否需要通过旧系统发布
            if (IsLegacyEventType<T>())
            {
                PublishToLegacySystem(eventData);
            }
        }

        public Task PublishAsync<T>(T eventData) where T : IEvent
        {
            return _newSystem.PublishAsync(eventData);
        }

        public void PublishDelayed<T>(T eventData, float delay) where T : IEvent
        {
            _newSystem.PublishDelayed(eventData, delay);
        }

        public void PublishBatch<T>(IEnumerable<T> events) where T : IEvent
        {
            _newSystem.PublishBatch(events);
        }

        public int GetSubscriberCount<T>() where T : IEvent
        {
            return _newSystem.GetSubscriberCount<T>();
        }

        public bool HasSubscribers<T>() where T : IEvent
        {
            return _newSystem.HasSubscribers<T>();
        }

        public void ClearAllSubscriptions()
        {
            _newSystem.ClearAllSubscriptions();
        }

        public void ClearSubscriptions<T>() where T : IEvent
        {
            _newSystem.ClearSubscriptions<T>();
        }

        public IEventStatistics GetStatistics()
        {
            return _newSystem.GetStatistics();
        }

        #endregion

        #region 兼容性方法

        /// <summary>
        /// 检查是否为旧系统事件类型
        /// </summary>
        private bool IsLegacyEventType<T>() where T : IEvent
        {
            return _legacyEventHandlers.ContainsKey(typeof(T));
        }

        /// <summary>
        /// 向旧系统发布事件
        /// </summary>
        private void PublishToLegacySystem<T>(T eventData) where T : IEvent
        {
            Type eventType = typeof(T);

            if (_legacyEventHandlers.TryGetValue(eventType, out Func<IEvent, bool> handler))
            {
                try
                {
                    bool handled = handler(eventData);

                    if (_enableDebugLogging)
                    {
                        Debug.Log($"UIEventSystemAdapter: 向旧系统发布事件 {eventType.Name}，处理结果: {handled}");
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"UIEventSystemAdapter: 向旧系统发布事件 {eventType.Name} 时发生错误: {ex.Message}");
                }
            }
        }

        #endregion

        public void Dispose()
        {
            _newSystem?.Dispose();
        }
    }
}
