using System;
using UnityEngine;
using BlastingDesign.Events.Core;
using BlastingDesign.Events.Management;
using BlastingDesign.Core.Input;

namespace BlastingDesign.Events.Handlers
{
    /// <summary>
    /// 输入事件处理器
    /// 集成现有的InputEventPriorityManager到新事件系统
    /// </summary>
    public class InputEventHandler : IDisposable
    {
        private readonly IEventBus _eventBus;
        private readonly InputEventPriorityManager _priorityManager;
        private readonly EventFilterManager _filterManager;
        private readonly bool _enableDebugLogging;
        
        // 事件订阅句柄
        private IEventSubscription _inputEventSubscription;
        private IEventSubscription _mouseEventSubscription;
        private IEventSubscription _keyboardEventSubscription;
        
        public InputEventHandler(IEventBus eventBus, InputEventPriorityManager priorityManager, 
                               EventFilterManager filterManager = null, bool enableDebugLogging = false)
        {
            _eventBus = eventBus ?? throw new ArgumentNullException(nameof(eventBus));
            _priorityManager = priorityManager ?? throw new ArgumentNullException(nameof(priorityManager));
            _filterManager = filterManager;
            _enableDebugLogging = enableDebugLogging;
            
            InitializeEventSubscriptions();
            SetupInputFilters();
        }
        
        private void InitializeEventSubscriptions()
        {
            // 订阅通用输入事件
            _inputEventSubscription = _eventBus.Subscribe<InputEvent>(HandleInputEvent, 100); // 高优先级
            
            // 订阅鼠标事件
            _mouseEventSubscription = _eventBus.Subscribe<MouseInputEvent>(HandleMouseEvent, 90);
            
            // 订阅键盘事件
            _keyboardEventSubscription = _eventBus.Subscribe<KeyboardInputEvent>(HandleKeyboardEvent, 90);
            
            if (_enableDebugLogging)
            {
                Debug.Log("InputEventHandler: 输入事件订阅初始化完成");
            }
        }
        
        private void SetupInputFilters()
        {
            if (_filterManager == null) return;
            
            // 添加UI优先级过滤器
            _filterManager.AddFilter<InputEvent>(evt => 
            {
                if (_priorityManager == null) return true;
                
                // 检查事件优先级
                var priority = _priorityManager.GetEventPriority(evt.Position);
                
                // 如果是UI事件且应该阻断场景事件，则过滤掉场景相关的输入事件
                if (priority == InputEventPriorityManager.EventPriority.UI_Event)
                {
                    return !ShouldBlockSceneInput(evt);
                }
                
                return true;
            }, 100, "UI Priority Filter");
            
            // 添加拖拽持续性过滤器
            _filterManager.AddFilter<MouseInputEvent>(evt =>
            {
                if (_priorityManager == null) return true;
                
                // 检查拖拽持续性
                return !_priorityManager.ShouldBlockSceneEvents(evt.Position);
            }, 90, "Drag Continuity Filter");
            
            if (_enableDebugLogging)
            {
                Debug.Log("InputEventHandler: 输入过滤器设置完成");
            }
        }
        
        private void HandleInputEvent(InputEvent inputEvent)
        {
            if (inputEvent == null) return;
            
            try
            {
                // 应用过滤器
                if (_filterManager != null && !_filterManager.ShouldProcess(inputEvent))
                {
                    if (_enableDebugLogging)
                    {
                        Debug.Log($"InputEventHandler: 输入事件被过滤器阻止 - {inputEvent.InputType}");
                    }
                    return;
                }
                
                // 处理不同类型的输入事件
                switch (inputEvent.InputType)
                {
                    case InputEventType.Mouse:
                        ProcessMouseInput(inputEvent);
                        break;
                    case InputEventType.Keyboard:
                        ProcessKeyboardInput(inputEvent);
                        break;
                    case InputEventType.Touch:
                        ProcessTouchInput(inputEvent);
                        break;
                    case InputEventType.Gamepad:
                        ProcessGamepadInput(inputEvent);
                        break;
                }
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"InputEventHandler: 处理输入事件 - {inputEvent.InputType} at {inputEvent.Position}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"InputEventHandler: 处理输入事件时发生错误: {ex.Message}");
            }
        }
        
        private void HandleMouseEvent(MouseInputEvent mouseEvent)
        {
            if (mouseEvent == null) return;
            
            try
            {
                // 检查事件优先级
                if (_priorityManager != null)
                {
                    var priority = _priorityManager.GetEventPriority(mouseEvent.Position);
                    
                    // 根据优先级决定是否处理
                    if (priority == InputEventPriorityManager.EventPriority.UI_Event && 
                        ShouldBlockSceneInput(mouseEvent))
                    {
                        if (_enableDebugLogging)
                        {
                            Debug.Log($"InputEventHandler: 鼠标事件被UI优先级阻止 - {mouseEvent.Button}");
                        }
                        return;
                    }
                }
                
                // 处理鼠标事件
                ProcessMouseEvent(mouseEvent);
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"InputEventHandler: 处理鼠标事件 - {mouseEvent.Button} at {mouseEvent.Position}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"InputEventHandler: 处理鼠标事件时发生错误: {ex.Message}");
            }
        }
        
        private void HandleKeyboardEvent(KeyboardInputEvent keyboardEvent)
        {
            if (keyboardEvent == null) return;
            
            try
            {
                // 键盘事件通常不需要位置检查，但可以添加其他过滤逻辑
                ProcessKeyboardEvent(keyboardEvent);
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"InputEventHandler: 处理键盘事件 - {keyboardEvent.KeyCode}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"InputEventHandler: 处理键盘事件时发生错误: {ex.Message}");
            }
        }
        
        #region 具体输入处理方法
        
        private void ProcessMouseInput(InputEvent inputEvent)
        {
            // 发布更具体的鼠标事件
            var mouseEvent = new MouseInputEvent(
                inputEvent.Position,
                MouseButton.Left, // 默认左键，实际应该从输入事件中获取
                inputEvent.IsPressed,
                inputEvent.IsReleased,
                inputEvent.Source
            );
            
            _eventBus.Publish(mouseEvent);
        }
        
        private void ProcessKeyboardInput(InputEvent inputEvent)
        {
            // 发布更具体的键盘事件
            var keyboardEvent = new KeyboardInputEvent(
                inputEvent.KeyCode,
                inputEvent.IsPressed,
                inputEvent.IsReleased,
                inputEvent.Source
            );
            
            _eventBus.Publish(keyboardEvent);
        }
        
        private void ProcessTouchInput(InputEvent inputEvent)
        {
            // 处理触摸输入
            // 可以发布触摸相关的事件
            if (_enableDebugLogging)
            {
                Debug.Log($"InputEventHandler: 处理触摸输入 at {inputEvent.Position}");
            }
        }
        
        private void ProcessGamepadInput(InputEvent inputEvent)
        {
            // 处理手柄输入
            // 可以发布手柄相关的事件
            if (_enableDebugLogging)
            {
                Debug.Log($"InputEventHandler: 处理手柄输入 - {inputEvent.KeyCode}");
            }
        }
        
        private void ProcessMouseEvent(MouseInputEvent mouseEvent)
        {
            // 处理具体的鼠标事件逻辑
            // 可以根据按钮类型、状态等进行不同处理
            
            switch (mouseEvent.Button)
            {
                case MouseButton.Left:
                    ProcessLeftMouseButton(mouseEvent);
                    break;
                case MouseButton.Right:
                    ProcessRightMouseButton(mouseEvent);
                    break;
                case MouseButton.Middle:
                    ProcessMiddleMouseButton(mouseEvent);
                    break;
            }
        }
        
        private void ProcessKeyboardEvent(KeyboardInputEvent keyboardEvent)
        {
            // 处理具体的键盘事件逻辑
            // 可以根据按键类型、修饰键等进行不同处理
            
            if (keyboardEvent.IsPressed)
            {
                ProcessKeyPress(keyboardEvent.KeyCode);
            }
            else if (keyboardEvent.IsReleased)
            {
                ProcessKeyRelease(keyboardEvent.KeyCode);
            }
        }
        
        #endregion
        
        #region 辅助方法
        
        private bool ShouldBlockSceneInput(IEvent inputEvent)
        {
            // 判断是否应该阻断场景输入
            // 这里可以添加更复杂的逻辑
            return false; // 默认不阻断
        }
        
        private void ProcessLeftMouseButton(MouseInputEvent mouseEvent)
        {
            // 处理左键点击
            if (_enableDebugLogging)
            {
                Debug.Log($"InputEventHandler: 左键 {(mouseEvent.IsPressed ? "按下" : "释放")} at {mouseEvent.Position}");
            }
        }
        
        private void ProcessRightMouseButton(MouseInputEvent mouseEvent)
        {
            // 处理右键点击
            if (_enableDebugLogging)
            {
                Debug.Log($"InputEventHandler: 右键 {(mouseEvent.IsPressed ? "按下" : "释放")} at {mouseEvent.Position}");
            }
        }
        
        private void ProcessMiddleMouseButton(MouseInputEvent mouseEvent)
        {
            // 处理中键点击
            if (_enableDebugLogging)
            {
                Debug.Log($"InputEventHandler: 中键 {(mouseEvent.IsPressed ? "按下" : "释放")} at {mouseEvent.Position}");
            }
        }
        
        private void ProcessKeyPress(KeyCode keyCode)
        {
            // 处理按键按下
            if (_enableDebugLogging)
            {
                Debug.Log($"InputEventHandler: 按键按下 - {keyCode}");
            }
        }
        
        private void ProcessKeyRelease(KeyCode keyCode)
        {
            // 处理按键释放
            if (_enableDebugLogging)
            {
                Debug.Log($"InputEventHandler: 按键释放 - {keyCode}");
            }
        }
        
        #endregion
        
        public void Dispose()
        {
            _inputEventSubscription?.Dispose();
            _mouseEventSubscription?.Dispose();
            _keyboardEventSubscription?.Dispose();
            
            if (_enableDebugLogging)
            {
                Debug.Log("InputEventHandler: 已释放资源");
            }
        }
    }
}
