using UnityEngine;
using BlastingDesign.Events.Core;
using BlastingDesign.Events.Compatibility;

namespace BlastingDesign.Events
{
    /// <summary>
    /// 迁移测试脚本
    /// 用于测试新旧事件系统的兼容性和功能
    /// </summary>
    public class MigrationTestScript : MonoBehaviour
    {
        [Header("测试配置")]
        [SerializeField] private bool runTestsOnStart = true;
        [SerializeField] private bool showDetailedLogs = true;
        [SerializeField] private float testInterval = 2f;

        private IEventSubscription menuSubscription;
        private IEventSubscription toolSubscription;
        private IEventSubscription statusSubscription;
        private IEventSubscription objectSubscription;

        private void Start()
        {
            if (runTestsOnStart)
            {
                // 延迟执行测试，确保事件系统完全初始化
                Invoke(nameof(RunMigrationTests), 1f);
            }
        }

        private void RunMigrationTests()
        {
            LogTest("开始事件系统迁移测试");

            // 测试1：验证事件系统初始化
            TestEventSystemInitialization();

            // 测试2：设置事件订阅
            SetupEventSubscriptions();

            // 测试3：测试新事件系统
            Invoke(nameof(TestNewEventSystem), testInterval);

            // 测试4：测试兼容性API
            Invoke(nameof(TestCompatibilityAPI), testInterval * 2);

            // 测试5：测试高级功能
            Invoke(nameof(TestAdvancedFeatures), testInterval * 3);

            // 测试6：性能测试
            Invoke(nameof(TestPerformance), testInterval * 4);
        }

        private void TestEventSystemInitialization()
        {
            LogTest("=== 测试1：事件系统初始化 ===");

            // 检查EventSystemManager
            if (EventSystemManager.Instance == null)
            {
                LogError("EventSystemManager未初始化！");
                return;
            }
            LogSuccess("EventSystemManager已初始化");

            // 检查事件总线
            if (EventSystemManager.Instance.EventBus == null)
            {
                LogError("事件总线未初始化！");
                return;
            }
            LogSuccess("事件总线已初始化");

            // 检查统计信息
            var stats = EventSystemManager.Instance.EventBus.GetStatistics();
            LogInfo($"事件系统统计: 活跃事件类型 {stats.ActiveEventTypes}, 总订阅数 {stats.TotalSubscriptions}");

            // 检查事件类型注册表
            if (EventSystemManager.Instance.EventTypeRegistry != null)
            {
                var registryStats = EventSystemManager.Instance.EventTypeRegistry.GetStatistics();
                LogInfo($"事件类型注册表: 总计 {registryStats.TotalRegisteredTypes} 个类型");
            }
        }

        private void SetupEventSubscriptions()
        {
            LogTest("=== 测试2：设置事件订阅 ===");

            if (EventSystemManager.Instance == null || EventSystemManager.Instance.EventBus == null)
            {
                LogError("无法设置事件订阅，事件总线未初始化");
                return;
            }

            var eventBus = EventSystemManager.Instance.EventBus;

            // 订阅各种事件
            menuSubscription = eventBus.Subscribe<MenuItemClickedEvent>(OnMenuItemClicked);
            toolSubscription = eventBus.Subscribe<ToolSelectedEvent>(OnToolSelected);
            statusSubscription = eventBus.Subscribe<StatusMessageEvent>(OnStatusMessage);
            objectSubscription = eventBus.Subscribe<ObjectSelectedEvent>(OnObjectSelected);

            LogSuccess("事件订阅设置完成");
        }

        private void TestNewEventSystem()
        {
            LogTest("=== 测试3：新事件系统功能 ===");

            if (EventSystemManager.Instance == null || EventSystemManager.Instance.EventBus == null)
            {
                LogError("新事件系统未初始化");
                return;
            }

            var eventBus = EventSystemManager.Instance.EventBus;

            // 发布各种事件
            eventBus.Publish(new MenuItemClickedEvent("Test.NewSystem.Menu"));
            eventBus.Publish(new ToolSelectedEvent("TestTool"));
            eventBus.Publish(new StatusMessageEvent("新事件系统测试消息", StatusMessageType.Info));
            eventBus.Publish(new ObjectSelectedEvent("TestObject"));

            LogSuccess("新事件系统测试完成");
        }

        private void TestCompatibilityAPI()
        {
            LogTest("=== 测试4：兼容性API ===");

            try
            {
                // 使用兼容性API（测试目的，抑制过时警告）
#pragma warning disable CS0618
                UIEventSystemCompat.TriggerMenuItemClicked("Test.Compatibility.Menu");
                UIEventSystemCompat.TriggerToolSelected("CompatibilityTool");
                UIEventSystemCompat.TriggerStatusMessage("兼容性API测试消息");
                UIEventSystemCompat.TriggerObjectSelected("CompatibilityObject");
#pragma warning restore CS0618

                LogSuccess("兼容性API测试完成");
            }
            catch (System.Exception ex)
            {
                LogError($"兼容性API测试失败: {ex.Message}");
            }
        }

        private void TestAdvancedFeatures()
        {
            LogTest("=== 测试5：高级功能 ===");

            if (EventSystemManager.Instance == null || EventSystemManager.Instance.EventBus == null)
            {
                LogError("无法测试高级功能，事件总线未初始化");
                return;
            }

            var eventBus = EventSystemManager.Instance.EventBus;

            // 测试条件订阅
            var conditionalSub = eventBus.Subscribe<MenuItemClickedEvent>(
                evt => LogInfo($"条件订阅接收到: {evt.MenuItem}"),
                evt => evt.MenuItem.StartsWith("Test.")
            );

            // 测试优先级订阅
            var highPrioritySub = eventBus.Subscribe<StatusMessageEvent>(
                evt => LogInfo($"高优先级处理: {evt.Message}"),
                100
            );

            // 发布测试事件
            eventBus.Publish(new MenuItemClickedEvent("Test.Advanced"));
            eventBus.Publish(new MenuItemClickedEvent("Other.Advanced"));
            eventBus.Publish(new StatusMessageEvent("优先级测试", StatusMessageType.Info));

            // 测试延迟发布
            eventBus.PublishDelayed(new StatusMessageEvent("延迟消息", StatusMessageType.Info), 1f);

            // 清理测试订阅
            conditionalSub?.Dispose();
            highPrioritySub?.Dispose();

            LogSuccess("高级功能测试完成");
        }

        private void TestPerformance()
        {
            LogTest("=== 测试6：性能测试 ===");

            if (EventSystemManager.Instance == null || EventSystemManager.Instance.EventBus == null)
            {
                LogError("无法进行性能测试，事件总线未初始化");
                return;
            }

            var eventBus = EventSystemManager.Instance.EventBus;

            // 性能测试：快速发布大量事件
            var startTime = Time.realtimeSinceStartup;

            for (int i = 0; i < 1000; i++)
            {
                eventBus.Publish(new MenuItemClickedEvent($"Performance.Test.{i}"));
            }

            var endTime = Time.realtimeSinceStartup;
            var duration = (endTime - startTime) * 1000f; // 转换为毫秒

            LogInfo($"性能测试结果: 发布1000个事件耗时 {duration:F2}ms");

            // 显示性能监控信息
            if (EventSystemManager.Instance.PerformanceMonitor != null)
            {
                var perfStats = EventSystemManager.Instance.PerformanceMonitor.GetOverallStatistics();
                LogInfo($"性能监控: 处理了 {perfStats.TotalEventsProcessed} 个事件，平均处理时间 {perfStats.AverageProcessingTime:F2}ms");
            }

            LogSuccess("性能测试完成");
        }

        #region 事件处理器

        private void OnMenuItemClicked(MenuItemClickedEvent evt)
        {
            LogInfo($"[新系统] 菜单项点击: {evt.MenuItem}");
        }

        private void OnToolSelected(ToolSelectedEvent evt)
        {
            LogInfo($"[新系统] 工具选择: {evt.ToolName}");
        }

        private void OnStatusMessage(StatusMessageEvent evt)
        {
            LogInfo($"[新系统] 状态消息: {evt.Message} ({evt.MessageType})");
        }

        private void OnObjectSelected(ObjectSelectedEvent evt)
        {
            LogInfo($"[新系统] 对象选择: {evt.SelectedObject}");
        }

        #endregion

        #region 日志方法

        private void LogTest(string message)
        {
            if (showDetailedLogs)
            {
                Debug.Log($"<color=cyan>[迁移测试] {message}</color>");
            }
        }

        private void LogSuccess(string message)
        {
            if (showDetailedLogs)
            {
                Debug.Log($"<color=green>[测试成功] {message}</color>");
            }
        }

        private void LogError(string message)
        {
            Debug.LogError($"<color=red>[测试错误] {message}</color>");
        }

        private void LogInfo(string message)
        {
            if (showDetailedLogs)
            {
                Debug.Log($"<color=white>[测试信息] {message}</color>");
            }
        }

        #endregion

        #region 上下文菜单方法

        [ContextMenu("运行迁移测试")]
        private void RunMigrationTestsFromMenu()
        {
            RunMigrationTests();
        }

        [ContextMenu("显示系统报告")]
        private void ShowSystemReport()
        {
            if (EventSystemManager.Instance != null)
            {
                string report = EventSystemManager.Instance.GenerateSystemReport();
                Debug.Log(report);
            }
            else
            {
                LogError("EventSystemManager未找到");
            }
        }

        [ContextMenu("清理测试订阅")]
        private void CleanupTestSubscriptions()
        {
            menuSubscription?.Dispose();
            toolSubscription?.Dispose();
            statusSubscription?.Dispose();
            objectSubscription?.Dispose();

            LogInfo("测试订阅已清理");
        }

        #endregion

        private void OnDestroy()
        {
            // 清理订阅
            CleanupTestSubscriptions();
        }
    }
}
