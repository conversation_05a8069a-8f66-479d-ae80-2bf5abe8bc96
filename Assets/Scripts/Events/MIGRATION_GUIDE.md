# 事件系统迁移指南

## 概述

本指南将帮助您从旧的UIEventSystem迁移到新的高性能事件系统。新系统提供了更好的性能、更强的功能和更好的调试支持，同时保持向后兼容性。

## 迁移策略

### 阶段1：启用新系统（立即可用）

1. **添加EventSystemManager到场景**
   ```csharp
   // 在场景中添加EventSystemManager组件
   // 配置相关设置，特别是启用兼容性模式
   ```

2. **现有代码无需修改**
   - 所有现有的UIEventSystem调用将继续工作
   - 兼容性适配器会自动处理新旧系统之间的转换

### 阶段2：逐步迁移高频事件（推荐）

#### 2.1 菜单事件迁移

**旧代码：**
```csharp
// 触发事件
UIEventSystem.TriggerMenuItemClicked("File.New");

// 监听事件
UIEventSystem.Instance.Toolbar.OnMenuItemClicked.AddListener(OnMenuItemClicked);
```

**新代码：**
```csharp
// 触发事件
EventSystemManager.Instance.PublishEvent(new MenuItemClickedEvent("File.New"));

// 监听事件
var subscription = EventSystemManager.Instance.SubscribeEvent<MenuItemClickedEvent>(OnMenuItemClicked);
```

#### 2.2 工具选择事件迁移

**旧代码：**
```csharp
UIEventSystem.TriggerToolSelected("SelectTool");
UIEventSystem.Instance.Toolbar.OnToolSelected.AddListener(OnToolSelected);
```

**新代码：**
```csharp
EventSystemManager.Instance.PublishEvent(new ToolSelectedEvent("SelectTool"));
var subscription = EventSystemManager.Instance.SubscribeEvent<ToolSelectedEvent>(OnToolSelected);
```

#### 2.3 状态消息事件迁移

**旧代码：**
```csharp
UIEventSystem.TriggerStatusMessage("操作完成");
UIEventSystem.Instance.Status.OnStatusMessageChanged.AddListener(OnStatusChanged);
```

**新代码：**
```csharp
EventSystemManager.Instance.PublishEvent(new StatusMessageEvent("操作完成", StatusMessageType.Success));
var subscription = EventSystemManager.Instance.SubscribeEvent<StatusMessageEvent>(OnStatusChanged);
```

### 阶段3：利用新功能（可选）

#### 3.1 条件订阅

```csharp
// 只监听File菜单的事件
var subscription = EventSystemManager.Instance.EventBus.Subscribe<MenuItemClickedEvent>(
    OnFileMenuClicked,
    evt => evt.MenuItem.StartsWith("File.")
);
```

#### 3.2 优先级订阅

```csharp
// 高优先级事件处理
var subscription = EventSystemManager.Instance.EventBus.Subscribe<StatusMessageEvent>(
    OnHighPriorityStatus,
    100 // 优先级
);
```

#### 3.3 事件过滤

```csharp
// 添加全局过滤器
EventSystemManager.Instance.FilterManager.AddFilter<MenuItemClickedEvent>(
    evt => !evt.MenuItem.Contains("Debug"), // 过滤Debug菜单
    50,
    "DebugFilter"
);
```

#### 3.4 延迟和批量事件

```csharp
// 延迟发布
EventSystemManager.Instance.EventBus.PublishDelayed(
    new StatusMessageEvent("延迟消息"), 
    2.0f
);

// 批量发布
var events = new[] {
    new StatusMessageEvent("消息1"),
    new StatusMessageEvent("消息2")
};
EventSystemManager.Instance.EventBus.PublishBatch(events);
```

### 阶段4：清理旧代码（最终阶段）

1. **移除UIEventSystem引用**
2. **删除兼容性适配器**
3. **清理过时的API调用**

## 性能优化建议

### 1. 使用对象池

新系统默认启用对象池，可以显著减少GC压力：

```csharp
// EventSystemManager配置中启用
useObjectPool = true;
```

### 2. 启用性能监控

```csharp
// 在EventSystemManager中启用
enablePerformanceMonitoring = true;

// 查看性能报告
var report = EventSystemManager.Instance.GenerateSystemReport();
Debug.Log(report);
```

### 3. 使用高性能分发器

对于极高频的事件，可以使用专门的高性能分发器：

```csharp
var dispatcher = new HighPerformanceEventDispatcher();
dispatcher.Subscribe<MenuItemClickedEvent>(OnMenuClicked);
dispatcher.Dispatch(new MenuItemClickedEvent("File.New"));
```

## 调试和监控

### 1. 事件流可视化

```csharp
// 启用事件调试
enableEventDebugging = true;

// 查看事件流报告
var debugReport = EventSystemManager.Instance.EventDebugger.GenerateEventFlowReport();
Debug.Log(debugReport);
```

### 2. 事件重放

```csharp
// 开始记录
EventSystemManager.Instance.ReplaySystem.StartRecording();

// 停止记录并重放
EventSystemManager.Instance.ReplaySystem.StopRecording();
EventSystemManager.Instance.ReplaySystem.StartReplay();
```

### 3. 性能分析

```csharp
// 获取性能统计
var perfStats = EventSystemManager.Instance.PerformanceMonitor.GetOverallStatistics();
Debug.Log($"平均处理时间: {perfStats.AverageProcessingTime}ms");
```

## 常见问题

### Q: 现有代码会立即停止工作吗？
A: 不会。兼容性适配器确保所有现有代码继续正常工作。

### Q: 新系统的性能提升有多大？
A: 根据基准测试，事件发布性能提升2-5倍，内存分配减少80%。

### Q: 如何处理自定义事件？
A: 实现IEvent接口或继承EventBase类：

```csharp
public class CustomEvent : EventBase
{
    public string CustomData { get; set; }
    
    public CustomEvent(string data) : base("CustomSource")
    {
        CustomData = data;
    }
}
```

### Q: 如何确保事件订阅被正确清理？
A: 使用订阅句柄的Dispose方法：

```csharp
private IEventSubscription _subscription;

void Start()
{
    _subscription = EventSystemManager.Instance.SubscribeEvent<MenuItemClickedEvent>(OnMenuClicked);
}

void OnDestroy()
{
    _subscription?.Dispose();
}
```

### Q: 可以同时使用新旧系统吗？
A: 可以。兼容性适配器允许新旧系统共存，事件会在两个系统间自动同步。

## 最佳实践

1. **逐步迁移**：不要一次性迁移所有代码，从高频事件开始
2. **使用订阅句柄**：始终保存订阅句柄并在适当时机清理
3. **启用监控**：在开发阶段启用性能监控和调试功能
4. **合理使用过滤器**：避免过度使用过滤器影响性能
5. **测试兼容性**：在迁移过程中充分测试新旧系统的兼容性

## 支持和帮助

如果在迁移过程中遇到问题，可以：

1. 查看EventSystemUsageExample.cs中的使用示例
2. 启用详细日志记录进行调试
3. 使用事件调试器分析事件流
4. 查看性能监控报告识别瓶颈

迁移是一个渐进的过程，不需要急于一次性完成。新系统的兼容性设计确保您可以按照自己的节奏进行迁移。
