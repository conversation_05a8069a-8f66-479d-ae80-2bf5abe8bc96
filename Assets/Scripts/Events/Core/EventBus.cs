using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using UnityEngine;
using BlastingDesign.Events.Services;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 事件总线实现
    /// 高性能的事件分发系统
    /// </summary>
    public class EventBus : IEventBus
    {
        private readonly ConcurrentDictionary<Type, List<EventSubscription>> _subscriptions;
        private readonly EventPool _eventPool;
        private readonly EventScheduler _scheduler;
        private readonly EventPerformanceMonitor _performanceMonitor;
        private readonly object _lockObject = new object();

        // 统计信息
        private readonly EventStatistics _statistics;

        // 配置
        public bool DebugMode { get; set; }
        private readonly bool _useObjectPool;
        private readonly bool _enablePerformanceMonitoring;

        public EventBus(bool useObjectPool = true, bool debugMode = false, bool enablePerformanceMonitoring = true)
        {
            _subscriptions = new ConcurrentDictionary<Type, List<EventSubscription>>();
            _useObjectPool = useObjectPool;
            _enablePerformanceMonitoring = enablePerformanceMonitoring;
            DebugMode = debugMode;

            if (_useObjectPool)
            {
                _eventPool = new EventPool(100, debugMode);
            }

            _scheduler = new EventScheduler(0.016f, debugMode);
            _statistics = new EventStatistics();

            if (_enablePerformanceMonitoring)
            {
                _performanceMonitor = new EventPerformanceMonitor(1000, debugMode);
            }
        }

        #region 基础订阅/取消订阅

        public IEventSubscription Subscribe<T>(Action<T> handler) where T : IEvent
        {
            return Subscribe(handler, null, 0);
        }

        public void Unsubscribe<T>(Action<T> handler) where T : IEvent
        {
            if (handler == null) return;

            Type eventType = typeof(T);
            if (_subscriptions.TryGetValue(eventType, out List<EventSubscription> subscriptions))
            {
                lock (_lockObject)
                {
                    for (int i = subscriptions.Count - 1; i >= 0; i--)
                    {
                        var subscription = subscriptions[i];
                        if (subscription.Handler.Equals(handler))
                        {
                            subscriptions.RemoveAt(i);
                            subscription.Dispose();

                            if (DebugMode)
                            {
                                UnityEngine.Debug.Log($"EventBus: 取消订阅事件 {eventType.Name}");
                            }
                            break;
                        }
                    }
                }
            }
        }

        public void Unsubscribe(IEventSubscription subscription)
        {
            if (subscription == null || !subscription.IsValid) return;

            var eventSubscription = subscription as EventSubscription;
            if (eventSubscription == null) return;

            Type eventType = eventSubscription.EventType;
            if (_subscriptions.TryGetValue(eventType, out List<EventSubscription> subscriptions))
            {
                lock (_lockObject)
                {
                    subscriptions.Remove(eventSubscription);
                    eventSubscription.Dispose();

                    if (DebugMode)
                    {
                        UnityEngine.Debug.Log($"EventBus: 通过句柄取消订阅事件 {eventType.Name}");
                    }
                }
            }
        }

        #endregion

        #region 条件订阅

        public IEventSubscription Subscribe<T>(Action<T> handler, Func<T, bool> filter) where T : IEvent
        {
            return Subscribe(handler, filter, 0);
        }

        #endregion

        #region 优先级订阅

        public IEventSubscription Subscribe<T>(Action<T> handler, int priority) where T : IEvent
        {
            return Subscribe(handler, null, priority);
        }

        public IEventSubscription Subscribe<T>(Action<T> handler, Func<T, bool> filter, int priority) where T : IEvent
        {
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            Type eventType = typeof(T);
            var subscription = new EventSubscription<T>(handler, filter, priority, eventType);

            if (!_subscriptions.TryGetValue(eventType, out List<EventSubscription> subscriptions))
            {
                subscriptions = new List<EventSubscription>();
                _subscriptions[eventType] = subscriptions;
            }

            lock (_lockObject)
            {
                // 按优先级插入（优先级高的在前面）
                int insertIndex = 0;
                for (int i = 0; i < subscriptions.Count; i++)
                {
                    if (subscriptions[i].Priority < priority)
                    {
                        insertIndex = i;
                        break;
                    }
                    insertIndex = i + 1;
                }

                subscriptions.Insert(insertIndex, subscription);
                _statistics.TotalSubscriptions++;

                if (DebugMode)
                {
                    UnityEngine.Debug.Log($"EventBus: 订阅事件 {eventType.Name}，优先级: {priority}，总订阅数: {subscriptions.Count}");
                }
            }

            return subscription;
        }

        #endregion

        #region 事件发布

        public void Publish<T>(T eventData) where T : IEvent
        {
            if (eventData == null) return;

            Type eventType = typeof(T);
            _statistics.TotalPublishedEvents++;

            var stopwatch = Stopwatch.StartNew();
            int subscriberCount = 0;

            try
            {
                if (_subscriptions.TryGetValue(eventType, out List<EventSubscription> subscriptions))
                {
                    List<EventSubscription> currentSubscriptions;

                    lock (_lockObject)
                    {
                        // 创建副本以避免在迭代过程中修改集合
                        currentSubscriptions = new List<EventSubscription>(subscriptions);
                        subscriberCount = currentSubscriptions.Count(s => s.IsValid);
                    }

                    foreach (var subscription in currentSubscriptions)
                    {
                        if (!subscription.IsValid) continue;

                        try
                        {
                            subscription.Invoke(eventData);

                            if (eventData.IsCancelled)
                            {
                                if (DebugMode)
                                {
                                    UnityEngine.Debug.Log($"EventBus: 事件 {eventType.Name} 被取消");
                                }
                                break;
                            }
                        }
                        catch (Exception ex)
                        {
                            UnityEngine.Debug.LogError($"EventBus: 处理事件 {eventType.Name} 时发生错误: {ex.Message}");
                        }
                    }
                }

                if (DebugMode)
                {
                    UnityEngine.Debug.Log($"EventBus: 发布事件 {eventType.Name}，订阅者数量: {subscriberCount}");
                }
            }
            finally
            {
                stopwatch.Stop();
                double executionTime = stopwatch.Elapsed.TotalMilliseconds;

                _statistics.UpdateProcessingTime(eventType, executionTime);

                // 性能监控
                if (_enablePerformanceMonitoring && _performanceMonitor != null)
                {
                    _performanceMonitor.RecordEventExecution(eventType, executionTime, subscriberCount, eventData.Source);
                }

                // 如果使用对象池，将事件返回到池中
                if (_useObjectPool && _eventPool != null)
                {
                    _eventPool.Return(eventData);
                }
            }
        }

        public async Task PublishAsync<T>(T eventData) where T : IEvent
        {
            await Task.Run(() => Publish(eventData));
        }

        #endregion

        #region 延迟和批量发布

        public void PublishDelayed<T>(T eventData, float delay) where T : IEvent
        {
            _scheduler.ScheduleEvent(eventData, delay, Publish);
        }

        public void PublishBatch<T>(IEnumerable<T> events) where T : IEvent
        {
            _scheduler.ScheduleBatch(events, Publish);
        }

        #endregion

        #region 查询和管理

        public int GetSubscriberCount<T>() where T : IEvent
        {
            Type eventType = typeof(T);
            if (_subscriptions.TryGetValue(eventType, out List<EventSubscription> subscriptions))
            {
                lock (_lockObject)
                {
                    return subscriptions.Count(s => s.IsValid);
                }
            }
            return 0;
        }

        public bool HasSubscribers<T>() where T : IEvent
        {
            return GetSubscriberCount<T>() > 0;
        }

        public void ClearAllSubscriptions()
        {
            lock (_lockObject)
            {
                foreach (var subscriptions in _subscriptions.Values)
                {
                    foreach (var subscription in subscriptions)
                    {
                        subscription.Dispose();
                    }
                    subscriptions.Clear();
                }
                _subscriptions.Clear();
                _statistics.TotalSubscriptions = 0;

                if (DebugMode)
                {
                    UnityEngine.Debug.Log("EventBus: 清除所有订阅");
                }
            }
        }

        public void ClearSubscriptions<T>() where T : IEvent
        {
            Type eventType = typeof(T);
            if (_subscriptions.TryGetValue(eventType, out List<EventSubscription> subscriptions))
            {
                lock (_lockObject)
                {
                    int count = subscriptions.Count;
                    foreach (var subscription in subscriptions)
                    {
                        subscription.Dispose();
                    }
                    subscriptions.Clear();
                    _statistics.TotalSubscriptions -= count;

                    if (DebugMode)
                    {
                        UnityEngine.Debug.Log($"EventBus: 清除事件 {eventType.Name} 的所有订阅，数量: {count}");
                    }
                }
            }
        }

        #endregion

        #region 事件统计和调试

        public IEventStatistics GetStatistics()
        {
            return _statistics;
        }

        #endregion

        #region 性能监控

        /// <summary>
        /// 获取性能监控器
        /// </summary>
        /// <returns>性能监控器实例</returns>
        public EventPerformanceMonitor GetPerformanceMonitor()
        {
            return _performanceMonitor;
        }

        /// <summary>
        /// 生成性能报告
        /// </summary>
        /// <returns>性能报告字符串</returns>
        public string GeneratePerformanceReport()
        {
            return _performanceMonitor?.GeneratePerformanceReport() ?? "性能监控未启用";
        }

        /// <summary>
        /// 重置性能统计
        /// </summary>
        public void ResetPerformanceStatistics()
        {
            _performanceMonitor?.Reset();
        }

        #endregion

        /// <summary>
        /// 处理调度的事件
        /// 应该在主线程的Update中调用
        /// </summary>
        public void Update()
        {
            _scheduler?.ProcessScheduledEvents();
        }

        public void Dispose()
        {
            ClearAllSubscriptions();
            _eventPool?.Dispose();
            _scheduler?.Dispose();
            _performanceMonitor?.Dispose();
        }
    }

    /// <summary>
    /// 事件订阅实现
    /// </summary>
    internal abstract class EventSubscription : IEventSubscription
    {
        public bool IsValid { get; private set; } = true;
        public Type EventType { get; protected set; }
        public int Priority { get; protected set; }
        public DateTime SubscribeTime { get; protected set; }

        protected EventSubscription(Type eventType, int priority)
        {
            EventType = eventType;
            Priority = priority;
            SubscribeTime = DateTime.UtcNow;
        }

        public abstract void Invoke(IEvent eventData);

        public virtual void Dispose()
        {
            IsValid = false;
        }
    }

    /// <summary>
    /// 泛型事件订阅实现
    /// </summary>
    internal class EventSubscription<T> : EventSubscription where T : IEvent
    {
        public Action<T> Handler { get; private set; }
        public Func<T, bool> Filter { get; private set; }

        public EventSubscription(Action<T> handler, Func<T, bool> filter, int priority, Type eventType)
            : base(eventType, priority)
        {
            Handler = handler ?? throw new ArgumentNullException(nameof(handler));
            Filter = filter;
        }

        public override void Invoke(IEvent eventData)
        {
            if (!IsValid || eventData == null) return;

            if (eventData is T typedEvent)
            {
                // 应用过滤器
                if (Filter != null && !Filter(typedEvent)) return;

                Handler(typedEvent);
            }
        }

        public override void Dispose()
        {
            base.Dispose();
            Handler = null;
            Filter = null;
        }
    }

    /// <summary>
    /// 事件统计信息实现
    /// </summary>
    internal class EventStatistics : IEventStatistics
    {
        private readonly ConcurrentDictionary<Type, EventTypeStatistics> _typeStatistics;
        private readonly object _lockObject = new object();

        public long TotalPublishedEvents { get; internal set; }
        public int TotalSubscriptions { get; internal set; }
        public int ActiveEventTypes => _typeStatistics.Count;

        public double AverageProcessingTime
        {
            get
            {
                if (_typeStatistics.IsEmpty) return 0;

                double totalTime = 0;
                long totalEvents = 0;

                foreach (var stats in _typeStatistics.Values)
                {
                    totalTime += stats.TotalProcessingTime;
                    totalEvents += stats.PublishCount;
                }

                return totalEvents > 0 ? totalTime / totalEvents : 0;
            }
        }

        public EventStatistics()
        {
            _typeStatistics = new ConcurrentDictionary<Type, EventTypeStatistics>();
        }

        public IEventTypeStatistics GetEventTypeStatistics(Type eventType)
        {
            return _typeStatistics.GetOrAdd(eventType, type => new EventTypeStatistics(type));
        }

        internal void UpdateProcessingTime(Type eventType, double processingTime)
        {
            var stats = _typeStatistics.GetOrAdd(eventType, type => new EventTypeStatistics(type));
            stats.UpdateProcessingTime(processingTime);
        }
    }

    /// <summary>
    /// 事件类型统计信息实现
    /// </summary>
    internal class EventTypeStatistics : IEventTypeStatistics
    {
        private readonly object _lockObject = new object();

        public Type EventType { get; private set; }
        public long PublishCount { get; private set; }
        public int SubscriberCount { get; internal set; }
        public DateTime LastPublishTime { get; private set; }

        internal double TotalProcessingTime { get; private set; }

        public double AverageProcessingTime
        {
            get
            {
                lock (_lockObject)
                {
                    return PublishCount > 0 ? TotalProcessingTime / PublishCount : 0;
                }
            }
        }

        public EventTypeStatistics(Type eventType)
        {
            EventType = eventType;
            LastPublishTime = DateTime.MinValue;
        }

        internal void UpdateProcessingTime(double processingTime)
        {
            lock (_lockObject)
            {
                PublishCount++;
                TotalProcessingTime += processingTime;
                LastPublishTime = DateTime.UtcNow;
            }
        }
    }
}
