using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 事件总线接口
    /// 定义事件系统的核心功能
    /// </summary>
    public interface IEventBus : IDisposable
    {
        #region 基础订阅/取消订阅
        
        /// <summary>
        /// 订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄，用于取消订阅</returns>
        IEventSubscription Subscribe<T>(Action<T> handler) where T : IEvent;
        
        /// <summary>
        /// 取消订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        void Unsubscribe<T>(Action<T> handler) where T : IEvent;
        
        /// <summary>
        /// 通过订阅句柄取消订阅
        /// </summary>
        /// <param name="subscription">订阅句柄</param>
        void Unsubscribe(IEventSubscription subscription);
        
        #endregion
        
        #region 条件订阅
        
        /// <summary>
        /// 条件订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <param name="filter">过滤条件</param>
        /// <returns>订阅句柄</returns>
        IEventSubscription Subscribe<T>(Action<T> handler, Func<T, bool> filter) where T : IEvent;
        
        #endregion
        
        #region 优先级订阅
        
        /// <summary>
        /// 优先级订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <param name="priority">优先级（数值越大优先级越高）</param>
        /// <returns>订阅句柄</returns>
        IEventSubscription Subscribe<T>(Action<T> handler, int priority) where T : IEvent;
        
        /// <summary>
        /// 条件和优先级订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <param name="filter">过滤条件</param>
        /// <param name="priority">优先级</param>
        /// <returns>订阅句柄</returns>
        IEventSubscription Subscribe<T>(Action<T> handler, Func<T, bool> filter, int priority) where T : IEvent;
        
        #endregion
        
        #region 事件发布
        
        /// <summary>
        /// 同步发布事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        void Publish<T>(T eventData) where T : IEvent;
        
        /// <summary>
        /// 异步发布事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        /// <returns>异步任务</returns>
        Task PublishAsync<T>(T eventData) where T : IEvent;
        
        #endregion
        
        #region 延迟和批量发布
        
        /// <summary>
        /// 延迟发布事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        /// <param name="delay">延迟时间（秒）</param>
        void PublishDelayed<T>(T eventData, float delay) where T : IEvent;
        
        /// <summary>
        /// 批量发布事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="events">事件集合</param>
        void PublishBatch<T>(IEnumerable<T> events) where T : IEvent;
        
        #endregion
        
        #region 查询和管理
        
        /// <summary>
        /// 获取指定事件类型的订阅者数量
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <returns>订阅者数量</returns>
        int GetSubscriberCount<T>() where T : IEvent;
        
        /// <summary>
        /// 检查是否有指定事件类型的订阅者
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <returns>是否有订阅者</returns>
        bool HasSubscribers<T>() where T : IEvent;
        
        /// <summary>
        /// 清除所有订阅
        /// </summary>
        void ClearAllSubscriptions();
        
        /// <summary>
        /// 清除指定事件类型的所有订阅
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        void ClearSubscriptions<T>() where T : IEvent;
        
        #endregion
        
        #region 事件统计和调试
        
        /// <summary>
        /// 获取事件统计信息
        /// </summary>
        /// <returns>事件统计</returns>
        IEventStatistics GetStatistics();
        
        /// <summary>
        /// 启用/禁用调试模式
        /// </summary>
        bool DebugMode { get; set; }
        
        #endregion
    }
    
    /// <summary>
    /// 事件订阅句柄接口
    /// </summary>
    public interface IEventSubscription : IDisposable
    {
        /// <summary>
        /// 订阅是否有效
        /// </summary>
        bool IsValid { get; }
        
        /// <summary>
        /// 事件类型
        /// </summary>
        Type EventType { get; }
        
        /// <summary>
        /// 优先级
        /// </summary>
        int Priority { get; }
        
        /// <summary>
        /// 订阅时间
        /// </summary>
        DateTime SubscribeTime { get; }
    }
    
    /// <summary>
    /// 事件统计信息接口
    /// </summary>
    public interface IEventStatistics
    {
        /// <summary>
        /// 总发布事件数
        /// </summary>
        long TotalPublishedEvents { get; }
        
        /// <summary>
        /// 总订阅数
        /// </summary>
        int TotalSubscriptions { get; }
        
        /// <summary>
        /// 活跃事件类型数
        /// </summary>
        int ActiveEventTypes { get; }
        
        /// <summary>
        /// 平均事件处理时间（毫秒）
        /// </summary>
        double AverageProcessingTime { get; }
        
        /// <summary>
        /// 获取指定事件类型的统计信息
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <returns>事件类型统计</returns>
        IEventTypeStatistics GetEventTypeStatistics(Type eventType);
    }
    
    /// <summary>
    /// 事件类型统计信息接口
    /// </summary>
    public interface IEventTypeStatistics
    {
        /// <summary>
        /// 事件类型
        /// </summary>
        Type EventType { get; }
        
        /// <summary>
        /// 发布次数
        /// </summary>
        long PublishCount { get; }
        
        /// <summary>
        /// 订阅者数量
        /// </summary>
        int SubscriberCount { get; }
        
        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        double AverageProcessingTime { get; }
        
        /// <summary>
        /// 最后发布时间
        /// </summary>
        DateTime LastPublishTime { get; }
    }
}
