using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using UnityEngine;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 优化的字符串事件系统
    /// 使用字符串哈希和预分配字典来优化字符串比较性能
    /// </summary>
    public class OptimizedStringEventSystem : IDisposable
    {
        // 使用字符串哈希作为键，避免字符串比较
        private readonly Dictionary<int, List<Action<string>>> _stringHandlers;
        private readonly Dictionary<int, string> _hashToString; // 用于调试和日志
        private readonly object _lockObject = new object();
        
        // 预分配的临时列表，避免GC
        private readonly List<Action<string>> _tempHandlerList = new List<Action<string>>(16);
        
        private readonly bool _enableDebugLogging;
        
        public OptimizedStringEventSystem(bool enableDebugLogging = false)
        {
            _stringHandlers = new Dictionary<int, List<Action<string>>>();
            _hashToString = new Dictionary<int, string>();
            _enableDebugLogging = enableDebugLogging;
        }
        
        /// <summary>
        /// 订阅字符串事件
        /// </summary>
        /// <param name="eventName">事件名称</param>
        /// <param name="handler">事件处理器</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Subscribe(string eventName, Action<string> handler)
        {
            if (string.IsNullOrEmpty(eventName) || handler == null) return;
            
            int hash = eventName.GetHashCode();
            
            lock (_lockObject)
            {
                if (!_stringHandlers.TryGetValue(hash, out List<Action<string>> handlers))
                {
                    handlers = new List<Action<string>>();
                    _stringHandlers[hash] = handlers;
                    _hashToString[hash] = eventName; // 保存原始字符串用于调试
                }
                
                handlers.Add(handler);
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"OptimizedStringEventSystem: 订阅字符串事件 '{eventName}' (Hash: {hash})");
                }
            }
        }
        
        /// <summary>
        /// 取消订阅字符串事件
        /// </summary>
        /// <param name="eventName">事件名称</param>
        /// <param name="handler">事件处理器</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Unsubscribe(string eventName, Action<string> handler)
        {
            if (string.IsNullOrEmpty(eventName) || handler == null) return;
            
            int hash = eventName.GetHashCode();
            
            lock (_lockObject)
            {
                if (_stringHandlers.TryGetValue(hash, out List<Action<string>> handlers))
                {
                    handlers.Remove(handler);
                    
                    // 如果没有处理器了，清理哈希表
                    if (handlers.Count == 0)
                    {
                        _stringHandlers.Remove(hash);
                        _hashToString.Remove(hash);
                    }
                    
                    if (_enableDebugLogging)
                    {
                        Debug.Log($"OptimizedStringEventSystem: 取消订阅字符串事件 '{eventName}' (Hash: {hash})");
                    }
                }
            }
        }
        
        /// <summary>
        /// 触发字符串事件
        /// </summary>
        /// <param name="eventName">事件名称</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Trigger(string eventName)
        {
            if (string.IsNullOrEmpty(eventName)) return;
            
            int hash = eventName.GetHashCode();
            
            if (!_stringHandlers.TryGetValue(hash, out List<Action<string>> handlers) || handlers.Count == 0)
                return;
            
            // 复制处理器列表以避免长时间锁定
            _tempHandlerList.Clear();
            lock (_lockObject)
            {
                _tempHandlerList.AddRange(handlers);
            }
            
            // 执行处理器
            for (int i = 0; i < _tempHandlerList.Count; i++)
            {
                try
                {
                    _tempHandlerList[i](eventName);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"OptimizedStringEventSystem: 执行字符串事件处理器时发生错误 '{eventName}': {ex.Message}");
                }
            }
            
            if (_enableDebugLogging)
            {
                Debug.Log($"OptimizedStringEventSystem: 触发字符串事件 '{eventName}' (Hash: {hash}), 处理器数量: {_tempHandlerList.Count}");
            }
        }
        
        /// <summary>
        /// 批量触发字符串事件
        /// </summary>
        /// <param name="eventNames">事件名称数组</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void TriggerBatch(string[] eventNames)
        {
            if (eventNames == null || eventNames.Length == 0) return;
            
            for (int i = 0; i < eventNames.Length; i++)
            {
                Trigger(eventNames[i]);
            }
        }
        
        /// <summary>
        /// 获取指定事件的订阅者数量
        /// </summary>
        /// <param name="eventName">事件名称</param>
        /// <returns>订阅者数量</returns>
        public int GetSubscriberCount(string eventName)
        {
            if (string.IsNullOrEmpty(eventName)) return 0;
            
            int hash = eventName.GetHashCode();
            
            lock (_lockObject)
            {
                if (_stringHandlers.TryGetValue(hash, out List<Action<string>> handlers))
                {
                    return handlers.Count;
                }
            }
            
            return 0;
        }
        
        /// <summary>
        /// 检查是否有指定事件的订阅者
        /// </summary>
        /// <param name="eventName">事件名称</param>
        /// <returns>是否有订阅者</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public bool HasSubscribers(string eventName)
        {
            return GetSubscriberCount(eventName) > 0;
        }
        
        /// <summary>
        /// 获取所有已注册的事件名称
        /// </summary>
        /// <returns>事件名称列表</returns>
        public List<string> GetAllEventNames()
        {
            lock (_lockObject)
            {
                return new List<string>(_hashToString.Values);
            }
        }
        
        /// <summary>
        /// 清除指定事件的所有订阅
        /// </summary>
        /// <param name="eventName">事件名称</param>
        public void ClearSubscriptions(string eventName)
        {
            if (string.IsNullOrEmpty(eventName)) return;
            
            int hash = eventName.GetHashCode();
            
            lock (_lockObject)
            {
                if (_stringHandlers.ContainsKey(hash))
                {
                    int count = _stringHandlers[hash].Count;
                    _stringHandlers.Remove(hash);
                    _hashToString.Remove(hash);
                    
                    if (_enableDebugLogging)
                    {
                        Debug.Log($"OptimizedStringEventSystem: 清除事件 '{eventName}' 的所有订阅，数量: {count}");
                    }
                }
            }
        }
        
        /// <summary>
        /// 清除所有订阅
        /// </summary>
        public void ClearAll()
        {
            lock (_lockObject)
            {
                int totalHandlers = 0;
                foreach (var handlers in _stringHandlers.Values)
                {
                    totalHandlers += handlers.Count;
                }
                
                _stringHandlers.Clear();
                _hashToString.Clear();
                
                if (_enableDebugLogging)
                {
                    Debug.Log($"OptimizedStringEventSystem: 清除所有订阅，总数: {totalHandlers}");
                }
            }
        }
        
        /// <summary>
        /// 获取系统统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public StringEventSystemStatistics GetStatistics()
        {
            lock (_lockObject)
            {
                int totalHandlers = 0;
                foreach (var handlers in _stringHandlers.Values)
                {
                    totalHandlers += handlers.Count;
                }
                
                return new StringEventSystemStatistics
                {
                    RegisteredEventCount = _stringHandlers.Count,
                    TotalHandlerCount = totalHandlers,
                    AverageHandlersPerEvent = _stringHandlers.Count > 0 ? (double)totalHandlers / _stringHandlers.Count : 0
                };
            }
        }
        
        public void Dispose()
        {
            ClearAll();
        }
    }
    
    /// <summary>
    /// 字符串事件系统统计信息
    /// </summary>
    public class StringEventSystemStatistics
    {
        public int RegisteredEventCount { get; set; }
        public int TotalHandlerCount { get; set; }
        public double AverageHandlersPerEvent { get; set; }
    }
}
