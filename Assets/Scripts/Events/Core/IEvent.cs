using System;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 事件基础接口
    /// 所有事件类型都必须实现此接口
    /// </summary>
    public interface IEvent
    {
        /// <summary>
        /// 事件时间戳
        /// </summary>
        DateTime Timestamp { get; }
        
        /// <summary>
        /// 事件唯一标识符
        /// </summary>
        string EventId { get; }
        
        /// <summary>
        /// 事件源标识
        /// </summary>
        string Source { get; }
        
        /// <summary>
        /// 事件是否已被处理
        /// </summary>
        bool IsHandled { get; set; }
        
        /// <summary>
        /// 事件是否可以被取消
        /// </summary>
        bool CanBeCancelled { get; }
        
        /// <summary>
        /// 取消事件
        /// </summary>
        void Cancel();
        
        /// <summary>
        /// 事件是否已被取消
        /// </summary>
        bool IsCancelled { get; }
    }
    
    /// <summary>
    /// 事件基础实现类
    /// 提供IEvent接口的默认实现
    /// </summary>
    public abstract class EventBase : IEvent
    {
        public DateTime Timestamp { get; private set; }
        public string EventId { get; private set; }
        public string Source { get; protected set; }
        public bool IsHandled { get; set; }
        public virtual bool CanBeCancelled => true;
        public bool IsCancelled { get; private set; }
        
        protected EventBase(string source = null)
        {
            Timestamp = DateTime.UtcNow;
            EventId = Guid.NewGuid().ToString();
            Source = source ?? "Unknown";
            IsHandled = false;
            IsCancelled = false;
        }
        
        public virtual void Cancel()
        {
            if (CanBeCancelled)
            {
                IsCancelled = true;
            }
        }
        
        /// <summary>
        /// 重置事件状态（用于对象池）
        /// </summary>
        public virtual void Reset()
        {
            Timestamp = DateTime.UtcNow;
            EventId = Guid.NewGuid().ToString();
            IsHandled = false;
            IsCancelled = false;
        }
    }
}
