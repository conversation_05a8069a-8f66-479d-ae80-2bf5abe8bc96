using UnityEngine;

namespace BlastingDesign.Events.Core
{
    /// <summary>
    /// 菜单项点击事件
    /// </summary>
    public class MenuItemClickedEvent : EventBase
    {
        public string MenuItem { get; set; }
        public object Data { get; set; }

        public MenuItemClickedEvent(string menuItem, object data = null, string source = "MenuBar")
            : base(source)
        {
            MenuItem = menuItem;
            Data = data;
        }

        public override void Reset()
        {
            base.Reset();
            MenuItem = null;
            Data = null;
        }
    }

    /// <summary>
    /// 工具选择事件
    /// </summary>
    public class ToolSelectedEvent : EventBase
    {
        public string ToolName { get; set; }
        public object ToolData { get; set; }

        public ToolSelectedEvent(string toolName, object toolData = null, string source = "Toolbar")
            : base(source)
        {
            ToolName = toolName;
            ToolData = toolData;
        }

        public override void Reset()
        {
            base.Reset();
            ToolName = null;
            ToolData = null;
        }
    }

    /// <summary>
    /// 对象选择事件
    /// </summary>
    public class ObjectSelectedEvent : EventBase
    {
        public object SelectedObject { get; set; }
        public bool IsMultiSelect { get; set; }
        public bool IsAddToSelection { get; set; }

        public ObjectSelectedEvent(object selectedObject, bool isMultiSelect = false, bool isAddToSelection = false, string source = "SelectionManager")
            : base(source)
        {
            SelectedObject = selectedObject;
            IsMultiSelect = isMultiSelect;
            IsAddToSelection = isAddToSelection;
        }

        public override void Reset()
        {
            base.Reset();
            SelectedObject = null;
            IsMultiSelect = false;
            IsAddToSelection = false;
        }
    }

    /// <summary>
    /// 状态消息事件
    /// </summary>
    public class StatusMessageEvent : EventBase
    {
        public string Message { get; set; }
        public StatusMessageType MessageType { get; set; }
        public float Duration { get; set; }

        public StatusMessageEvent(string message, StatusMessageType messageType = StatusMessageType.Info, float duration = 3f, string source = "StatusBar")
            : base(source)
        {
            Message = message;
            MessageType = messageType;
            Duration = duration;
        }

        public override void Reset()
        {
            base.Reset();
            Message = null;
            MessageType = StatusMessageType.Info;
            Duration = 3f;
        }
    }

    /// <summary>
    /// 面板事件
    /// </summary>
    public class PanelEvent : EventBase
    {
        public string PanelName { get; set; }
        public PanelAction Action { get; set; }
        public object Data { get; set; }

        public PanelEvent(string panelName, PanelAction action, object data = null, string source = "PanelManager")
            : base(source)
        {
            PanelName = panelName;
            Action = action;
            Data = data;
        }

        public override void Reset()
        {
            base.Reset();
            PanelName = null;
            Action = PanelAction.Show;
            Data = null;
        }
    }

    /// <summary>
    /// 属性变更事件
    /// </summary>
    public class PropertyChangedEvent : EventBase
    {
        public object Target { get; set; }
        public string PropertyName { get; set; }
        public object OldValue { get; set; }
        public object NewValue { get; set; }

        public PropertyChangedEvent(object target, string propertyName, object oldValue, object newValue, string source = "PropertyPanel")
            : base(source)
        {
            Target = target;
            PropertyName = propertyName;
            OldValue = oldValue;
            NewValue = newValue;
        }

        public override void Reset()
        {
            base.Reset();
            Target = null;
            PropertyName = null;
            OldValue = null;
            NewValue = null;
        }
    }

    /// <summary>
    /// 自定义事件
    /// </summary>
    public class CustomEvent : EventBase
    {
        public string EventName { get; set; }
        public object Data { get; set; }

        public CustomEvent(string eventName, object data = null, string source = "Custom")
            : base(source)
        {
            EventName = eventName;
            Data = data;
        }

        public override void Reset()
        {
            base.Reset();
            EventName = null;
            Data = null;
        }
    }

    /// <summary>
    /// 输入事件
    /// </summary>
    public class InputEvent : EventBase
    {
        public InputEventType InputType { get; set; }
        public Vector2 Position { get; set; }
        public KeyCode KeyCode { get; set; }
        public bool IsPressed { get; set; }
        public bool IsReleased { get; set; }

        public InputEvent(InputEventType inputType, Vector2 position = default, KeyCode keyCode = KeyCode.None, string source = "InputManager")
            : base(source)
        {
            InputType = inputType;
            Position = position;
            KeyCode = keyCode;
        }

        public override void Reset()
        {
            base.Reset();
            InputType = InputEventType.Mouse;
            Position = Vector2.zero;
            KeyCode = KeyCode.None;
            IsPressed = false;
            IsReleased = false;
        }
    }

    #region 枚举定义

    public enum StatusMessageType
    {
        Info,
        Warning,
        Error,
        Success
    }

    public enum PanelAction
    {
        Show,
        Hide,
        Toggle,
        Focus,
        Refresh
    }

    public enum InputEventType
    {
        Mouse,
        Keyboard,
        Touch,
        Gamepad
    }

    public enum MouseButton
    {
        Left,
        Right,
        Middle,
        Forward,
        Back
    }

    #endregion

    /// <summary>
    /// 鼠标输入事件
    /// </summary>
    public class MouseInputEvent : EventBase
    {
        public Vector2 Position { get; set; }
        public MouseButton Button { get; set; }
        public bool IsPressed { get; set; }
        public bool IsReleased { get; set; }
        public float ScrollDelta { get; set; }

        public MouseInputEvent(Vector2 position, MouseButton button, bool isPressed = false, bool isReleased = false, string source = "InputManager")
            : base(source)
        {
            Position = position;
            Button = button;
            IsPressed = isPressed;
            IsReleased = isReleased;
        }

        public override void Reset()
        {
            base.Reset();
            Position = Vector2.zero;
            Button = MouseButton.Left;
            IsPressed = false;
            IsReleased = false;
            ScrollDelta = 0f;
        }
    }

    /// <summary>
    /// 键盘输入事件
    /// </summary>
    public class KeyboardInputEvent : EventBase
    {
        public KeyCode KeyCode { get; set; }
        public bool IsPressed { get; set; }
        public bool IsReleased { get; set; }
        public bool IsHeld { get; set; }
        public bool CtrlPressed { get; set; }
        public bool ShiftPressed { get; set; }
        public bool AltPressed { get; set; }

        public KeyboardInputEvent(KeyCode keyCode, bool isPressed = false, bool isReleased = false, string source = "InputManager")
            : base(source)
        {
            KeyCode = keyCode;
            IsPressed = isPressed;
            IsReleased = isReleased;
        }

        public override void Reset()
        {
            base.Reset();
            KeyCode = KeyCode.None;
            IsPressed = false;
            IsReleased = false;
            IsHeld = false;
            CtrlPressed = false;
            ShiftPressed = false;
            AltPressed = false;
        }
    }
}
