using System;
using System.Collections;
using System.Diagnostics;
using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using BlastingDesign.Events.Core;
using BlastingDesign.UI.Core;

namespace BlastingDesign.Events.Tests
{
    /// <summary>
    /// 事件系统性能基准测试
    /// 对比新旧事件系统的性能表现
    /// </summary>
    public class EventSystemPerformanceTests
    {
        private EventBus _newEventBus;
        private UIEventSystem _legacyEventSystem;
        private HighPerformanceEventDispatcher _highPerfDispatcher;
        private OptimizedStringEventSystem _stringEventSystem;
        
        private const int PERFORMANCE_TEST_ITERATIONS = 10000;
        private const int SUBSCRIBER_COUNT = 100;
        
        [SetUp]
        public void SetUp()
        {
            _newEventBus = new EventBus(useObjectPool: true, debugMode: false, enablePerformanceMonitoring: true);
            _highPerfDispatcher = new HighPerformanceEventDispatcher(false);
            _stringEventSystem = new OptimizedStringEventSystem(false);
            
            // 创建旧系统实例
            var legacyGameObject = new GameObject("LegacyEventSystem");
            _legacyEventSystem = legacyGameObject.AddComponent<UIEventSystem>();
        }
        
        [TearDown]
        public void TearDown()
        {
            _newEventBus?.Dispose();
            _highPerfDispatcher?.Dispose();
            _stringEventSystem?.Dispose();
            
            if (_legacyEventSystem != null)
            {
                UnityEngine.Object.DestroyImmediate(_legacyEventSystem.gameObject);
            }
        }
        
        [Test]
        public void PerformanceTest_EventSubscription_NewVsLegacy()
        {
            // 测试新系统订阅性能
            var stopwatch = Stopwatch.StartNew();
            
            for (int i = 0; i < SUBSCRIBER_COUNT; i++)
            {
                _newEventBus.Subscribe<MenuItemClickedEvent>(evt => { });
            }
            
            stopwatch.Stop();
            double newSystemSubscriptionTime = stopwatch.Elapsed.TotalMilliseconds;
            
            // 测试旧系统订阅性能
            stopwatch.Restart();
            
            for (int i = 0; i < SUBSCRIBER_COUNT; i++)
            {
                _legacyEventSystem.Toolbar.OnMenuItemClicked.AddListener(menuItem => { });
            }
            
            stopwatch.Stop();
            double legacySystemSubscriptionTime = stopwatch.Elapsed.TotalMilliseconds;
            
            UnityEngine.Debug.Log($"订阅性能对比 ({SUBSCRIBER_COUNT}个订阅者):");
            UnityEngine.Debug.Log($"新系统: {newSystemSubscriptionTime:F2}ms");
            UnityEngine.Debug.Log($"旧系统: {legacySystemSubscriptionTime:F2}ms");
            UnityEngine.Debug.Log($"性能提升: {(legacySystemSubscriptionTime / newSystemSubscriptionTime):F2}x");
            
            // 新系统应该更快
            Assert.Less(newSystemSubscriptionTime, legacySystemSubscriptionTime * 2, "新系统订阅性能应该不超过旧系统的2倍");
        }
        
        [Test]
        public void PerformanceTest_EventPublishing_NewVsLegacy()
        {
            // 设置订阅者
            int eventCount = 0;
            
            // 新系统设置
            for (int i = 0; i < SUBSCRIBER_COUNT; i++)
            {
                _newEventBus.Subscribe<MenuItemClickedEvent>(evt => eventCount++);
            }
            
            // 旧系统设置
            for (int i = 0; i < SUBSCRIBER_COUNT; i++)
            {
                _legacyEventSystem.Toolbar.OnMenuItemClicked.AddListener(menuItem => eventCount++);
            }
            
            // 测试新系统发布性能
            eventCount = 0;
            var stopwatch = Stopwatch.StartNew();
            
            for (int i = 0; i < PERFORMANCE_TEST_ITERATIONS; i++)
            {
                _newEventBus.Publish(new MenuItemClickedEvent($"Test{i}"));
            }
            
            stopwatch.Stop();
            double newSystemPublishTime = stopwatch.Elapsed.TotalMilliseconds;
            int newSystemEventCount = eventCount;
            
            // 测试旧系统发布性能
            eventCount = 0;
            stopwatch.Restart();
            
            for (int i = 0; i < PERFORMANCE_TEST_ITERATIONS; i++)
            {
                _legacyEventSystem.Toolbar.OnMenuItemClicked?.Invoke($"Test{i}");
            }
            
            stopwatch.Stop();
            double legacySystemPublishTime = stopwatch.Elapsed.TotalMilliseconds;
            int legacySystemEventCount = eventCount;
            
            UnityEngine.Debug.Log($"发布性能对比 ({PERFORMANCE_TEST_ITERATIONS}次发布, {SUBSCRIBER_COUNT}个订阅者):");
            UnityEngine.Debug.Log($"新系统: {newSystemPublishTime:F2}ms, 处理事件: {newSystemEventCount}");
            UnityEngine.Debug.Log($"旧系统: {legacySystemPublishTime:F2}ms, 处理事件: {legacySystemEventCount}");
            UnityEngine.Debug.Log($"性能提升: {(legacySystemPublishTime / newSystemPublishTime):F2}x");
            
            // 验证事件数量正确
            Assert.AreEqual(PERFORMANCE_TEST_ITERATIONS * SUBSCRIBER_COUNT, newSystemEventCount, "新系统事件处理数量应该正确");
            Assert.AreEqual(PERFORMANCE_TEST_ITERATIONS * SUBSCRIBER_COUNT, legacySystemEventCount, "旧系统事件处理数量应该正确");
        }
        
        [Test]
        public void PerformanceTest_HighPerformanceDispatcher()
        {
            // 设置订阅者
            int eventCount = 0;
            
            for (int i = 0; i < SUBSCRIBER_COUNT; i++)
            {
                _highPerfDispatcher.Subscribe<MenuItemClickedEvent>(evt => eventCount++);
            }
            
            // 测试高性能分发器
            var stopwatch = Stopwatch.StartNew();
            
            for (int i = 0; i < PERFORMANCE_TEST_ITERATIONS; i++)
            {
                _highPerfDispatcher.Dispatch(new MenuItemClickedEvent($"Test{i}"));
            }
            
            stopwatch.Stop();
            double highPerfTime = stopwatch.Elapsed.TotalMilliseconds;
            
            UnityEngine.Debug.Log($"高性能分发器测试 ({PERFORMANCE_TEST_ITERATIONS}次发布, {SUBSCRIBER_COUNT}个订阅者):");
            UnityEngine.Debug.Log($"执行时间: {highPerfTime:F2}ms");
            UnityEngine.Debug.Log($"处理事件: {eventCount}");
            UnityEngine.Debug.Log($"平均每次发布: {(highPerfTime / PERFORMANCE_TEST_ITERATIONS):F4}ms");
            
            Assert.AreEqual(PERFORMANCE_TEST_ITERATIONS * SUBSCRIBER_COUNT, eventCount, "高性能分发器事件处理数量应该正确");
            Assert.Less(highPerfTime, 1000, "高性能分发器应该在1秒内完成测试");
        }
        
        [Test]
        public void PerformanceTest_OptimizedStringEventSystem()
        {
            // 设置订阅者
            int eventCount = 0;
            
            for (int i = 0; i < SUBSCRIBER_COUNT; i++)
            {
                _stringEventSystem.Subscribe("TestEvent", eventName => eventCount++);
            }
            
            // 测试优化的字符串事件系统
            var stopwatch = Stopwatch.StartNew();
            
            for (int i = 0; i < PERFORMANCE_TEST_ITERATIONS; i++)
            {
                _stringEventSystem.Trigger("TestEvent");
            }
            
            stopwatch.Stop();
            double stringEventTime = stopwatch.Elapsed.TotalMilliseconds;
            
            UnityEngine.Debug.Log($"优化字符串事件系统测试 ({PERFORMANCE_TEST_ITERATIONS}次触发, {SUBSCRIBER_COUNT}个订阅者):");
            UnityEngine.Debug.Log($"执行时间: {stringEventTime:F2}ms");
            UnityEngine.Debug.Log($"处理事件: {eventCount}");
            UnityEngine.Debug.Log($"平均每次触发: {(stringEventTime / PERFORMANCE_TEST_ITERATIONS):F4}ms");
            
            Assert.AreEqual(PERFORMANCE_TEST_ITERATIONS * SUBSCRIBER_COUNT, eventCount, "字符串事件系统事件处理数量应该正确");
            Assert.Less(stringEventTime, 500, "字符串事件系统应该在0.5秒内完成测试");
        }
        
        [Test]
        public void PerformanceTest_EventPooling()
        {
            // 测试对象池化的性能影响
            var eventBusWithPool = new EventBus(useObjectPool: true, debugMode: false);
            var eventBusWithoutPool = new EventBus(useObjectPool: false, debugMode: false);
            
            try
            {
                // 设置订阅者
                eventBusWithPool.Subscribe<MenuItemClickedEvent>(evt => { });
                eventBusWithoutPool.Subscribe<MenuItemClickedEvent>(evt => { });
                
                // 测试带对象池的性能
                var stopwatch = Stopwatch.StartNew();
                
                for (int i = 0; i < PERFORMANCE_TEST_ITERATIONS; i++)
                {
                    eventBusWithPool.Publish(new MenuItemClickedEvent($"Test{i}"));
                }
                
                stopwatch.Stop();
                double withPoolTime = stopwatch.Elapsed.TotalMilliseconds;
                
                // 测试不带对象池的性能
                stopwatch.Restart();
                
                for (int i = 0; i < PERFORMANCE_TEST_ITERATIONS; i++)
                {
                    eventBusWithoutPool.Publish(new MenuItemClickedEvent($"Test{i}"));
                }
                
                stopwatch.Stop();
                double withoutPoolTime = stopwatch.Elapsed.TotalMilliseconds;
                
                UnityEngine.Debug.Log($"对象池性能对比 ({PERFORMANCE_TEST_ITERATIONS}次发布):");
                UnityEngine.Debug.Log($"带对象池: {withPoolTime:F2}ms");
                UnityEngine.Debug.Log($"不带对象池: {withoutPoolTime:F2}ms");
                UnityEngine.Debug.Log($"性能差异: {Math.Abs(withPoolTime - withoutPoolTime):F2}ms");
                
                // 对象池可能会有轻微的性能开销，但应该在合理范围内
                Assert.Less(Math.Abs(withPoolTime - withoutPoolTime), withoutPoolTime * 0.5, "对象池的性能影响应该在50%以内");
            }
            finally
            {
                eventBusWithPool?.Dispose();
                eventBusWithoutPool?.Dispose();
            }
        }
        
        [Test]
        public void PerformanceTest_FilteredEvents()
        {
            // 测试过滤事件的性能
            int filteredEventCount = 0;
            int totalEventCount = 0;
            
            // 设置过滤订阅者（只处理包含"Important"的事件）
            _newEventBus.Subscribe<MenuItemClickedEvent>(
                evt => filteredEventCount++,
                evt => evt.MenuItem.Contains("Important")
            );
            
            // 设置普通订阅者
            _newEventBus.Subscribe<MenuItemClickedEvent>(evt => totalEventCount++);
            
            var stopwatch = Stopwatch.StartNew();
            
            // 发布混合事件（一半重要，一半普通）
            for (int i = 0; i < PERFORMANCE_TEST_ITERATIONS; i++)
            {
                string menuItem = i % 2 == 0 ? $"Important{i}" : $"Normal{i}";
                _newEventBus.Publish(new MenuItemClickedEvent(menuItem));
            }
            
            stopwatch.Stop();
            double filteredEventTime = stopwatch.Elapsed.TotalMilliseconds;
            
            UnityEngine.Debug.Log($"过滤事件性能测试 ({PERFORMANCE_TEST_ITERATIONS}次发布):");
            UnityEngine.Debug.Log($"执行时间: {filteredEventTime:F2}ms");
            UnityEngine.Debug.Log($"过滤事件处理: {filteredEventCount}");
            UnityEngine.Debug.Log($"总事件处理: {totalEventCount}");
            
            Assert.AreEqual(PERFORMANCE_TEST_ITERATIONS / 2, filteredEventCount, "过滤事件数量应该是总数的一半");
            Assert.AreEqual(PERFORMANCE_TEST_ITERATIONS, totalEventCount, "总事件数量应该正确");
        }
    }
}
