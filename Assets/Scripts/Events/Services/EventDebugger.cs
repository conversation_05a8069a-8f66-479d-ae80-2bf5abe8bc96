using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using BlastingDesign.Events.Core;

namespace BlastingDesign.Events.Services
{
    /// <summary>
    /// 事件调试器
    /// 提供事件流可视化、跟踪和调试功能
    /// </summary>
    public class EventDebugger : IDisposable
    {
        private readonly List<EventTraceRecord> _eventTrace;
        private readonly Dictionary<Type, EventTypeDebugInfo> _eventTypeInfo;
        private readonly Dictionary<string, List<EventTraceRecord>> _eventSourceTrace;
        private readonly object _lockObject = new object();

        // 配置
        private readonly int _maxTraceRecords;
        private readonly bool _enableDetailedLogging;
        private readonly bool _trackEventSources;

        // 过滤设置
        private HashSet<Type> _filteredEventTypes;
        private HashSet<string> _filteredSources;
        private bool _enableFiltering = false;

        public EventDebugger(int maxTraceRecords = 1000, bool enableDetailedLogging = true, bool trackEventSources = true)
        {
            _eventTrace = new List<EventTraceRecord>();
            _eventTypeInfo = new Dictionary<Type, EventTypeDebugInfo>();
            _eventSourceTrace = new Dictionary<string, List<EventTraceRecord>>();
            _maxTraceRecords = maxTraceRecords;
            _enableDetailedLogging = enableDetailedLogging;
            _trackEventSources = trackEventSources;

            _filteredEventTypes = new HashSet<Type>();
            _filteredSources = new HashSet<string>();
        }

        /// <summary>
        /// 记录事件发布
        /// </summary>
        /// <param name="eventData">事件数据</param>
        /// <param name="subscriberCount">订阅者数量</param>
        /// <param name="processingTime">处理时间（毫秒）</param>
        public void RecordEventPublish(IEvent eventData, int subscriberCount, double processingTime)
        {
            if (eventData == null) return;

            Type eventType = eventData.GetType();

            // 应用过滤器
            if (_enableFiltering)
            {
                if (_filteredEventTypes.Contains(eventType) ||
                    _filteredSources.Contains(eventData.Source))
                {
                    return;
                }
            }

            var traceRecord = new EventTraceRecord
            {
                EventId = eventData.EventId,
                EventType = eventType,
                EventTypeName = eventType.Name,
                Source = eventData.Source,
                Timestamp = eventData.Timestamp,
                SubscriberCount = subscriberCount,
                ProcessingTime = processingTime,
                WasCancelled = eventData.IsCancelled,
                WasHandled = eventData.IsHandled
            };

            lock (_lockObject)
            {
                // 添加到主跟踪列表
                _eventTrace.Add(traceRecord);

                // 限制跟踪记录数量
                if (_eventTrace.Count > _maxTraceRecords)
                {
                    _eventTrace.RemoveAt(0);
                }

                // 更新事件类型信息
                UpdateEventTypeInfo(eventType, traceRecord);

                // 按源跟踪事件
                if (_trackEventSources)
                {
                    UpdateEventSourceTrace(eventData.Source, traceRecord);
                }

                if (_enableDetailedLogging)
                {
                    Debug.Log($"EventDebugger: 记录事件 {eventType.Name} (ID: {eventData.EventId}, 源: {eventData.Source}, 订阅者: {subscriberCount}, 处理时间: {processingTime:F2}ms)");
                }
            }
        }

        /// <summary>
        /// 记录事件订阅
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="subscriberInfo">订阅者信息</param>
        public void RecordEventSubscription(Type eventType, string subscriberInfo)
        {
            if (eventType == null) return;

            lock (_lockObject)
            {
                if (!_eventTypeInfo.TryGetValue(eventType, out EventTypeDebugInfo debugInfo))
                {
                    debugInfo = new EventTypeDebugInfo(eventType);
                    _eventTypeInfo[eventType] = debugInfo;
                }

                debugInfo.Subscribers.Add(subscriberInfo);
                debugInfo.SubscriptionCount++;

                if (_enableDetailedLogging)
                {
                    Debug.Log($"EventDebugger: 记录订阅 {eventType.Name} - {subscriberInfo}");
                }
            }
        }

        /// <summary>
        /// 记录事件取消订阅
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="subscriberInfo">订阅者信息</param>
        public void RecordEventUnsubscription(Type eventType, string subscriberInfo)
        {
            if (eventType == null) return;

            lock (_lockObject)
            {
                if (_eventTypeInfo.TryGetValue(eventType, out EventTypeDebugInfo debugInfo))
                {
                    debugInfo.Subscribers.Remove(subscriberInfo);
                    debugInfo.UnsubscriptionCount++;

                    if (_enableDetailedLogging)
                    {
                        Debug.Log($"EventDebugger: 记录取消订阅 {eventType.Name} - {subscriberInfo}");
                    }
                }
            }
        }

        /// <summary>
        /// 获取事件跟踪记录
        /// </summary>
        /// <param name="count">获取数量，-1表示全部</param>
        /// <returns>事件跟踪记录列表</returns>
        public List<EventTraceRecord> GetEventTrace(int count = -1)
        {
            lock (_lockObject)
            {
                if (count == -1 || count >= _eventTrace.Count)
                {
                    return new List<EventTraceRecord>(_eventTrace);
                }

                return _eventTrace.Skip(_eventTrace.Count - count).ToList();
            }
        }

        /// <summary>
        /// 获取指定事件类型的跟踪记录
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="count">获取数量</param>
        /// <returns>事件跟踪记录列表</returns>
        public List<EventTraceRecord> GetEventTraceByType(Type eventType, int count = -1)
        {
            lock (_lockObject)
            {
                var filteredTrace = _eventTrace.Where(record => record.EventType == eventType).ToList();

                if (count == -1 || count >= filteredTrace.Count)
                {
                    return filteredTrace;
                }

                return filteredTrace.Skip(filteredTrace.Count - count).ToList();
            }
        }

        /// <summary>
        /// 获取指定源的事件跟踪记录
        /// </summary>
        /// <param name="source">事件源</param>
        /// <param name="count">获取数量</param>
        /// <returns>事件跟踪记录列表</returns>
        public List<EventTraceRecord> GetEventTraceBySource(string source, int count = -1)
        {
            if (string.IsNullOrEmpty(source)) return new List<EventTraceRecord>();

            lock (_lockObject)
            {
                if (_eventSourceTrace.TryGetValue(source, out List<EventTraceRecord> sourceTrace))
                {
                    if (count == -1 || count >= sourceTrace.Count)
                    {
                        return new List<EventTraceRecord>(sourceTrace);
                    }

                    return sourceTrace.Skip(sourceTrace.Count - count).ToList();
                }
            }

            return new List<EventTraceRecord>();
        }

        /// <summary>
        /// 获取事件类型调试信息
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <returns>事件类型调试信息</returns>
        public EventTypeDebugInfo GetEventTypeDebugInfo(Type eventType)
        {
            lock (_lockObject)
            {
                _eventTypeInfo.TryGetValue(eventType, out EventTypeDebugInfo debugInfo);
                return debugInfo;
            }
        }

        /// <summary>
        /// 获取所有事件类型调试信息
        /// </summary>
        /// <returns>事件类型调试信息列表</returns>
        public List<EventTypeDebugInfo> GetAllEventTypeDebugInfo()
        {
            lock (_lockObject)
            {
                return _eventTypeInfo.Values.ToList();
            }
        }

        /// <summary>
        /// 生成事件流报告
        /// </summary>
        /// <returns>事件流报告字符串</returns>
        public string GenerateEventFlowReport()
        {
            lock (_lockObject)
            {
                var report = new StringBuilder();
                report.AppendLine("=== 事件流调试报告 ===");
                report.AppendLine($"跟踪记录数量: {_eventTrace.Count}");
                report.AppendLine($"事件类型数量: {_eventTypeInfo.Count}");
                report.AppendLine();

                // 最近的事件
                report.AppendLine("=== 最近的事件 (最多10个) ===");
                var recentEvents = _eventTrace.TakeLast(10).ToList();
                foreach (var record in recentEvents)
                {
                    report.AppendLine($"[{record.Timestamp:HH:mm:ss.fff}] {record.EventTypeName} (源: {record.Source}, 订阅者: {record.SubscriberCount}, 处理时间: {record.ProcessingTime:F2}ms)");
                }
                report.AppendLine();

                // 事件类型统计
                report.AppendLine("=== 事件类型统计 ===");
                var sortedEventTypes = _eventTypeInfo.Values
                    .OrderByDescending(info => info.PublishCount)
                    .Take(10);

                foreach (var eventInfo in sortedEventTypes)
                {
                    report.AppendLine($"{eventInfo.EventType.Name}: 发布 {eventInfo.PublishCount} 次, 平均处理时间 {eventInfo.AverageProcessingTime:F2}ms, 当前订阅者 {eventInfo.Subscribers.Count}");
                }

                return report.ToString();
            }
        }

        /// <summary>
        /// 设置事件类型过滤器
        /// </summary>
        /// <param name="eventTypes">要过滤的事件类型</param>
        public void SetEventTypeFilter(params Type[] eventTypes)
        {
            lock (_lockObject)
            {
                _filteredEventTypes.Clear();
                if (eventTypes != null)
                {
                    foreach (var eventType in eventTypes)
                    {
                        _filteredEventTypes.Add(eventType);
                    }
                }
                _enableFiltering = _filteredEventTypes.Count > 0 || _filteredSources.Count > 0;
            }
        }

        /// <summary>
        /// 设置事件源过滤器
        /// </summary>
        /// <param name="sources">要过滤的事件源</param>
        public void SetEventSourceFilter(params string[] sources)
        {
            lock (_lockObject)
            {
                _filteredSources.Clear();
                if (sources != null)
                {
                    foreach (var source in sources)
                    {
                        if (!string.IsNullOrEmpty(source))
                        {
                            _filteredSources.Add(source);
                        }
                    }
                }
                _enableFiltering = _filteredEventTypes.Count > 0 || _filteredSources.Count > 0;
            }
        }

        /// <summary>
        /// 清除所有过滤器
        /// </summary>
        public void ClearFilters()
        {
            lock (_lockObject)
            {
                _filteredEventTypes.Clear();
                _filteredSources.Clear();
                _enableFiltering = false;
            }
        }

        /// <summary>
        /// 清除所有跟踪记录
        /// </summary>
        public void ClearTrace()
        {
            lock (_lockObject)
            {
                _eventTrace.Clear();
                _eventTypeInfo.Clear();
                _eventSourceTrace.Clear();

                if (_enableDetailedLogging)
                {
                    Debug.Log("EventDebugger: 清除所有跟踪记录");
                }
            }
        }

        #region 私有辅助方法

        private void UpdateEventTypeInfo(Type eventType, EventTraceRecord record)
        {
            if (!_eventTypeInfo.TryGetValue(eventType, out EventTypeDebugInfo debugInfo))
            {
                debugInfo = new EventTypeDebugInfo(eventType);
                _eventTypeInfo[eventType] = debugInfo;
            }

            debugInfo.PublishCount++;
            debugInfo.TotalProcessingTime += record.ProcessingTime;
            debugInfo.LastPublishTime = record.Timestamp;

            if (record.WasCancelled)
            {
                debugInfo.CancelledCount++;
            }
        }

        private void UpdateEventSourceTrace(string source, EventTraceRecord record)
        {
            if (!_eventSourceTrace.TryGetValue(source, out List<EventTraceRecord> sourceTrace))
            {
                sourceTrace = new List<EventTraceRecord>();
                _eventSourceTrace[source] = sourceTrace;
            }

            sourceTrace.Add(record);

            // 限制每个源的跟踪记录数量
            if (sourceTrace.Count > _maxTraceRecords / 10) // 每个源最多保存总数的1/10
            {
                sourceTrace.RemoveAt(0);
            }
        }

        #endregion

        public void Dispose()
        {
            ClearTrace();
        }
    }

    /// <summary>
    /// 事件跟踪记录
    /// </summary>
    public class EventTraceRecord
    {
        public string EventId { get; set; }
        public Type EventType { get; set; }
        public string EventTypeName { get; set; }
        public string Source { get; set; }
        public DateTime Timestamp { get; set; }
        public int SubscriberCount { get; set; }
        public double ProcessingTime { get; set; }
        public bool WasCancelled { get; set; }
        public bool WasHandled { get; set; }
    }

    /// <summary>
    /// 事件类型调试信息
    /// </summary>
    public class EventTypeDebugInfo
    {
        public Type EventType { get; set; }
        public long PublishCount { get; set; }
        public long CancelledCount { get; set; }
        public double TotalProcessingTime { get; set; }
        public DateTime LastPublishTime { get; set; }
        public int SubscriptionCount { get; set; }
        public int UnsubscriptionCount { get; set; }
        public HashSet<string> Subscribers { get; set; }

        public double AverageProcessingTime => PublishCount > 0 ? TotalProcessingTime / PublishCount : 0;
        public double CancellationRate => PublishCount > 0 ? (double)CancelledCount / PublishCount : 0;

        public EventTypeDebugInfo(Type eventType)
        {
            EventType = eventType;
            Subscribers = new HashSet<string>();
            LastPublishTime = DateTime.MinValue;
        }
    }
}
