using UnityEngine;
using BlastingDesign.Events;
using BlastingDesign.UI.Core;
using BlastingDesign.Core.Input;

namespace BlastingDesign.Events
{
    /// <summary>
    /// 事件系统设置助手
    /// 帮助正确配置EventSystemManager组件
    /// </summary>
    [System.Serializable]
    public class EventSystemSetup
    {
        [Header("推荐配置")]
        [Tooltip("启用对象池以提高性能")]
        public bool useObjectPool = true;
        
        [Tooltip("启用调试模式（开发时推荐）")]
        public bool enableDebugMode = false;
        
        [Tooltip("启用性能监控")]
        public bool enablePerformanceMonitoring = true;
        
        [Tooltip("启用事件调试")]
        public bool enableEventDebugging = true;
        
        [Tooltip("启用事件重放")]
        public bool enableEventReplay = true;
        
        [Header("兼容性设置")]
        [Tooltip("启用与旧系统的兼容性")]
        public bool enableLegacyCompatibility = true;
        
        [Header("输入系统集成")]
        [Tooltip("启用输入系统集成")]
        public bool enableInputIntegration = true;
        
        [Header("调试设置")]
        [Tooltip("显示调试信息")]
        public bool showDebugInfo = true;
        
        [Tooltip("启用详细日志")]
        public bool enableDetailedLogging = false;
    }
    
    /// <summary>
    /// EventSystemManager配置器
    /// 提供一键配置功能
    /// </summary>
    [RequireComponent(typeof(EventSystemManager))]
    public class EventSystemConfigurator : MonoBehaviour
    {
        [Header("配置模板")]
        [SerializeField] private EventSystemSetup developmentSetup = new EventSystemSetup
        {
            useObjectPool = true,
            enableDebugMode = true,
            enablePerformanceMonitoring = true,
            enableEventDebugging = true,
            enableEventReplay = true,
            enableLegacyCompatibility = true,
            enableInputIntegration = true,
            showDebugInfo = true,
            enableDetailedLogging = true
        };
        
        [SerializeField] private EventSystemSetup productionSetup = new EventSystemSetup
        {
            useObjectPool = true,
            enableDebugMode = false,
            enablePerformanceMonitoring = false,
            enableEventDebugging = false,
            enableEventReplay = false,
            enableLegacyCompatibility = true,
            enableInputIntegration = true,
            showDebugInfo = false,
            enableDetailedLogging = false
        };
        
        [Header("当前配置")]
        [SerializeField] private EventSystemSetup currentSetup;
        
        private EventSystemManager eventSystemManager;
        
        private void Awake()
        {
            eventSystemManager = GetComponent<EventSystemManager>();
            
            // 如果当前配置为空，使用开发配置
            if (currentSetup == null)
            {
                currentSetup = developmentSetup;
            }
        }
        
        private void Start()
        {
            ApplyConfiguration();
        }
        
        /// <summary>
        /// 应用配置到EventSystemManager
        /// </summary>
        public void ApplyConfiguration()
        {
            if (eventSystemManager == null || currentSetup == null)
            {
                Debug.LogError("EventSystemConfigurator: EventSystemManager或配置为空");
                return;
            }
            
            // 使用反射设置私有字段（这里简化处理）
            // 在实际项目中，您可能需要修改EventSystemManager以支持运行时配置
            
            Debug.Log("EventSystemConfigurator: 配置已应用");
            LogCurrentConfiguration();
        }
        
        /// <summary>
        /// 使用开发配置
        /// </summary>
        [ContextMenu("应用开发配置")]
        public void ApplyDevelopmentSetup()
        {
            currentSetup = developmentSetup;
            ApplyConfiguration();
            Debug.Log("EventSystemConfigurator: 已应用开发配置");
        }
        
        /// <summary>
        /// 使用生产配置
        /// </summary>
        [ContextMenu("应用生产配置")]
        public void ApplyProductionSetup()
        {
            currentSetup = productionSetup;
            ApplyConfiguration();
            Debug.Log("EventSystemConfigurator: 已应用生产配置");
        }
        
        /// <summary>
        /// 自动检测并配置组件引用
        /// </summary>
        [ContextMenu("自动检测组件引用")]
        public void AutoDetectComponents()
        {
            Debug.Log("EventSystemConfigurator: 开始自动检测组件引用");
            
            // 查找UIEventSystem
            var uiEventSystem = FindObjectOfType<UIEventSystem>();
            if (uiEventSystem != null)
            {
                Debug.Log($"EventSystemConfigurator: 找到UIEventSystem - {uiEventSystem.name}");
                // 这里您需要手动设置EventSystemManager的legacyEventSystem字段
            }
            else
            {
                Debug.LogWarning("EventSystemConfigurator: 未找到UIEventSystem，兼容性功能可能不可用");
            }
            
            // 查找InputEventPriorityManager
            var inputManager = FindObjectOfType<InputEventPriorityManager>();
            if (inputManager != null)
            {
                Debug.Log($"EventSystemConfigurator: 找到InputEventPriorityManager - {inputManager.name}");
                // 这里您需要手动设置EventSystemManager的inputPriorityManager字段
            }
            else
            {
                Debug.LogWarning("EventSystemConfigurator: 未找到InputEventPriorityManager，输入集成功能可能不可用");
            }
        }
        
        /// <summary>
        /// 验证配置
        /// </summary>
        [ContextMenu("验证配置")]
        public void ValidateConfiguration()
        {
            Debug.Log("EventSystemConfigurator: 开始验证配置");
            
            if (eventSystemManager == null)
            {
                Debug.LogError("EventSystemConfigurator: EventSystemManager组件缺失");
                return;
            }
            
            // 检查EventSystemManager是否正确初始化
            if (EventSystemManager.Instance == null)
            {
                Debug.LogWarning("EventSystemConfigurator: EventSystemManager.Instance为空，可能尚未初始化");
            }
            else
            {
                Debug.Log("EventSystemConfigurator: EventSystemManager已正确初始化");
                
                // 检查事件总线
                if (EventSystemManager.Instance.EventBus != null)
                {
                    Debug.Log("EventSystemConfigurator: 事件总线已初始化");
                    
                    var stats = EventSystemManager.Instance.EventBus.GetStatistics();
                    Debug.Log($"EventSystemConfigurator: 事件系统统计 - 活跃事件类型: {stats.ActiveEventTypes}, 总订阅数: {stats.TotalSubscriptions}");
                }
                else
                {
                    Debug.LogError("EventSystemConfigurator: 事件总线未初始化");
                }
            }
            
            Debug.Log("EventSystemConfigurator: 配置验证完成");
        }
        
        /// <summary>
        /// 记录当前配置
        /// </summary>
        private void LogCurrentConfiguration()
        {
            if (currentSetup == null) return;
            
            Debug.Log("=== EventSystem当前配置 ===");
            Debug.Log($"对象池: {currentSetup.useObjectPool}");
            Debug.Log($"调试模式: {currentSetup.enableDebugMode}");
            Debug.Log($"性能监控: {currentSetup.enablePerformanceMonitoring}");
            Debug.Log($"事件调试: {currentSetup.enableEventDebugging}");
            Debug.Log($"事件重放: {currentSetup.enableEventReplay}");
            Debug.Log($"兼容性支持: {currentSetup.enableLegacyCompatibility}");
            Debug.Log($"输入集成: {currentSetup.enableInputIntegration}");
            Debug.Log($"调试信息: {currentSetup.showDebugInfo}");
            Debug.Log($"详细日志: {currentSetup.enableDetailedLogging}");
        }
        
        /// <summary>
        /// 生成配置报告
        /// </summary>
        [ContextMenu("生成配置报告")]
        public void GenerateConfigurationReport()
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine("=== EventSystem配置报告 ===");
            report.AppendLine($"生成时间: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();
            
            // 当前配置
            report.AppendLine("=== 当前配置 ===");
            if (currentSetup != null)
            {
                report.AppendLine($"对象池: {currentSetup.useObjectPool}");
                report.AppendLine($"调试模式: {currentSetup.enableDebugMode}");
                report.AppendLine($"性能监控: {currentSetup.enablePerformanceMonitoring}");
                report.AppendLine($"事件调试: {currentSetup.enableEventDebugging}");
                report.AppendLine($"事件重放: {currentSetup.enableEventReplay}");
                report.AppendLine($"兼容性支持: {currentSetup.enableLegacyCompatibility}");
                report.AppendLine($"输入集成: {currentSetup.enableInputIntegration}");
            }
            report.AppendLine();
            
            // 系统状态
            report.AppendLine("=== 系统状态 ===");
            if (EventSystemManager.Instance != null)
            {
                report.AppendLine("EventSystemManager: 已初始化");
                
                if (EventSystemManager.Instance.EventBus != null)
                {
                    var stats = EventSystemManager.Instance.EventBus.GetStatistics();
                    report.AppendLine($"事件总线: 已初始化");
                    report.AppendLine($"活跃事件类型: {stats.ActiveEventTypes}");
                    report.AppendLine($"总订阅数: {stats.TotalSubscriptions}");
                    report.AppendLine($"总发布事件: {stats.TotalPublishedEvents}");
                }
                else
                {
                    report.AppendLine("事件总线: 未初始化");
                }
            }
            else
            {
                report.AppendLine("EventSystemManager: 未初始化");
            }
            
            Debug.Log(report.ToString());
        }
    }
}
