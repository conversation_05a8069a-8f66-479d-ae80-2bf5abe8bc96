using UnityEngine;
using BlastingDesign.Events.Core;
using BlastingDesign.Events.Compatibility;
using BlastingDesign.UI.Core;

namespace BlastingDesign.Events
{
    /// <summary>
    /// 事件系统迁移助手
    /// 帮助从旧事件系统平滑迁移到新事件系统
    /// </summary>
    public class MigrationHelper : MonoBehaviour
    {
        [Header("迁移配置")]
        [SerializeField] private bool enableMigrationMode = true;
        [SerializeField] private bool showMigrationLogs = true;
        [SerializeField] private bool validateEventSystemSetup = true;

        [Header("组件引用")]
        [SerializeField] private EventSystemManager eventSystemManager;
        [SerializeField] private UIEventSystem legacyEventSystem;

        private void Start()
        {
            if (enableMigrationMode)
            {
                StartMigrationProcess();
            }
        }

        private void StartMigrationProcess()
        {
            LogMigrationStep("开始事件系统迁移过程");

            // 步骤1：验证系统设置
            if (validateEventSystemSetup)
            {
                ValidateSystemSetup();
            }

            // 步骤2：设置兼容性适配器
            SetupCompatibilityAdapter();

            // 步骤3：验证兼容性
            TestCompatibility();

            // 步骤4：显示迁移状态
            ShowMigrationStatus();

            LogMigrationStep("事件系统迁移过程完成");
        }

        private void ValidateSystemSetup()
        {
            LogMigrationStep("验证系统设置");

            // 检查EventSystemManager
            if (eventSystemManager == null)
            {
                eventSystemManager = EventSystemManager.Instance;
            }

            if (eventSystemManager == null)
            {
                LogMigrationError("未找到EventSystemManager！请确保场景中已添加EventSystemManager组件。");
                return;
            }

            LogMigrationSuccess("EventSystemManager已找到并正常工作");

            // 检查旧事件系统
            if (legacyEventSystem == null)
            {
                legacyEventSystem = UIEventSystem.Instance;
            }

            if (legacyEventSystem == null)
            {
                LogMigrationWarning("未找到UIEventSystem，将跳过兼容性设置");
            }
            else
            {
                LogMigrationSuccess("UIEventSystem已找到，兼容性模式可用");
            }

            // 检查新事件系统状态
            if (eventSystemManager.EventBus != null)
            {
                LogMigrationSuccess("新事件总线已初始化");

                var stats = eventSystemManager.EventBus.GetStatistics();
                LogMigrationInfo($"事件系统统计: 活跃事件类型 {stats.ActiveEventTypes}, 总订阅数 {stats.TotalSubscriptions}");
            }
        }

        private void SetupCompatibilityAdapter()
        {
            LogMigrationStep("设置兼容性适配器");

            if (eventSystemManager == null || legacyEventSystem == null)
            {
                LogMigrationWarning("跳过兼容性适配器设置");
                return;
            }

            // 兼容性适配器应该已经在EventSystemManager中自动设置
            // 这里我们验证它是否正常工作

            try
            {
                // 测试兼容性API（迁移测试目的，抑制过时警告）
#pragma warning disable CS0618
                UIEventSystemCompat.TriggerMenuItemClicked("Migration.Test");
#pragma warning restore CS0618
                LogMigrationSuccess("兼容性适配器设置成功");
            }
            catch (System.Exception ex)
            {
                LogMigrationError($"兼容性适配器设置失败: {ex.Message}");
            }
        }

        private void TestCompatibility()
        {
            LogMigrationStep("测试兼容性");

            if (eventSystemManager == null || eventSystemManager.EventBus == null)
            {
                LogMigrationError("事件总线未初始化，跳过兼容性测试");
                return;
            }

            // 测试新系统事件发布
            bool newSystemWorking = false;
            var subscription = eventSystemManager.EventBus.Subscribe<MenuItemClickedEvent>(evt =>
            {
                newSystemWorking = true;
                LogMigrationSuccess($"新系统接收到事件: {evt.MenuItem}");
            });

            // 发布测试事件
            eventSystemManager.EventBus.Publish(new MenuItemClickedEvent("Migration.NewSystemTest"));

            if (newSystemWorking)
            {
                LogMigrationSuccess("新事件系统工作正常");
            }
            else
            {
                LogMigrationError("新事件系统测试失败");
            }

            // 清理测试订阅
            subscription?.Dispose();

            // 测试兼容性API
            if (legacyEventSystem != null)
            {
                bool legacySystemWorking = false;

                // 临时订阅旧系统事件来测试
                void TestHandler(string menuItem)
                {
                    legacySystemWorking = true;
                    LogMigrationSuccess($"兼容性系统接收到事件: {menuItem}");
                }

                legacyEventSystem.Toolbar.OnMenuItemClicked.AddListener(TestHandler);

                // 通过兼容性API触发事件
#pragma warning disable CS0618
                UIEventSystemCompat.TriggerMenuItemClicked("Migration.CompatibilityTest");
#pragma warning restore CS0618

                if (legacySystemWorking)
                {
                    LogMigrationSuccess("兼容性系统工作正常");
                }
                else
                {
                    LogMigrationWarning("兼容性系统测试未通过，但这可能是正常的");
                }

                // 清理测试订阅
                legacyEventSystem.Toolbar.OnMenuItemClicked.RemoveListener(TestHandler);
            }
        }

        private void ShowMigrationStatus()
        {
            LogMigrationStep("显示迁移状态");

            if (eventSystemManager == null || eventSystemManager.EventBus == null)
            {
                LogMigrationError("事件系统未正确初始化");
                return;
            }

            // 显示系统统计
            var stats = eventSystemManager.EventBus.GetStatistics();
            LogMigrationInfo("=== 事件系统状态 ===");
            LogMigrationInfo($"总发布事件: {stats.TotalPublishedEvents}");
            LogMigrationInfo($"总订阅数: {stats.TotalSubscriptions}");
            LogMigrationInfo($"活跃事件类型: {stats.ActiveEventTypes}");

            // 显示注册的事件类型
            if (eventSystemManager.EventTypeRegistry != null)
            {
                var registryStats = eventSystemManager.EventTypeRegistry.GetStatistics();
                LogMigrationInfo($"注册事件类型: {registryStats.TotalRegisteredTypes}");
                LogMigrationInfo($"内置事件类型: {registryStats.BuiltInEventTypes}");
                LogMigrationInfo($"自定义事件类型: {registryStats.CustomEventTypes}");
            }

            // 显示性能监控信息
            if (eventSystemManager.PerformanceMonitor != null)
            {
                var perfStats = eventSystemManager.PerformanceMonitor.GetOverallStatistics();
                LogMigrationInfo($"处理事件数: {perfStats.TotalEventsProcessed}");
                LogMigrationInfo($"平均处理时间: {perfStats.AverageProcessingTime:F2}ms");
            }

            LogMigrationSuccess("事件系统迁移完成！现在可以开始使用新系统了。");
        }

        #region 日志方法

        private void LogMigrationStep(string message)
        {
            if (showMigrationLogs)
            {
                Debug.Log($"<color=cyan>[迁移] {message}</color>");
            }
        }

        private void LogMigrationSuccess(string message)
        {
            if (showMigrationLogs)
            {
                Debug.Log($"<color=green>[迁移成功] {message}</color>");
            }
        }

        private void LogMigrationWarning(string message)
        {
            if (showMigrationLogs)
            {
                Debug.LogWarning($"<color=yellow>[迁移警告] {message}</color>");
            }
        }

        private void LogMigrationError(string message)
        {
            if (showMigrationLogs)
            {
                Debug.LogError($"<color=red>[迁移错误] {message}</color>");
            }
        }

        private void LogMigrationInfo(string message)
        {
            if (showMigrationLogs)
            {
                Debug.Log($"<color=white>[迁移信息] {message}</color>");
            }
        }

        #endregion

        #region 上下文菜单方法（用于测试）

        [ContextMenu("测试新事件系统")]
        private void TestNewEventSystem()
        {
            if (eventSystemManager != null && eventSystemManager.EventBus != null)
            {
                eventSystemManager.EventBus.Publish(new MenuItemClickedEvent("Test.NewSystem"));
                eventSystemManager.EventBus.Publish(new ToolSelectedEvent("TestTool"));
                eventSystemManager.EventBus.Publish(new StatusMessageEvent("新事件系统测试", StatusMessageType.Info));
                LogMigrationSuccess("新事件系统测试完成");
            }
            else
            {
                LogMigrationError("新事件系统未初始化");
            }
        }

        [ContextMenu("测试兼容性API")]
        private void TestCompatibilityAPI()
        {
#pragma warning disable CS0618
            UIEventSystemCompat.TriggerMenuItemClicked("Test.Compatibility");
            UIEventSystemCompat.TriggerToolSelected("TestTool");
            UIEventSystemCompat.TriggerStatusMessage("兼容性API测试");
#pragma warning restore CS0618
            LogMigrationSuccess("兼容性API测试完成");
        }

        [ContextMenu("显示系统报告")]
        private void ShowSystemReport()
        {
            if (eventSystemManager != null)
            {
                string report = eventSystemManager.GenerateSystemReport();
                Debug.Log(report);
            }
            else
            {
                LogMigrationError("EventSystemManager未找到");
            }
        }

        #endregion
    }
}
