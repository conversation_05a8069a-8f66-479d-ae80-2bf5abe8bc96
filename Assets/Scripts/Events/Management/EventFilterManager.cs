using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using BlastingDesign.Events.Core;

namespace BlastingDesign.Events.Management
{
    /// <summary>
    /// 事件过滤管理器
    /// 提供复杂的事件过滤和条件处理功能
    /// </summary>
    public class EventFilterManager : IDisposable
    {
        private readonly ConcurrentDictionary<Type, List<EventFilter>> _globalFilters;
        private readonly ConcurrentDictionary<string, List<EventFilter>> _namedFilters;
        private readonly object _lockObject = new object();

        // 配置
        private readonly bool _enableDebugLogging;
        private readonly bool _enableFilterChaining;

        // 统计信息
        private long _totalFilterExecutions = 0;
        private long _totalFilteredEvents = 0;
        private long _totalPassedEvents = 0;

        public EventFilterManager(bool enableDebugLogging = false, bool enableFilterChaining = true)
        {
            _globalFilters = new ConcurrentDictionary<Type, List<EventFilter>>();
            _namedFilters = new ConcurrentDictionary<string, List<EventFilter>>();
            _enableDebugLogging = enableDebugLogging;
            _enableFilterChaining = enableFilterChaining;
        }

        /// <summary>
        /// 添加全局事件过滤器
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="filter">过滤器函数</param>
        /// <param name="priority">优先级（数值越大优先级越高）</param>
        /// <param name="name">过滤器名称</param>
        /// <returns>过滤器ID</returns>
        public string AddFilter<T>(Func<T, bool> filter, int priority = 0, string name = null) where T : IEvent
        {
            if (filter == null) return null;

            Type eventType = typeof(T);
            var eventFilter = new EventFilter<T>(filter, priority, name ?? Guid.NewGuid().ToString());

            if (!_globalFilters.TryGetValue(eventType, out List<EventFilter> filters))
            {
                filters = new List<EventFilter>();
                _globalFilters[eventType] = filters;
            }

            lock (_lockObject)
            {
                // 按优先级插入
                int insertIndex = 0;
                for (int i = 0; i < filters.Count; i++)
                {
                    if (filters[i].Priority < priority)
                    {
                        insertIndex = i;
                        break;
                    }
                    insertIndex = i + 1;
                }

                filters.Insert(insertIndex, eventFilter);

                if (_enableDebugLogging)
                {
                    Debug.Log($"EventFilterManager: 添加全局过滤器 {eventFilter.Name} 到事件类型 {eventType.Name}，优先级: {priority}");
                }
            }

            return eventFilter.Id;
        }

        /// <summary>
        /// 添加命名过滤器组
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="groupName">过滤器组名称</param>
        /// <param name="filter">过滤器函数</param>
        /// <param name="priority">优先级</param>
        /// <param name="name">过滤器名称</param>
        /// <returns>过滤器ID</returns>
        public string AddNamedFilter<T>(string groupName, Func<T, bool> filter, int priority = 0, string name = null) where T : IEvent
        {
            if (string.IsNullOrEmpty(groupName) || filter == null) return null;

            var eventFilter = new EventFilter<T>(filter, priority, name ?? Guid.NewGuid().ToString());

            if (!_namedFilters.TryGetValue(groupName, out List<EventFilter> filters))
            {
                filters = new List<EventFilter>();
                _namedFilters[groupName] = filters;
            }

            lock (_lockObject)
            {
                // 按优先级插入
                int insertIndex = 0;
                for (int i = 0; i < filters.Count; i++)
                {
                    if (filters[i].Priority < priority)
                    {
                        insertIndex = i;
                        break;
                    }
                    insertIndex = i + 1;
                }

                filters.Insert(insertIndex, eventFilter);

                if (_enableDebugLogging)
                {
                    Debug.Log($"EventFilterManager: 添加命名过滤器 {eventFilter.Name} 到组 {groupName}，优先级: {priority}");
                }
            }

            return eventFilter.Id;
        }

        /// <summary>
        /// 移除过滤器
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="filterId">过滤器ID</param>
        /// <returns>是否移除成功</returns>
        public bool RemoveFilter<T>(string filterId) where T : IEvent
        {
            if (string.IsNullOrEmpty(filterId)) return false;

            Type eventType = typeof(T);

            if (_globalFilters.TryGetValue(eventType, out List<EventFilter> filters))
            {
                lock (_lockObject)
                {
                    for (int i = 0; i < filters.Count; i++)
                    {
                        if (filters[i].Id == filterId)
                        {
                            filters.RemoveAt(i);

                            if (_enableDebugLogging)
                            {
                                Debug.Log($"EventFilterManager: 移除全局过滤器 {filterId} 从事件类型 {eventType.Name}");
                            }

                            return true;
                        }
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 移除命名过滤器
        /// </summary>
        /// <param name="groupName">过滤器组名称</param>
        /// <param name="filterId">过滤器ID</param>
        /// <returns>是否移除成功</returns>
        public bool RemoveNamedFilter(string groupName, string filterId)
        {
            if (string.IsNullOrEmpty(groupName) || string.IsNullOrEmpty(filterId)) return false;

            if (_namedFilters.TryGetValue(groupName, out List<EventFilter> filters))
            {
                lock (_lockObject)
                {
                    for (int i = 0; i < filters.Count; i++)
                    {
                        if (filters[i].Id == filterId)
                        {
                            filters.RemoveAt(i);

                            if (_enableDebugLogging)
                            {
                                Debug.Log($"EventFilterManager: 移除命名过滤器 {filterId} 从组 {groupName}");
                            }

                            return true;
                        }
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 检查事件是否应该被处理（全局过滤器）
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        /// <returns>是否应该处理</returns>
        public bool ShouldProcess<T>(T eventData) where T : IEvent
        {
            if (eventData == null) return false;

            _totalFilterExecutions++;

            Type eventType = typeof(T);

            if (_globalFilters.TryGetValue(eventType, out List<EventFilter> filters))
            {
                List<EventFilter> currentFilters;

                lock (_lockObject)
                {
                    currentFilters = new List<EventFilter>(filters);
                }

                foreach (var filter in currentFilters)
                {
                    try
                    {
                        bool result = filter.Evaluate(eventData);

                        if (!result)
                        {
                            _totalFilteredEvents++;

                            if (_enableDebugLogging)
                            {
                                Debug.Log($"EventFilterManager: 事件 {eventType.Name} 被过滤器 {filter.Name} 阻止");
                            }

                            return false;
                        }

                        // 如果不启用过滤器链，第一个通过的过滤器就决定结果
                        if (!_enableFilterChaining)
                        {
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"EventFilterManager: 过滤器 {filter.Name} 执行时发生错误: {ex.Message}");
                    }
                }
            }

            _totalPassedEvents++;
            return true;
        }

        /// <summary>
        /// 检查事件是否应该被处理（命名过滤器组）
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="groupName">过滤器组名称</param>
        /// <param name="eventData">事件数据</param>
        /// <returns>是否应该处理</returns>
        public bool ShouldProcessWithNamedFilters<T>(string groupName, T eventData) where T : IEvent
        {
            if (string.IsNullOrEmpty(groupName) || eventData == null) return true;

            _totalFilterExecutions++;

            if (_namedFilters.TryGetValue(groupName, out List<EventFilter> filters))
            {
                List<EventFilter> currentFilters;

                lock (_lockObject)
                {
                    currentFilters = new List<EventFilter>(filters);
                }

                foreach (var filter in currentFilters)
                {
                    try
                    {
                        bool result = filter.Evaluate(eventData);

                        if (!result)
                        {
                            _totalFilteredEvents++;

                            if (_enableDebugLogging)
                            {
                                Debug.Log($"EventFilterManager: 事件 {typeof(T).Name} 被命名过滤器组 {groupName} 中的过滤器 {filter.Name} 阻止");
                            }

                            return false;
                        }

                        if (!_enableFilterChaining)
                        {
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"EventFilterManager: 命名过滤器 {filter.Name} 执行时发生错误: {ex.Message}");
                    }
                }
            }

            _totalPassedEvents++;
            return true;
        }

        /// <summary>
        /// 获取过滤器统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public EventFilterStatistics GetStatistics()
        {
            lock (_lockObject)
            {
                int totalGlobalFilters = _globalFilters.Values.Sum(filters => filters.Count);
                int totalNamedFilters = _namedFilters.Values.Sum(filters => filters.Count);

                return new EventFilterStatistics
                {
                    TotalGlobalFilters = totalGlobalFilters,
                    TotalNamedFilters = totalNamedFilters,
                    TotalFilterGroups = _namedFilters.Count,
                    TotalFilterExecutions = _totalFilterExecutions,
                    TotalFilteredEvents = _totalFilteredEvents,
                    TotalPassedEvents = _totalPassedEvents,
                    FilterEfficiency = _totalFilterExecutions > 0 ? (double)_totalFilteredEvents / _totalFilterExecutions : 0
                };
            }
        }

        /// <summary>
        /// 清除所有过滤器
        /// </summary>
        public void ClearAll()
        {
            lock (_lockObject)
            {
                int globalCount = _globalFilters.Values.Sum(filters => filters.Count);
                int namedCount = _namedFilters.Values.Sum(filters => filters.Count);

                _globalFilters.Clear();
                _namedFilters.Clear();

                _totalFilterExecutions = 0;
                _totalFilteredEvents = 0;
                _totalPassedEvents = 0;

                if (_enableDebugLogging)
                {
                    Debug.Log($"EventFilterManager: 清除所有过滤器，全局: {globalCount}，命名: {namedCount}");
                }
            }
        }

        /// <summary>
        /// 清除指定事件类型的所有过滤器
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        public void ClearFilters<T>() where T : IEvent
        {
            Type eventType = typeof(T);

            if (_globalFilters.TryGetValue(eventType, out List<EventFilter> filters))
            {
                lock (_lockObject)
                {
                    int count = filters.Count;
                    filters.Clear();

                    if (_enableDebugLogging)
                    {
                        Debug.Log($"EventFilterManager: 清除事件类型 {eventType.Name} 的所有过滤器，数量: {count}");
                    }
                }
            }
        }

        /// <summary>
        /// 清除指定命名过滤器组
        /// </summary>
        /// <param name="groupName">过滤器组名称</param>
        public void ClearNamedFilters(string groupName)
        {
            if (string.IsNullOrEmpty(groupName)) return;

            if (_namedFilters.TryGetValue(groupName, out List<EventFilter> filters))
            {
                lock (_lockObject)
                {
                    int count = filters.Count;
                    filters.Clear();

                    if (_enableDebugLogging)
                    {
                        Debug.Log($"EventFilterManager: 清除命名过滤器组 {groupName}，数量: {count}");
                    }
                }
            }
        }

        public void Dispose()
        {
            ClearAll();
        }
    }

    /// <summary>
    /// 事件过滤器基类
    /// </summary>
    internal abstract class EventFilter
    {
        public string Id { get; protected set; }
        public string Name { get; protected set; }
        public int Priority { get; protected set; }
        public DateTime CreationTime { get; protected set; }

        protected EventFilter(int priority, string name)
        {
            Id = Guid.NewGuid().ToString();
            Name = name ?? Id;
            Priority = priority;
            CreationTime = DateTime.UtcNow;
        }

        public abstract bool Evaluate(IEvent eventData);
    }

    /// <summary>
    /// 泛型事件过滤器
    /// </summary>
    internal class EventFilter<T> : EventFilter where T : IEvent
    {
        private readonly Func<T, bool> _filterFunc;

        public EventFilter(Func<T, bool> filterFunc, int priority, string name)
            : base(priority, name)
        {
            _filterFunc = filterFunc ?? throw new ArgumentNullException(nameof(filterFunc));
        }

        public override bool Evaluate(IEvent eventData)
        {
            if (eventData is T typedEvent)
            {
                return _filterFunc(typedEvent);
            }

            return true; // 如果类型不匹配，默认通过
        }
    }

    /// <summary>
    /// 事件过滤器统计信息
    /// </summary>
    public class EventFilterStatistics
    {
        public int TotalGlobalFilters { get; set; }
        public int TotalNamedFilters { get; set; }
        public int TotalFilterGroups { get; set; }
        public long TotalFilterExecutions { get; set; }
        public long TotalFilteredEvents { get; set; }
        public long TotalPassedEvents { get; set; }
        public double FilterEfficiency { get; set; }
    }
}
