using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;
using BlastingDesign.Events.Core;

namespace BlastingDesign.Events.Management
{
    /// <summary>
    /// 事件类型注册表
    /// 动态管理和注册事件类型，支持运行时发现和注册
    /// </summary>
    public class EventTypeRegistry : IDisposable
    {
        private readonly ConcurrentDictionary<Type, EventTypeInfo> _registeredTypes;
        private readonly ConcurrentDictionary<string, Type> _nameToTypeMap;
        private readonly object _lockObject = new object();

        // 配置
        private readonly bool _enableAutoDiscovery;
        private readonly bool _enableDebugLogging;

        // 统计信息
        private int _totalRegistrations = 0;
        private DateTime _lastRegistrationTime = DateTime.MinValue;

        public EventTypeRegistry(bool enableAutoDiscovery = true, bool enableDebugLogging = false)
        {
            _registeredTypes = new ConcurrentDictionary<Type, EventTypeInfo>();
            _nameToTypeMap = new ConcurrentDictionary<string, Type>();
            _enableAutoDiscovery = enableAutoDiscovery;
            _enableDebugLogging = enableDebugLogging;

            if (_enableAutoDiscovery)
            {
                DiscoverEventTypes();
            }
        }

        /// <summary>
        /// 注册事件类型
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="category">事件分类</param>
        /// <param name="description">事件描述</param>
        /// <returns>是否注册成功</returns>
        public bool RegisterEventType<T>(string category = null, string description = null) where T : IEvent
        {
            return RegisterEventType(typeof(T), category, description);
        }

        /// <summary>
        /// 注册事件类型
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="category">事件分类</param>
        /// <param name="description">事件描述</param>
        /// <returns>是否注册成功</returns>
        public bool RegisterEventType(Type eventType, string category = null, string description = null)
        {
            if (eventType == null || !typeof(IEvent).IsAssignableFrom(eventType))
            {
                if (_enableDebugLogging)
                {
                    Debug.LogWarning($"EventTypeRegistry: 无效的事件类型 {eventType?.Name}");
                }
                return false;
            }

            var eventInfo = new EventTypeInfo
            {
                EventType = eventType,
                Name = eventType.Name,
                FullName = eventType.FullName,
                Category = category ?? GetCategoryFromType(eventType),
                Description = description ?? GetDescriptionFromType(eventType),
                RegistrationTime = DateTime.UtcNow,
                IsBuiltIn = IsBuiltInEventType(eventType)
            };

            bool isNewRegistration = _registeredTypes.TryAdd(eventType, eventInfo);

            if (isNewRegistration)
            {
                _nameToTypeMap.TryAdd(eventType.Name, eventType);
                _nameToTypeMap.TryAdd(eventType.FullName, eventType);

                _totalRegistrations++;
                _lastRegistrationTime = DateTime.UtcNow;

                if (_enableDebugLogging)
                {
                    Debug.Log($"EventTypeRegistry: 注册事件类型 {eventType.Name} (分类: {eventInfo.Category})");
                }
            }

            return isNewRegistration;
        }

        /// <summary>
        /// 取消注册事件类型
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <returns>是否取消成功</returns>
        public bool UnregisterEventType<T>() where T : IEvent
        {
            return UnregisterEventType(typeof(T));
        }

        /// <summary>
        /// 取消注册事件类型
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <returns>是否取消成功</returns>
        public bool UnregisterEventType(Type eventType)
        {
            if (eventType == null) return false;

            bool removed = _registeredTypes.TryRemove(eventType, out EventTypeInfo eventInfo);

            if (removed)
            {
                _nameToTypeMap.TryRemove(eventType.Name, out _);
                _nameToTypeMap.TryRemove(eventType.FullName, out _);

                if (_enableDebugLogging)
                {
                    Debug.Log($"EventTypeRegistry: 取消注册事件类型 {eventType.Name}");
                }
            }

            return removed;
        }

        /// <summary>
        /// 检查事件类型是否已注册
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <returns>是否已注册</returns>
        public bool IsEventTypeRegistered(Type eventType)
        {
            return eventType != null && _registeredTypes.ContainsKey(eventType);
        }

        /// <summary>
        /// 检查事件类型是否已注册
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <returns>是否已注册</returns>
        public bool IsEventTypeRegistered<T>() where T : IEvent
        {
            return IsEventTypeRegistered(typeof(T));
        }

        /// <summary>
        /// 通过名称获取事件类型
        /// </summary>
        /// <param name="eventName">事件名称（支持简单名称和完整名称）</param>
        /// <returns>事件类型，如果未找到返回null</returns>
        public Type GetEventTypeByName(string eventName)
        {
            if (string.IsNullOrEmpty(eventName)) return null;

            _nameToTypeMap.TryGetValue(eventName, out Type eventType);
            return eventType;
        }

        /// <summary>
        /// 获取事件类型信息
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <returns>事件类型信息</returns>
        public EventTypeInfo GetEventTypeInfo(Type eventType)
        {
            if (eventType == null) return null;

            _registeredTypes.TryGetValue(eventType, out EventTypeInfo eventInfo);
            return eventInfo;
        }

        /// <summary>
        /// 获取事件类型信息
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <returns>事件类型信息</returns>
        public EventTypeInfo GetEventTypeInfo<T>() where T : IEvent
        {
            return GetEventTypeInfo(typeof(T));
        }

        /// <summary>
        /// 获取所有已注册的事件类型
        /// </summary>
        /// <returns>事件类型列表</returns>
        public List<Type> GetAllEventTypes()
        {
            return _registeredTypes.Keys.ToList();
        }

        /// <summary>
        /// 获取指定分类的事件类型
        /// </summary>
        /// <param name="category">事件分类</param>
        /// <returns>事件类型列表</returns>
        public List<Type> GetEventTypesByCategory(string category)
        {
            if (string.IsNullOrEmpty(category)) return new List<Type>();

            return _registeredTypes.Values
                .Where(info => string.Equals(info.Category, category, StringComparison.OrdinalIgnoreCase))
                .Select(info => info.EventType)
                .ToList();
        }

        /// <summary>
        /// 获取所有事件分类
        /// </summary>
        /// <returns>分类列表</returns>
        public List<string> GetAllCategories()
        {
            return _registeredTypes.Values
                .Select(info => info.Category)
                .Where(category => !string.IsNullOrEmpty(category))
                .Distinct()
                .OrderBy(category => category)
                .ToList();
        }

        /// <summary>
        /// 自动发现程序集中的事件类型
        /// </summary>
        public void DiscoverEventTypes()
        {
            try
            {
                var assemblies = AppDomain.CurrentDomain.GetAssemblies();
                int discoveredCount = 0;

                foreach (var assembly in assemblies)
                {
                    try
                    {
                        var eventTypes = assembly.GetTypes()
                            .Where(type => typeof(IEvent).IsAssignableFrom(type) &&
                                          !type.IsInterface &&
                                          !type.IsAbstract)
                            .ToList();

                        foreach (var eventType in eventTypes)
                        {
                            if (RegisterEventType(eventType))
                            {
                                discoveredCount++;
                            }
                        }
                    }
                    catch (ReflectionTypeLoadException ex)
                    {
                        if (_enableDebugLogging)
                        {
                            Debug.LogWarning($"EventTypeRegistry: 无法加载程序集 {assembly.FullName} 中的类型: {ex.Message}");
                        }
                    }
                }

                if (_enableDebugLogging)
                {
                    Debug.Log($"EventTypeRegistry: 自动发现并注册了 {discoveredCount} 个事件类型");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"EventTypeRegistry: 自动发现事件类型时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取注册表统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public EventTypeRegistryStatistics GetStatistics()
        {
            var categories = GetAllCategories();
            var categoryStats = categories.ToDictionary(
                category => category,
                category => GetEventTypesByCategory(category).Count
            );

            return new EventTypeRegistryStatistics
            {
                TotalRegisteredTypes = _registeredTypes.Count,
                TotalRegistrations = _totalRegistrations,
                TotalCategories = categories.Count,
                BuiltInEventTypes = _registeredTypes.Values.Count(info => info.IsBuiltIn),
                CustomEventTypes = _registeredTypes.Values.Count(info => !info.IsBuiltIn),
                LastRegistrationTime = _lastRegistrationTime,
                CategoryStatistics = categoryStats
            };
        }

        /// <summary>
        /// 清除所有注册
        /// </summary>
        public void ClearAll()
        {
            lock (_lockObject)
            {
                int count = _registeredTypes.Count;
                _registeredTypes.Clear();
                _nameToTypeMap.Clear();
                _totalRegistrations = 0;
                _lastRegistrationTime = DateTime.MinValue;

                if (_enableDebugLogging)
                {
                    Debug.Log($"EventTypeRegistry: 清除了所有注册，总数: {count}");
                }
            }
        }

        #region 私有辅助方法

        private string GetCategoryFromType(Type eventType)
        {
            // 尝试从命名空间推断分类
            if (eventType.Namespace != null)
            {
                var namespaceParts = eventType.Namespace.Split('.');
                if (namespaceParts.Length > 0)
                {
                    return namespaceParts[namespaceParts.Length - 1];
                }
            }

            // 尝试从类名推断分类
            string typeName = eventType.Name;
            if (typeName.EndsWith("Event"))
            {
                typeName = typeName.Substring(0, typeName.Length - 5);
            }

            return typeName;
        }

        private string GetDescriptionFromType(Type eventType)
        {
            // 尝试从特性获取描述
            var descriptionAttr = eventType.GetCustomAttribute<System.ComponentModel.DescriptionAttribute>();
            if (descriptionAttr != null)
            {
                return descriptionAttr.Description;
            }

            // 默认描述
            return $"{eventType.Name} 事件";
        }

        private bool IsBuiltInEventType(Type eventType)
        {
            // 检查是否为内置事件类型
            return eventType.Namespace != null &&
                   eventType.Namespace.StartsWith("BlastingDesign.Events.Core");
        }

        #endregion

        public void Dispose()
        {
            ClearAll();
        }
    }

    /// <summary>
    /// 事件类型信息
    /// </summary>
    public class EventTypeInfo
    {
        public Type EventType { get; set; }
        public string Name { get; set; }
        public string FullName { get; set; }
        public string Category { get; set; }
        public string Description { get; set; }
        public DateTime RegistrationTime { get; set; }
        public bool IsBuiltIn { get; set; }
    }

    /// <summary>
    /// 事件类型注册表统计信息
    /// </summary>
    public class EventTypeRegistryStatistics
    {
        public int TotalRegisteredTypes { get; set; }
        public int TotalRegistrations { get; set; }
        public int TotalCategories { get; set; }
        public int BuiltInEventTypes { get; set; }
        public int CustomEventTypes { get; set; }
        public DateTime LastRegistrationTime { get; set; }
        public Dictionary<string, int> CategoryStatistics { get; set; }
    }
}
