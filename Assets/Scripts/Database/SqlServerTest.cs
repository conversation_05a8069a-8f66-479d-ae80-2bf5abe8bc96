using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Data;
using System.Data.SqlClient;
using BlastingDesign.Utils;

public class SqlServerTest : MonoBehaviour
{
    //下面的账号和密码是你下载数据库时设置的账号和密码，而server后面的IP地址是最开始设置的IP地址
    // 尝试多种连接字符串配置
    string[] connectionStrings = {
        // 方案1: 完全禁用加密和证书验证
        @"Data Source=localhost; Initial Catalog=DTCKC_DATA; User ID=sa; Password=******; Encrypt=False; TrustServerCertificate=True; MultipleActiveResultSets=True;",
    };

    SqlConnection conn;     //创建一个数据库连接

    void Start()
    {
        SQLServerToConnection();
    }

    private void SQLServerToConnection()
    {
        // 尝试不同的连接字符串
        for (int i = 0; i < connectionStrings.Length; i++)
        {
            try
            {
                Logging.LogInfo("SqlServerTest", $"尝试连接方案 {i + 1}: {connectionStrings[i].Substring(0, Math.Min(50, connectionStrings[i].Length))}...");

                conn = new SqlConnection(connectionStrings[i]);
                Logging.LogInfo("SqlServerTest", "开始连接数据库:" + conn.State);

                //判断数据库是否处于关闭状态
                if (conn.State == ConnectionState.Closed)
                {
                    conn.Open();
                    Logging.LogInfo("SqlServerTest", "尝试连接数据库:" + conn.State);
                    if (conn.State == ConnectionState.Open)
                    {
                        Logging.LogInfo("SqlServerTest", $"连接成功！使用方案 {i + 1}");
                        return; // 连接成功，退出循环
                    }
                    else
                    {
                        Logging.LogError("SqlServerTest", $"方案 {i + 1} 连接失败");
                    }
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("SqlServerTest", $"方案 {i + 1} 数据库连接异常: " + ex.Message);
                if (i == connectionStrings.Length - 1) // 最后一个方案也失败了
                {
                    Logging.LogError("SqlServerTest", "所有连接方案都失败了，异常详情: " + ex.ToString());
                }

                // 关闭当前连接（如果存在）
                if (conn != null && conn.State != ConnectionState.Closed)
                {
                    conn.Close();
                }
            }
        }
    }
}
