using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Linq;
using UnityEngine;
using Microsoft.Data.Sqlite;
using Dapper;
using BlastingDesign.Utils;

namespace BlastingDesign.Database
{
    /// <summary>
    /// 使用 Dapper ORM 的 SQLite 测试脚本
    /// 演示如何在 Unity 中使用 Dapper 进行数据库操作
    /// </summary>
    public class DapperSqliteTest : MonoBehaviour
    {
        [Header("Dapper SQLite 配置")]
        [SerializeField] private string databaseName = "DapperTest.db";
        [SerializeField] private bool useMemoryDatabase = false;
        [SerializeField] private bool createSampleData = true;
        [SerializeField] private bool useAsyncOperations = true;

        private string connectionString;
        private string databasePath;

        void Start()
        {
            InitializeDatabase();

            if (useAsyncOperations)
            {
                _ = RunDapperTestsAsync();
            }
            else
            {
                RunDapperTests();
            }
        }

        /// <summary>
        /// 初始化数据库连接
        /// </summary>
        private void InitializeDatabase()
        {
            if (useMemoryDatabase)
            {
                connectionString = "Data Source=:memory:";
                Logging.LogInfo("DapperSqliteTest", "使用内存数据库");
            }
            else
            {
                databasePath = Path.Combine(Application.persistentDataPath, databaseName);
                connectionString = $"Data Source={databasePath}";
                Logging.LogInfo("DapperSqliteTest", $"数据库路径: {databasePath}");
            }
        }

        /// <summary>
        /// 运行 Dapper 测试（同步版本）
        /// </summary>
        private void RunDapperTests()
        {
            try
            {
                using var connection = new SqliteConnection(connectionString);
                connection.Open();

                Logging.LogInfo("DapperSqliteTest", "开始 Dapper 同步测试...");

                // 创建表
                CreateTables(connection);

                // 插入数据
                if (createSampleData)
                {
                    InsertSampleData(connection);
                }

                // 查询数据
                QueryData(connection);

                Logging.LogInfo("DapperSqliteTest", "Dapper 同步测试完成");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperSqliteTest", $"同步测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行 Dapper 测试（异步版本）
        /// </summary>
        private async Task RunDapperTestsAsync()
        {
            try
            {
                using var connection = new SqliteConnection(connectionString);
                await connection.OpenAsync();

                Logging.LogInfo("DapperSqliteTest", "开始 Dapper 异步测试...");

                // 创建表
                await CreateTablesAsync(connection);

                // 插入数据
                if (createSampleData)
                {
                    await InsertSampleDataAsync(connection);
                }

                // 查询数据
                await QueryDataAsync(connection);

                Logging.LogInfo("DapperSqliteTest", "Dapper 异步测试完成");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperSqliteTest", $"异步测试失败: {ex.Message}");
            }
        }

        #region 同步方法

        /// <summary>
        /// 创建表（同步）
        /// </summary>
        private void CreateTables(SqliteConnection connection)
        {
            var createUserTableSql = @"
                CREATE TABLE IF NOT EXISTS Users (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Email TEXT UNIQUE,
                    Age INTEGER,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )";

            var createPostTableSql = @"
                CREATE TABLE IF NOT EXISTS Posts (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER,
                    Title TEXT NOT NULL,
                    Content TEXT,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )";

            connection.Execute(createUserTableSql);
            connection.Execute(createPostTableSql);

            Logging.LogInfo("DapperSqliteTest", "表创建成功");
        }

        /// <summary>
        /// 插入示例数据（同步）
        /// </summary>
        private void InsertSampleData(SqliteConnection connection)
        {
            // 插入用户
            var users = new[]
            {
                new { Name = "张三", Email = "<EMAIL>", Age = 25 },
                new { Name = "李四", Email = "<EMAIL>", Age = 30 },
                new { Name = "王五", Email = "<EMAIL>", Age = 28 }
            };

            var insertUserSql = "INSERT INTO Users (Name, Email, Age) VALUES (@Name, @Email, @Age)";
            var userCount = connection.Execute(insertUserSql, users);
            Logging.LogInfo("DapperSqliteTest", $"插入了 {userCount} 个用户");

            // 插入文章
            var posts = new[]
            {
                new { UserId = 1, Title = "Unity 开发心得", Content = "分享一些 Unity 开发的经验..." },
                new { UserId = 1, Title = "SQLite 数据库使用", Content = "在 Unity 中使用 SQLite 的方法..." },
                new { UserId = 2, Title = "Dapper ORM 介绍", Content = "Dapper 是一个轻量级的 ORM 框架..." },
                new { UserId = 3, Title = "游戏性能优化", Content = "提升游戏性能的几个技巧..." }
            };

            var insertPostSql = "INSERT INTO Posts (UserId, Title, Content) VALUES (@UserId, @Title, @Content)";
            var postCount = connection.Execute(insertPostSql, posts);
            Logging.LogInfo("DapperSqliteTest", $"插入了 {postCount} 篇文章");
        }

        /// <summary>
        /// 查询数据（同步）
        /// </summary>
        private void QueryData(SqliteConnection connection)
        {
            // 查询所有用户
            var users = connection.Query<User>("SELECT * FROM Users ORDER BY Id");
            Logging.LogInfo("DapperSqliteTest", $"查询到 {users.Count()} 个用户:");
            foreach (var user in users)
            {
                Logging.LogInfo("DapperSqliteTest", $"  用户: {user.Name}, 邮箱: {user.Email}, 年龄: {user.Age}");
            }

            // 查询用户及其文章数量
            var userPostCounts = connection.Query(@"
                SELECT u.Name, u.Email, COUNT(p.Id) as PostCount
                FROM Users u
                LEFT JOIN Posts p ON u.Id = p.UserId
                GROUP BY u.Id, u.Name, u.Email
                ORDER BY PostCount DESC");

            Logging.LogInfo("DapperSqliteTest", "用户文章统计:");
            foreach (var item in userPostCounts)
            {
                Logging.LogInfo("DapperSqliteTest", $"  {item.Name}: {item.PostCount} 篇文章");
            }

            // 查询特定用户的文章
            var userPosts = connection.Query<Post>("SELECT * FROM Posts WHERE UserId = @UserId", new { UserId = 1 });
            Logging.LogInfo("DapperSqliteTest", $"用户1的文章 ({userPosts.Count()} 篇):");
            foreach (var post in userPosts)
            {
                Logging.LogInfo("DapperSqliteTest", $"  标题: {post.Title}");
            }
        }

        #endregion

        #region 异步方法

        /// <summary>
        /// 创建表（异步）
        /// </summary>
        private async Task CreateTablesAsync(SqliteConnection connection)
        {
            var createUserTableSql = @"
                CREATE TABLE IF NOT EXISTS Users (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Email TEXT UNIQUE,
                    Age INTEGER,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )";

            var createPostTableSql = @"
                CREATE TABLE IF NOT EXISTS Posts (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER,
                    Title TEXT NOT NULL,
                    Content TEXT,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )";

            await connection.ExecuteAsync(createUserTableSql);
            await connection.ExecuteAsync(createPostTableSql);

            Logging.LogInfo("DapperSqliteTest", "表创建成功（异步）");
        }

        /// <summary>
        /// 插入示例数据（异步）
        /// </summary>
        private async Task InsertSampleDataAsync(SqliteConnection connection)
        {
            // 插入用户
            var users = new[]
            {
                new { Name = "异步张三", Email = "<EMAIL>", Age = 26 },
                new { Name = "异步李四", Email = "<EMAIL>", Age = 31 },
                new { Name = "异步王五", Email = "<EMAIL>", Age = 29 }
            };

            var insertUserSql = "INSERT INTO Users (Name, Email, Age) VALUES (@Name, @Email, @Age)";
            var userCount = await connection.ExecuteAsync(insertUserSql, users);
            Logging.LogInfo("DapperSqliteTest", $"异步插入了 {userCount} 个用户");

            // 插入文章
            var posts = new[]
            {
                new { UserId = 1, Title = "异步编程指南", Content = "C# 异步编程的最佳实践..." },
                new { UserId = 2, Title = "Dapper 异步操作", Content = "使用 Dapper 进行异步数据库操作..." }
            };

            var insertPostSql = "INSERT INTO Posts (UserId, Title, Content) VALUES (@UserId, @Title, @Content)";
            var postCount = await connection.ExecuteAsync(insertPostSql, posts);
            Logging.LogInfo("DapperSqliteTest", $"异步插入了 {postCount} 篇文章");
        }

        /// <summary>
        /// 查询数据（异步）
        /// </summary>
        private async Task QueryDataAsync(SqliteConnection connection)
        {
            // 查询所有用户
            var users = await connection.QueryAsync<User>("SELECT * FROM Users ORDER BY Id");
            Logging.LogInfo("DapperSqliteTest", $"异步查询到 {users.Count()} 个用户:");
            foreach (var user in users)
            {
                Logging.LogInfo("DapperSqliteTest", $"  用户: {user.Name}, 邮箱: {user.Email}, 年龄: {user.Age}");
            }

            // 使用参数化查询
            var youngUsers = await connection.QueryAsync<User>(
                "SELECT * FROM Users WHERE Age < @MaxAge ORDER BY Age",
                new { MaxAge = 30 });

            Logging.LogInfo("DapperSqliteTest", $"年龄小于30的用户 ({youngUsers.Count()} 个):");
            foreach (var user in youngUsers)
            {
                Logging.LogInfo("DapperSqliteTest", $"  {user.Name}: {user.Age}岁");
            }
        }

        #endregion

        #region 右键菜单方法

        /// <summary>
        /// 清空数据库
        /// </summary>
        [ContextMenu("清空数据库")]
        public void ClearDatabase()
        {
            try
            {
                using var connection = new SqliteConnection(connectionString);
                connection.Open();

                connection.Execute("DELETE FROM Posts");
                connection.Execute("DELETE FROM Users");

                Logging.LogInfo("DapperSqliteTest", "数据库已清空");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperSqliteTest", $"清空数据库失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取数据库统计信息
        /// </summary>
        [ContextMenu("获取统计信息")]
        public void GetDatabaseStats()
        {
            try
            {
                using var connection = new SqliteConnection(connectionString);
                connection.Open();

                var userCount = connection.QuerySingle<int>("SELECT COUNT(*) FROM Users");
                var postCount = connection.QuerySingle<int>("SELECT COUNT(*) FROM Posts");

                Logging.LogInfo("DapperSqliteTest", "=== 数据库统计 ===");
                Logging.LogInfo("DapperSqliteTest", $"用户数量: {userCount}");
                Logging.LogInfo("DapperSqliteTest", $"文章数量: {postCount}");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperSqliteTest", $"获取统计信息失败: {ex.Message}");
            }
        }

        #endregion
    }

    #region 数据模型

    /// <summary>
    /// 用户数据模型
    /// </summary>
    public class User
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public int Age { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// 文章数据模型
    /// </summary>
    public class Post
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    #endregion
}
