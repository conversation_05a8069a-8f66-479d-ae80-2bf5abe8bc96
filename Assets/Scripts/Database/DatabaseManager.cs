using System;
using System.Threading.Tasks;
using UnityEngine;
using System.Data.SqlClient;
using System.Data;
using BlastingDesign.Utils;

namespace BlastingDesign.Database
{
    /// <summary>
    /// 数据库管理器
    /// 提供数据库连接、测试和操作功能
    /// </summary>
    public class DatabaseManager
    {
        private SqlConnection currentConnection;
        private DatabaseConfiguration currentConfig;
        private bool isConnected = false;

        #region 构造函数

        public DatabaseManager()
        {
            Logging.LogInfo("DatabaseManager", "数据库管理器初始化");
        }

        ~DatabaseManager()
        {
            Disconnect();
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 当前连接状态
        /// </summary>
        public bool IsConnected => isConnected && currentConnection != null && currentConnection.State == ConnectionState.Open;

        /// <summary>
        /// 当前配置
        /// </summary>
        public DatabaseConfiguration CurrentConfiguration => currentConfig;

        #endregion

        #region 连接管理

        /// <summary>
        /// 异步测试数据库连接
        /// </summary>
        /// <param name="config">数据库配置</param>
        /// <returns>连接是否成功</returns>
        public async Task<bool> TestConnectionAsync(DatabaseConfiguration config)
        {
            if (config == null)
            {
                Logging.LogError("DatabaseManager", "数据库配置为空");
                return false;
            }

            try
            {
                string connectionString = BuildConnectionString(config);
                Logging.LogInfo("DatabaseManager", $"开始测试数据库连接: {config.Server}/{config.Database}");

                using (var testConnection = new SqlConnection(connectionString))
                {
                    await testConnection.OpenAsync();

                    if (testConnection.State == ConnectionState.Open)
                    {
                        Logging.LogInfo("DatabaseManager", "数据库连接测试成功");
                        return true;
                    }
                    else
                    {
                        Logging.LogError("DatabaseManager", $"数据库连接状态异常: {testConnection.State}");
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("DatabaseManager", $"数据库连接测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 连接数据库
        /// </summary>
        /// <param name="config">数据库配置</param>
        /// <returns>连接是否成功</returns>
        public async Task<bool> ConnectAsync(DatabaseConfiguration config)
        {
            if (config == null)
            {
                Logging.LogError("DatabaseManager", "数据库配置为空");
                return false;
            }

            try
            {
                // 如果已有连接，先断开
                if (currentConnection != null)
                {
                    await DisconnectAsync();
                }

                string connectionString = BuildConnectionString(config);
                currentConnection = new SqlConnection(connectionString);

                await currentConnection.OpenAsync();

                if (currentConnection.State == ConnectionState.Open)
                {
                    isConnected = true;
                    currentConfig = config;
                    Logging.LogInfo("DatabaseManager", $"数据库连接成功: {config.Server}/{config.Database}");
                    return true;
                }
                else
                {
                    Logging.LogError("DatabaseManager", $"数据库连接状态异常: {currentConnection.State}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("DatabaseManager", $"数据库连接失败: {ex.Message}");
                isConnected = false;
                return false;
            }
        }

        /// <summary>
        /// 断开数据库连接
        /// </summary>
        public async Task DisconnectAsync()
        {
            try
            {
                if (currentConnection != null)
                {
                    if (currentConnection.State == ConnectionState.Open)
                    {
                        await currentConnection.CloseAsync();
                    }
                    currentConnection.Dispose();
                    currentConnection = null;
                }

                isConnected = false;
                Logging.LogInfo("DatabaseManager", "数据库连接已断开");
            }
            catch (Exception ex)
            {
                Logging.LogError("DatabaseManager", $"断开数据库连接时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 同步版本的断开连接
        /// </summary>
        public void Disconnect()
        {
            try
            {
                if (currentConnection != null)
                {
                    if (currentConnection.State == ConnectionState.Open)
                    {
                        currentConnection.Close();
                    }
                    currentConnection.Dispose();
                    currentConnection = null;
                }

                isConnected = false;
                Logging.LogInfo("DatabaseManager", "数据库连接已断开");
            }
            catch (Exception ex)
            {
                Logging.LogError("DatabaseManager", $"断开数据库连接时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region 数据库操作

        /// <summary>
        /// 执行SQL查询
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <returns>查询结果</returns>
        public async Task<DataTable> ExecuteQueryAsync(string sql)
        {
            if (!IsConnected)
            {
                throw new InvalidOperationException("数据库未连接");
            }

            try
            {
                using (var command = new SqlCommand(sql, currentConnection))
                {
                    using (var adapter = new SqlDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        await Task.Run(() => adapter.Fill(dataTable));
                        return dataTable;
                    }
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("DatabaseManager", $"执行SQL查询失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 执行SQL命令（非查询）
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <returns>受影响的行数</returns>
        public async Task<int> ExecuteNonQueryAsync(string sql)
        {
            if (!IsConnected)
            {
                throw new InvalidOperationException("数据库未连接");
            }

            try
            {
                using (var command = new SqlCommand(sql, currentConnection))
                {
                    return await command.ExecuteNonQueryAsync();
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("DatabaseManager", $"执行SQL命令失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 执行SQL标量查询
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <returns>查询结果</returns>
        public async Task<object> ExecuteScalarAsync(string sql)
        {
            if (!IsConnected)
            {
                throw new InvalidOperationException("数据库未连接");
            }

            try
            {
                using (var command = new SqlCommand(sql, currentConnection))
                {
                    return await command.ExecuteScalarAsync();
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("DatabaseManager", $"执行SQL标量查询失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取数据库信息
        /// </summary>
        /// <returns>数据库信息</returns>
        public async Task<DatabaseInfo> GetDatabaseInfoAsync()
        {
            if (!IsConnected)
            {
                throw new InvalidOperationException("数据库未连接");
            }

            try
            {
                var info = new DatabaseInfo();

                // 获取数据库版本
                var versionResult = await ExecuteScalarAsync("SELECT @@VERSION");
                info.Version = versionResult?.ToString() ?? "Unknown";

                // 获取数据库大小
                var sizeQuery = $"SELECT SUM(size) * 8 / 1024.0 FROM sys.master_files WHERE database_id = DB_ID('{currentConfig.Database}')";
                var sizeResult = await ExecuteScalarAsync(sizeQuery);
                if (sizeResult != null && sizeResult != DBNull.Value)
                {
                    info.SizeInMB = Convert.ToDouble(sizeResult);
                }

                // 获取表数量
                var tableCountResult = await ExecuteScalarAsync("SELECT COUNT(*) FROM sys.tables");
                if (tableCountResult != null && tableCountResult != DBNull.Value)
                {
                    info.TableCount = Convert.ToInt32(tableCountResult);
                }

                return info;
            }
            catch (Exception ex)
            {
                Logging.LogError("DatabaseManager", $"获取数据库信息失败: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region 工具方法

        /// <summary>
        /// 构建连接字符串
        /// </summary>
        /// <param name="config">数据库配置</param>
        /// <returns>连接字符串</returns>
        private string BuildConnectionString(DatabaseConfiguration config)
        {
            var builder = new SqlConnectionStringBuilder
            {
                DataSource = config.Server,
                InitialCatalog = config.Database,
                UserID = config.Username,
                Password = config.Password,
                Encrypt = config.Encrypt,
                TrustServerCertificate = config.TrustServerCertificate,
                MultipleActiveResultSets = true,
                ConnectTimeout = 30, // 30秒超时
            };

            return builder.ConnectionString;
        }

        #endregion
    }

    /// <summary>
    /// 数据库配置
    /// </summary>
    [Serializable]
    public class DatabaseConfiguration
    {
        public string Server { get; set; } = "localhost";
        public string Database { get; set; } = "DTCKC_DATA";
        public string Username { get; set; } = "sa";
        public string Password { get; set; } = "123456";
        public bool Encrypt { get; set; } = false;
        public bool TrustServerCertificate { get; set; } = true;

        public override string ToString()
        {
            return $"Server={Server}, Database={Database}, Username={Username}";
        }
    }

    /// <summary>
    /// 数据库信息
    /// </summary>
    [Serializable]
    public class DatabaseInfo
    {
        public string Version { get; set; } = "Unknown";
        public double SizeInMB { get; set; } = 0;
        public int TableCount { get; set; } = 0;

        public override string ToString()
        {
            return $"Version: {Version}, Size: {SizeInMB:F2} MB, Tables: {TableCount}";
        }
    }
}