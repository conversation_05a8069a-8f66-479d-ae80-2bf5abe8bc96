using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using UnityEngine;
using System.Data.SqlClient;
using Dapper;
using BlastingDesign.Utils;

namespace BlastingDesign.Database
{
    /// <summary>
    /// 使用 Dapper ORM 的 SQL Server 测试脚本
    /// 演示如何在 Unity 中使用 Dapper 连接 SQL Server 进行数据库操作
    /// </summary>
    public class DapperSqlServerTest : MonoBehaviour
    {
        [Header("Dapper SQL Server 配置")]
        [SerializeField] private string serverName = "localhost";
        [SerializeField] private string databaseName = "DapperTestDB";
        [SerializeField] private string username = "sa";
        [SerializeField] private string password = "your_password";
        [SerializeField] private bool useWindowsAuth = true;
        [SerializeField] private bool createSampleData = true;
        [SerializeField] private bool useAsyncOperations = true;
        [SerializeField] private int connectionTimeout = 30;

        private string[] connectionStrings;

        void Start()
        {
            InitializeConnectionStrings();

            if (useAsyncOperations)
            {
                _ = RunDapperSqlServerTestsAsync();
            }
            else
            {
                RunDapperSqlServerTests();
            }
        }

        /// <summary>
        /// 初始化连接字符串
        /// </summary>
        private void InitializeConnectionStrings()
        {
            if (useWindowsAuth)
            {
                // Windows 身份验证
                connectionStrings = new string[]
                {
                    $"Server={serverName};Database={databaseName};Integrated Security=true;Connection Timeout={connectionTimeout};",
                    $"Server={serverName};Database={databaseName};Trusted_Connection=true;Connection Timeout={connectionTimeout};",
                    $"Data Source={serverName};Initial Catalog={databaseName};Integrated Security=SSPI;Connection Timeout={connectionTimeout};"
                };
                Logging.LogInfo("DapperSqlServerTest", "使用 Windows 身份验证");
            }
            else
            {
                // SQL Server 身份验证
                connectionStrings = new string[]
                {
                    $"Server={serverName};Database={databaseName};User Id={username};Password={password};Connection Timeout={connectionTimeout};",
                    $"Data Source={serverName};Initial Catalog={databaseName};User ID={username};Password={password};Connection Timeout={connectionTimeout};",
                    $"Server={serverName};Database={databaseName};UID={username};PWD={password};Connection Timeout={connectionTimeout};"
                };
                Logging.LogInfo("DapperSqlServerTest", "使用 SQL Server 身份验证");
            }

            Logging.LogInfo("DapperSqlServerTest", $"目标服务器: {serverName}");
            Logging.LogInfo("DapperSqlServerTest", $"目标数据库: {databaseName}");
        }

        /// <summary>
        /// 运行 Dapper SQL Server 测试（同步版本）
        /// </summary>
        private void RunDapperSqlServerTests()
        {
            SqlConnection connection = null;

            try
            {
                connection = EstablishConnection();
                if (connection == null) return;

                Logging.LogInfo("DapperSqlServerTest", "开始 Dapper SQL Server 同步测试...");

                // 创建表
                CreateTables(connection);

                // 插入数据
                if (createSampleData)
                {
                    InsertSampleData(connection);
                }

                // 查询数据
                QueryData(connection);

                Logging.LogInfo("DapperSqlServerTest", "Dapper SQL Server 同步测试完成");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperSqlServerTest", $"同步测试失败: {ex.Message}");
            }
            finally
            {
                connection?.Close();
                connection?.Dispose();
            }
        }

        /// <summary>
        /// 运行 Dapper SQL Server 测试（异步版本）
        /// </summary>
        private async Task RunDapperSqlServerTestsAsync()
        {
            SqlConnection connection = null;

            try
            {
                connection = await EstablishConnectionAsync();
                if (connection == null) return;

                Logging.LogInfo("DapperSqlServerTest", "开始 Dapper SQL Server 异步测试...");

                // 创建表
                await CreateTablesAsync(connection);

                // 插入数据
                if (createSampleData)
                {
                    await InsertSampleDataAsync(connection);
                }

                // 查询数据
                await QueryDataAsync(connection);

                Logging.LogInfo("DapperSqlServerTest", "Dapper SQL Server 异步测试完成");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperSqlServerTest", $"异步测试失败: {ex.Message}");
            }
            finally
            {
                if (connection != null)
                {
                    connection.Close();
                    connection.Dispose();
                }
            }
        }

        /// <summary>
        /// 建立数据库连接（同步）
        /// </summary>
        private SqlConnection EstablishConnection()
        {
            for (int i = 0; i < connectionStrings.Length; i++)
            {
                try
                {
                    Logging.LogInfo("DapperSqlServerTest", $"尝试连接方案 {i + 1}...");

                    var connection = new SqlConnection(connectionStrings[i]);
                    connection.Open();

                    if (connection.State == System.Data.ConnectionState.Open)
                    {
                        Logging.LogInfo("DapperSqlServerTest", $"SQL Server 连接成功！使用方案 {i + 1}");

                        // 测试基本查询
                        var serverVersion = connection.QuerySingle<string>("SELECT @@VERSION");
                        Logging.LogInfo("DapperSqlServerTest", $"服务器版本: {serverVersion.Substring(0, Math.Min(100, serverVersion.Length))}...");

                        return connection;
                    }
                }
                catch (Exception ex)
                {
                    Logging.LogError("DapperSqlServerTest", $"方案 {i + 1} 连接失败: {ex.Message}");
                    if (i == connectionStrings.Length - 1)
                    {
                        Logging.LogError("DapperSqlServerTest", "所有连接方案都失败了");
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 建立数据库连接（异步）
        /// </summary>
        private async Task<SqlConnection> EstablishConnectionAsync()
        {
            for (int i = 0; i < connectionStrings.Length; i++)
            {
                try
                {
                    Logging.LogInfo("DapperSqlServerTest", $"尝试异步连接方案 {i + 1}...");

                    var connection = new SqlConnection(connectionStrings[i]);
                    await connection.OpenAsync();

                    if (connection.State == System.Data.ConnectionState.Open)
                    {
                        Logging.LogInfo("DapperSqlServerTest", $"SQL Server 异步连接成功！使用方案 {i + 1}");

                        // 测试基本查询
                        var serverVersion = await connection.QuerySingleAsync<string>("SELECT @@VERSION");
                        Logging.LogInfo("DapperSqlServerTest", $"服务器版本: {serverVersion.Substring(0, Math.Min(100, serverVersion.Length))}...");

                        return connection;
                    }
                }
                catch (Exception ex)
                {
                    Logging.LogError("DapperSqlServerTest", $"方案 {i + 1} 异步连接失败: {ex.Message}");
                    if (i == connectionStrings.Length - 1)
                    {
                        Logging.LogError("DapperSqlServerTest", "所有异步连接方案都失败了");
                    }
                }
            }

            return null;
        }

        #region 同步方法

        /// <summary>
        /// 创建表（同步）
        /// </summary>
        private void CreateTables(SqlConnection connection)
        {
            var createUserTableSql = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
                CREATE TABLE Users (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    Name NVARCHAR(100) NOT NULL,
                    Email NVARCHAR(255) UNIQUE,
                    Age INT,
                    CreatedAt DATETIME2 DEFAULT GETDATE()
                )";

            var createPostTableSql = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Posts' AND xtype='U')
                CREATE TABLE Posts (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    UserId INT,
                    Title NVARCHAR(255) NOT NULL,
                    Content NTEXT,
                    CreatedAt DATETIME2 DEFAULT GETDATE(),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )";

            connection.Execute(createUserTableSql);
            connection.Execute(createPostTableSql);

            Logging.LogInfo("DapperSqlServerTest", "SQL Server 表创建成功");
        }

        /// <summary>
        /// 插入示例数据（同步）
        /// </summary>
        private void InsertSampleData(SqlConnection connection)
        {
            // 清空现有数据
            connection.Execute("DELETE FROM Posts");
            connection.Execute("DELETE FROM Users");

            // 插入用户
            var users = new[]
            {
                new { Name = "SQL张三", Email = "<EMAIL>", Age = 25 },
                new { Name = "SQL李四", Email = "<EMAIL>", Age = 30 },
                new { Name = "SQL王五", Email = "<EMAIL>", Age = 28 }
            };

            var insertUserSql = "INSERT INTO Users (Name, Email, Age) VALUES (@Name, @Email, @Age)";
            var userCount = connection.Execute(insertUserSql, users);
            Logging.LogInfo("DapperSqlServerTest", $"插入了 {userCount} 个用户");

            // 插入文章
            var posts = new[]
            {
                new { UserId = 1, Title = "SQL Server 性能优化", Content = "分享一些 SQL Server 性能优化的技巧..." },
                new { UserId = 1, Title = "T-SQL 高级查询", Content = "深入了解 T-SQL 的高级查询功能..." },
                new { UserId = 2, Title = "数据库设计原则", Content = "关系型数据库设计的最佳实践..." },
                new { UserId = 3, Title = "索引优化策略", Content = "如何正确使用索引提升查询性能..." }
            };

            var insertPostSql = "INSERT INTO Posts (UserId, Title, Content) VALUES (@UserId, @Title, @Content)";
            var postCount = connection.Execute(insertPostSql, posts);
            Logging.LogInfo("DapperSqlServerTest", $"插入了 {postCount} 篇文章");
        }

        /// <summary>
        /// 查询数据（同步）
        /// </summary>
        private void QueryData(SqlConnection connection)
        {
            // 查询所有用户
            var users = connection.Query<SqlServerUser>("SELECT * FROM Users ORDER BY Id");
            Logging.LogInfo("DapperSqlServerTest", $"查询到 {users.Count()} 个用户:");
            foreach (var user in users)
            {
                Logging.LogInfo("DapperSqlServerTest", $"  用户: {user.Name}, 邮箱: {user.Email}, 年龄: {user.Age}");
            }

            // 查询用户及其文章数量（使用 T-SQL 特性）
            var userPostCounts = connection.Query(@"
                SELECT u.Name, u.Email, COUNT(p.Id) as PostCount
                FROM Users u
                LEFT JOIN Posts p ON u.Id = p.UserId
                GROUP BY u.Id, u.Name, u.Email
                ORDER BY PostCount DESC");

            Logging.LogInfo("DapperSqlServerTest", "用户文章统计:");
            foreach (var item in userPostCounts)
            {
                Logging.LogInfo("DapperSqlServerTest", $"  {item.Name}: {item.PostCount} 篇文章");
            }

            // 查询特定用户的文章
            var userPosts = connection.Query<SqlServerPost>("SELECT * FROM Posts WHERE UserId = @UserId", new { UserId = 1 });
            Logging.LogInfo("DapperSqlServerTest", $"用户1的文章 ({userPosts.Count()} 篇):");
            foreach (var post in userPosts)
            {
                Logging.LogInfo("DapperSqlServerTest", $"  标题: {post.Title}");
            }
        }

        #endregion

        #region 异步方法

        /// <summary>
        /// 创建表（异步）
        /// </summary>
        private async Task CreateTablesAsync(SqlConnection connection)
        {
            var createUserTableSql = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
                CREATE TABLE Users (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    Name NVARCHAR(100) NOT NULL,
                    Email NVARCHAR(255) UNIQUE,
                    Age INT,
                    CreatedAt DATETIME2 DEFAULT GETDATE()
                )";

            var createPostTableSql = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Posts' AND xtype='U')
                CREATE TABLE Posts (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    UserId INT,
                    Title NVARCHAR(255) NOT NULL,
                    Content NTEXT,
                    CreatedAt DATETIME2 DEFAULT GETDATE(),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )";

            await connection.ExecuteAsync(createUserTableSql);
            await connection.ExecuteAsync(createPostTableSql);

            Logging.LogInfo("DapperSqlServerTest", "SQL Server 表创建成功（异步）");
        }

        /// <summary>
        /// 插入示例数据（异步）
        /// </summary>
        private async Task InsertSampleDataAsync(SqlConnection connection)
        {
            // 清空现有数据
            await connection.ExecuteAsync("DELETE FROM Posts");
            await connection.ExecuteAsync("DELETE FROM Users");

            // 插入用户
            var users = new[]
            {
                new { Name = "异步SQL张三", Email = "<EMAIL>", Age = 26 },
                new { Name = "异步SQL李四", Email = "<EMAIL>", Age = 31 },
                new { Name = "异步SQL王五", Email = "<EMAIL>", Age = 29 }
            };

            var insertUserSql = "INSERT INTO Users (Name, Email, Age) VALUES (@Name, @Email, @Age)";
            var userCount = await connection.ExecuteAsync(insertUserSql, users);
            Logging.LogInfo("DapperSqlServerTest", $"异步插入了 {userCount} 个用户");

            // 插入文章
            var posts = new[]
            {
                new { UserId = 1, Title = "异步编程与SQL Server", Content = "在.NET中使用异步方法操作SQL Server..." },
                new { UserId = 2, Title = "Dapper与SQL Server最佳实践", Content = "使用Dapper操作SQL Server的技巧..." }
            };

            var insertPostSql = "INSERT INTO Posts (UserId, Title, Content) VALUES (@UserId, @Title, @Content)";
            var postCount = await connection.ExecuteAsync(insertPostSql, posts);
            Logging.LogInfo("DapperSqlServerTest", $"异步插入了 {postCount} 篇文章");
        }

        /// <summary>
        /// 查询数据（异步）
        /// </summary>
        private async Task QueryDataAsync(SqlConnection connection)
        {
            // 查询所有用户
            var users = await connection.QueryAsync<SqlServerUser>("SELECT * FROM Users ORDER BY Id");
            Logging.LogInfo("DapperSqlServerTest", $"异步查询到 {users.Count()} 个用户:");
            foreach (var user in users)
            {
                Logging.LogInfo("DapperSqlServerTest", $"  用户: {user.Name}, 邮箱: {user.Email}, 年龄: {user.Age}");
            }

            // 使用参数化查询和 T-SQL 特性
            var youngUsers = await connection.QueryAsync<SqlServerUser>(
                "SELECT * FROM Users WHERE Age < @MaxAge ORDER BY Age",
                new { MaxAge = 30 });

            Logging.LogInfo("DapperSqlServerTest", $"年龄小于30的用户 ({youngUsers.Count()} 个):");
            foreach (var user in youngUsers)
            {
                Logging.LogInfo("DapperSqlServerTest", $"  {user.Name}: {user.Age}岁");
            }

            // 使用 SQL Server 特有的功能（如 TOP）
            var topPosts = await connection.QueryAsync<SqlServerPost>(
                "SELECT TOP 3 * FROM Posts ORDER BY CreatedAt DESC");

            Logging.LogInfo("DapperSqlServerTest", $"最新的3篇文章:");
            foreach (var post in topPosts)
            {
                Logging.LogInfo("DapperSqlServerTest", $"  {post.Title} (创建时间: {post.CreatedAt:yyyy-MM-dd HH:mm})");
            }
        }

        #endregion

        #region 右键菜单方法

        /// <summary>
        /// 清空数据库
        /// </summary>
        [ContextMenu("清空SQL Server数据库")]
        public void ClearDatabase()
        {
            try
            {
                var connection = EstablishConnection();
                if (connection == null) return;

                using (connection)
                {
                    connection.Execute("DELETE FROM Posts");
                    connection.Execute("DELETE FROM Users");

                    Logging.LogInfo("DapperSqlServerTest", "SQL Server 数据库已清空");
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperSqlServerTest", $"清空数据库失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取数据库统计信息
        /// </summary>
        [ContextMenu("获取SQL Server统计信息")]
        public void GetDatabaseStats()
        {
            try
            {
                var connection = EstablishConnection();
                if (connection == null) return;

                using (connection)
                {
                    var userCount = connection.QuerySingle<int>("SELECT COUNT(*) FROM Users");
                    var postCount = connection.QuerySingle<int>("SELECT COUNT(*) FROM Posts");
                    var dbSize = connection.QuerySingle<decimal>(@"
                        SELECT SUM(size * 8.0 / 1024)
                        FROM sys.database_files
                        WHERE type = 0");

                    Logging.LogInfo("DapperSqlServerTest", "=== SQL Server 数据库统计 ===");
                    Logging.LogInfo("DapperSqlServerTest", $"用户数量: {userCount}");
                    Logging.LogInfo("DapperSqlServerTest", $"文章数量: {postCount}");
                    Logging.LogInfo("DapperSqlServerTest", $"数据库大小: {dbSize:F2} MB");
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperSqlServerTest", $"获取统计信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试连接
        /// </summary>
        [ContextMenu("测试SQL Server连接")]
        public void TestConnection()
        {
            var connection = EstablishConnection();
            if (connection != null)
            {
                connection.Close();
                connection.Dispose();
                Logging.LogInfo("DapperSqlServerTest", "SQL Server 连接测试成功");
            }
            else
            {
                Logging.LogError("DapperSqlServerTest", "SQL Server 连接测试失败");
            }
        }

        #endregion
    }

    #region SQL Server 数据模型

    /// <summary>
    /// SQL Server 用户数据模型
    /// </summary>
    public class SqlServerUser
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public int Age { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// SQL Server 文章数据模型
    /// </summary>
    public class SqlServerPost
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    #endregion
}
