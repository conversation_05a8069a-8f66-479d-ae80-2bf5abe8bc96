using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using UnityEngine;
using System.Data.SqlClient;
using System.Data;
using Dapper;
using BlastingDesign.Utils;

namespace BlastingDesign.Database.Demo
{
    /// <summary>
    /// Dapper SQL Server 高级功能演示
    /// 展示存储过程、事务、批量操作、窗口函数等 SQL Server 特有功能
    /// </summary>
    public class DapperSqlServerAdvancedDemo : MonoBehaviour
    {
        [Header("SQL Server 高级演示配置")]
        [SerializeField] private KeyCode storedProcTestKey = KeyCode.F9;
        [SerializeField] private KeyCode transactionTestKey = KeyCode.F10;
        [SerializeField] private KeyCode windowFunctionKey = KeyCode.F11;
        [SerializeField] private KeyCode bulkOperationKey = KeyCode.F12;
        [SerializeField] private bool showGUI = true;

        private string connectionString;
        private DapperSqlServerTest sqlServerTest;

        void Start()
        {
            // 获取基础的 SQL Server 测试组件
            sqlServerTest = GetComponent<DapperSqlServerTest>();
            if (sqlServerTest == null)
            {
                sqlServerTest = gameObject.AddComponent<DapperSqlServerTest>();
            }

            // 使用相同的连接配置
            connectionString = GetConnectionString();
            ShowInstructions();
        }

        void Update()
        {
            if (Input.GetKeyDown(storedProcTestKey))
            {
                _ = TestStoredProceduresAsync();
            }

            if (Input.GetKeyDown(transactionTestKey))
            {
                _ = TestAdvancedTransactionsAsync();
            }

            if (Input.GetKeyDown(windowFunctionKey))
            {
                _ = TestWindowFunctionsAsync();
            }

            if (Input.GetKeyDown(bulkOperationKey))
            {
                _ = TestBulkOperationsAsync();
            }
        }

        /// <summary>
        /// 获取连接字符串
        /// </summary>
        private string GetConnectionString()
        {
            // 这里使用简化的连接字符串，实际使用时应该从配置中读取
            return "Server=localhost;Database=DapperTestDB;Integrated Security=true;Connection Timeout=30;";
        }

        /// <summary>
        /// 显示使用说明
        /// </summary>
        private void ShowInstructions()
        {
            Logging.LogInfo("DapperSqlServerAdvancedDemo", "=== Dapper SQL Server 高级功能演示 ===");
            Logging.LogInfo("DapperSqlServerAdvancedDemo", "");
            Logging.LogInfo("DapperSqlServerAdvancedDemo", "快捷键操作：");
            Logging.LogInfo("DapperSqlServerAdvancedDemo", $"{storedProcTestKey} - 存储过程测试");
            Logging.LogInfo("DapperSqlServerAdvancedDemo", $"{transactionTestKey} - 高级事务测试");
            Logging.LogInfo("DapperSqlServerAdvancedDemo", $"{windowFunctionKey} - 窗口函数测试");
            Logging.LogInfo("DapperSqlServerAdvancedDemo", $"{bulkOperationKey} - 批量操作测试");
            Logging.LogInfo("DapperSqlServerAdvancedDemo", "");
            Logging.LogInfo("DapperSqlServerAdvancedDemo", "SQL Server 特有功能：");
            Logging.LogInfo("DapperSqlServerAdvancedDemo", "• 存储过程和函数");
            Logging.LogInfo("DapperSqlServerAdvancedDemo", "• 高级事务和隔离级别");
            Logging.LogInfo("DapperSqlServerAdvancedDemo", "• 窗口函数和分析函数");
            Logging.LogInfo("DapperSqlServerAdvancedDemo", "• 批量操作和表值参数");
            Logging.LogInfo("DapperSqlServerAdvancedDemo", "• CTE 和递归查询");
        }

        /// <summary>
        /// 测试存储过程
        /// </summary>
        private async Task TestStoredProceduresAsync()
        {
            Logging.LogInfo("DapperSqlServerAdvancedDemo", "开始存储过程测试...");

            try
            {
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                // 创建存储过程
                await CreateStoredProceduresAsync(connection);

                // 测试存储过程调用
                await TestStoredProcedureCallsAsync(connection);

                Logging.LogInfo("DapperSqlServerAdvancedDemo", "存储过程测试完成");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperSqlServerAdvancedDemo", $"存储过程测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建存储过程
        /// </summary>
        private async Task CreateStoredProceduresAsync(SqlConnection connection)
        {
            // 创建获取用户统计的存储过程
            var createUserStatsSP = @"
                IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'GetUserStats')
                    DROP PROCEDURE GetUserStats
                GO
                CREATE PROCEDURE GetUserStats
                    @MinAge INT = 0,
                    @TotalUsers INT OUTPUT,
                    @AverageAge DECIMAL(5,2) OUTPUT
                AS
                BEGIN
                    SET NOCOUNT ON;
                    
                    SELECT @TotalUsers = COUNT(*), @AverageAge = AVG(CAST(Age AS DECIMAL(5,2)))
                    FROM Users 
                    WHERE Age >= @MinAge;
                    
                    SELECT Id, Name, Email, Age, CreatedAt
                    FROM Users 
                    WHERE Age >= @MinAge
                    ORDER BY Age DESC;
                END";

            // 创建用户管理存储过程
            var createUserManagementSP = @"
                IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'ManageUser')
                    DROP PROCEDURE ManageUser
                GO
                CREATE PROCEDURE ManageUser
                    @Action NVARCHAR(10),
                    @UserId INT = NULL,
                    @Name NVARCHAR(100) = NULL,
                    @Email NVARCHAR(255) = NULL,
                    @Age INT = NULL,
                    @Result NVARCHAR(255) OUTPUT
                AS
                BEGIN
                    SET NOCOUNT ON;
                    
                    IF @Action = 'INSERT'
                    BEGIN
                        INSERT INTO Users (Name, Email, Age) VALUES (@Name, @Email, @Age);
                        SET @Result = 'User inserted successfully. ID: ' + CAST(SCOPE_IDENTITY() AS NVARCHAR(10));
                    END
                    ELSE IF @Action = 'UPDATE'
                    BEGIN
                        UPDATE Users SET Name = @Name, Email = @Email, Age = @Age WHERE Id = @UserId;
                        SET @Result = 'User updated successfully. Rows affected: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
                    END
                    ELSE IF @Action = 'DELETE'
                    BEGIN
                        DELETE FROM Users WHERE Id = @UserId;
                        SET @Result = 'User deleted successfully. Rows affected: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
                    END
                    ELSE
                    BEGIN
                        SET @Result = 'Invalid action specified';
                    END
                END";

            await connection.ExecuteAsync(createUserStatsSP);
            await connection.ExecuteAsync(createUserManagementSP);

            Logging.LogInfo("DapperSqlServerAdvancedDemo", "存储过程创建成功");
        }

        /// <summary>
        /// 测试存储过程调用
        /// </summary>
        private async Task TestStoredProcedureCallsAsync(SqlConnection connection)
        {
            // 测试带输出参数的存储过程
            var parameters = new DynamicParameters();
            parameters.Add("@MinAge", 25);
            parameters.Add("@TotalUsers", dbType: DbType.Int32, direction: ParameterDirection.Output);
            parameters.Add("@AverageAge", dbType: DbType.Decimal, direction: ParameterDirection.Output);

            var users = await connection.QueryAsync<SqlServerUser>("GetUserStats", parameters, commandType: CommandType.StoredProcedure);

            var totalUsers = parameters.Get<int>("@TotalUsers");
            var averageAge = parameters.Get<decimal>("@AverageAge");

            Logging.LogInfo("DapperSqlServerAdvancedDemo", $"存储过程结果 - 总用户数: {totalUsers}, 平均年龄: {averageAge:F1}");
            Logging.LogInfo("DapperSqlServerAdvancedDemo", $"返回的用户列表: {users.Count()} 个");

            // 测试用户管理存储过程
            var managementParams = new DynamicParameters();
            managementParams.Add("@Action", "INSERT");
            managementParams.Add("@Name", "存储过程用户");
            managementParams.Add("@Email", "<EMAIL>");
            managementParams.Add("@Age", 35);
            managementParams.Add("@Result", dbType: DbType.String, size: 255, direction: ParameterDirection.Output);

            await connection.ExecuteAsync("ManageUser", managementParams, commandType: CommandType.StoredProcedure);

            var result = managementParams.Get<string>("@Result");
            Logging.LogInfo("DapperSqlServerAdvancedDemo", $"用户管理结果: {result}");
        }

        /// <summary>
        /// 测试高级事务
        /// </summary>
        private async Task TestAdvancedTransactionsAsync()
        {
            Logging.LogInfo("DapperSqlServerAdvancedDemo", "开始高级事务测试...");

            try
            {
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                // 测试不同隔离级别的事务
                await TestIsolationLevelsAsync(connection);

                // 测试嵌套事务（保存点）
                await TestSavepointsAsync(connection);

                Logging.LogInfo("DapperSqlServerAdvancedDemo", "高级事务测试完成");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperSqlServerAdvancedDemo", $"高级事务测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试隔离级别
        /// </summary>
        private async Task TestIsolationLevelsAsync(SqlConnection connection)
        {
            // 测试 READ COMMITTED 隔离级别
            using (var transaction = connection.BeginTransaction(IsolationLevel.ReadCommitted))
            {
                try
                {
                    Logging.LogInfo("DapperSqlServerAdvancedDemo", "测试 READ COMMITTED 隔离级别...");

                    var userCount = await connection.QuerySingleAsync<int>(
                        "SELECT COUNT(*) FROM Users WITH (READCOMMITTED)", transaction: transaction);

                    Logging.LogInfo("DapperSqlServerAdvancedDemo", $"READ COMMITTED - 用户数量: {userCount}");

                    await transaction.CommitAsync();
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }

            // 测试 SNAPSHOT 隔离级别（如果启用）
            try
            {
                using var transaction = connection.BeginTransaction(IsolationLevel.Snapshot);
                Logging.LogInfo("DapperSqlServerAdvancedDemo", "测试 SNAPSHOT 隔离级别...");

                var users = await connection.QueryAsync<SqlServerUser>(
                    "SELECT TOP 5 * FROM Users ORDER BY Id", transaction: transaction);

                Logging.LogInfo("DapperSqlServerAdvancedDemo", $"SNAPSHOT - 查询到 {users.Count()} 个用户");

                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                Logging.LogWarning("DapperSqlServerAdvancedDemo", $"SNAPSHOT 隔离级别测试失败（可能未启用）: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试保存点
        /// </summary>
        private async Task TestSavepointsAsync(SqlConnection connection)
        {
            using var transaction = connection.BeginTransaction();
            try
            {
                Logging.LogInfo("DapperSqlServerAdvancedDemo", "测试保存点功能...");

                // 插入第一个用户
                await connection.ExecuteAsync(
                    "INSERT INTO Users (Name, Email, Age) VALUES (@Name, @Email, @Age)",
                    new { Name = "保存点用户1", Email = "<EMAIL>", Age = 30 },
                    transaction);

                // 创建保存点
                await connection.ExecuteAsync("SAVE TRANSACTION SavePoint1", transaction: transaction);

                // 插入第二个用户
                await connection.ExecuteAsync(
                    "INSERT INTO Users (Name, Email, Age) VALUES (@Name, @Email, @Age)",
                    new { Name = "保存点用户2", Email = "<EMAIL>", Age = 32 },
                    transaction);

                // 模拟错误，回滚到保存点
                await connection.ExecuteAsync("ROLLBACK TRANSACTION SavePoint1", transaction: transaction);

                Logging.LogInfo("DapperSqlServerAdvancedDemo", "已回滚到保存点，只保留第一个用户");

                await transaction.CommitAsync();
                Logging.LogInfo("DapperSqlServerAdvancedDemo", "保存点测试完成");
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 测试窗口函数
        /// </summary>
        private async Task TestWindowFunctionsAsync()
        {
            Logging.LogInfo("DapperSqlServerAdvancedDemo", "开始窗口函数测试...");

            try
            {
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                // 确保有足够的测试数据
                await EnsureTestDataAsync(connection);

                // 测试排名函数
                await TestRankingFunctionsAsync(connection);

                // 测试分析函数
                await TestAnalyticalFunctionsAsync(connection);

                // 测试 CTE 和递归查询
                await TestCTEAndRecursiveQueriesAsync(connection);

                Logging.LogInfo("DapperSqlServerAdvancedDemo", "窗口函数测试完成");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperSqlServerAdvancedDemo", $"窗口函数测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 确保有测试数据
        /// </summary>
        private async Task EnsureTestDataAsync(SqlConnection connection)
        {
            var userCount = await connection.QuerySingleAsync<int>("SELECT COUNT(*) FROM Users");
            if (userCount < 10)
            {
                // 添加更多测试数据
                var additionalUsers = new List<object>();
                for (int i = 1; i <= 15; i++)
                {
                    additionalUsers.Add(new
                    {
                        Name = $"测试用户{i}",
                        Email = $"test{i}@example.com",
                        Age = 20 + (i % 40)
                    });
                }

                await connection.ExecuteAsync(
                    "INSERT INTO Users (Name, Email, Age) VALUES (@Name, @Email, @Age)",
                    additionalUsers);

                Logging.LogInfo("DapperSqlServerAdvancedDemo", $"添加了 {additionalUsers.Count} 个测试用户");
            }
        }

        /// <summary>
        /// 测试排名函数
        /// </summary>
        private async Task TestRankingFunctionsAsync(SqlConnection connection)
        {
            var rankingQuery = @"
                SELECT
                    Name,
                    Age,
                    ROW_NUMBER() OVER (ORDER BY Age DESC) as RowNum,
                    RANK() OVER (ORDER BY Age DESC) as Rank,
                    DENSE_RANK() OVER (ORDER BY Age DESC) as DenseRank,
                    NTILE(4) OVER (ORDER BY Age DESC) as Quartile
                FROM Users
                ORDER BY Age DESC";

            var results = await connection.QueryAsync(rankingQuery);

            Logging.LogInfo("DapperSqlServerAdvancedDemo", "排名函数结果（前5名）:");
            foreach (var item in results.Take(5))
            {
                Logging.LogInfo("DapperSqlServerAdvancedDemo",
                    $"  {item.Name} (年龄: {item.Age}) - 行号: {item.RowNum}, 排名: {item.Rank}, 密集排名: {item.DenseRank}, 四分位: {item.Quartile}");
            }
        }

        /// <summary>
        /// 测试分析函数
        /// </summary>
        private async Task TestAnalyticalFunctionsAsync(SqlConnection connection)
        {
            var analyticalQuery = @"
                SELECT
                    Name,
                    Age,
                    AVG(CAST(Age AS FLOAT)) OVER () as OverallAvgAge,
                    AVG(CAST(Age AS FLOAT)) OVER (ORDER BY Id ROWS BETWEEN 2 PRECEDING AND 2 FOLLOWING) as MovingAvgAge,
                    LAG(Age, 1) OVER (ORDER BY Age) as PrevAge,
                    LEAD(Age, 1) OVER (ORDER BY Age) as NextAge,
                    FIRST_VALUE(Name) OVER (ORDER BY Age DESC) as OldestUser,
                    LAST_VALUE(Name) OVER (ORDER BY Age DESC ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) as YoungestUser
                FROM Users
                ORDER BY Age DESC";

            var results = await connection.QueryAsync(analyticalQuery);

            Logging.LogInfo("DapperSqlServerAdvancedDemo", "分析函数结果（前3名）:");
            foreach (var item in results.Take(3))
            {
                Logging.LogInfo("DapperSqlServerAdvancedDemo",
                    $"  {item.Name} (年龄: {item.Age}) - 平均年龄: {item.OverallAvgAge:F1}, 移动平均: {item.MovingAvgAge:F1}");
                Logging.LogInfo("DapperSqlServerAdvancedDemo",
                    $"    前一个: {item.PrevAge}, 后一个: {item.NextAge}, 最老: {item.OldestUser}, 最年轻: {item.YoungestUser}");
            }
        }

        /// <summary>
        /// 测试 CTE 和递归查询
        /// </summary>
        private async Task TestCTEAndRecursiveQueriesAsync(SqlConnection connection)
        {
            // 创建层次结构表（如果不存在）
            await connection.ExecuteAsync(@"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Categories' AND xtype='U')
                CREATE TABLE Categories (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    Name NVARCHAR(100),
                    ParentId INT NULL,
                    FOREIGN KEY (ParentId) REFERENCES Categories(Id)
                )");

            // 插入层次数据
            await connection.ExecuteAsync("DELETE FROM Categories");
            await connection.ExecuteAsync(@"
                INSERT INTO Categories (Name, ParentId) VALUES
                ('根目录', NULL),
                ('技术', 1),
                ('生活', 1),
                ('编程', 2),
                ('数据库', 2),
                ('C#', 4),
                ('SQL Server', 5),
                ('Unity', 4)");

            // 使用递归 CTE 查询层次结构
            var recursiveQuery = @"
                WITH CategoryHierarchy AS (
                    -- 锚点：根节点
                    SELECT Id, Name, ParentId, 0 as Level, CAST(Name AS NVARCHAR(500)) as Path
                    FROM Categories
                    WHERE ParentId IS NULL

                    UNION ALL

                    -- 递归部分
                    SELECT c.Id, c.Name, c.ParentId, ch.Level + 1,
                           CAST(ch.Path + ' -> ' + c.Name AS NVARCHAR(500))
                    FROM Categories c
                    INNER JOIN CategoryHierarchy ch ON c.ParentId = ch.Id
                )
                SELECT Id, Name, Level, Path
                FROM CategoryHierarchy
                ORDER BY Path";

            var hierarchy = await connection.QueryAsync(recursiveQuery);

            Logging.LogInfo("DapperSqlServerAdvancedDemo", "递归 CTE 层次结构:");
            foreach (var item in hierarchy)
            {
                var indent = new string(' ', item.Level * 2);
                Logging.LogInfo("DapperSqlServerAdvancedDemo", $"  {indent}{item.Name} (级别: {item.Level})");
                Logging.LogInfo("DapperSqlServerAdvancedDemo", $"  {indent}路径: {item.Path}");
            }
        }

        /// <summary>
        /// 测试批量操作
        /// </summary>
        private async Task TestBulkOperationsAsync()
        {
            Logging.LogInfo("DapperSqlServerAdvancedDemo", "开始批量操作测试...");

            try
            {
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                // 测试批量插入
                await TestBulkInsertAsync(connection);

                // 测试表值参数
                await TestTableValuedParametersAsync(connection);

                // 测试 MERGE 语句
                await TestMergeStatementAsync(connection);

                Logging.LogInfo("DapperSqlServerAdvancedDemo", "批量操作测试完成");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperSqlServerAdvancedDemo", $"批量操作测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试批量插入
        /// </summary>
        private async Task TestBulkInsertAsync(SqlConnection connection)
        {
            var startTime = DateTime.Now;

            // 生成大量测试数据
            var bulkUsers = new List<object>();
            for (int i = 1; i <= 1000; i++)
            {
                bulkUsers.Add(new
                {
                    Name = $"批量用户{i}",
                    Email = $"bulk{i}@example.com",
                    Age = 18 + (i % 50)
                });
            }

            // 使用事务进行批量插入
            using var transaction = connection.BeginTransaction();
            try
            {
                var insertedCount = await connection.ExecuteAsync(
                    "INSERT INTO Users (Name, Email, Age) VALUES (@Name, @Email, @Age)",
                    bulkUsers, transaction);

                await transaction.CommitAsync();

                var duration = DateTime.Now - startTime;
                Logging.LogInfo("DapperSqlServerAdvancedDemo",
                    $"批量插入 {insertedCount} 个用户，耗时: {duration.TotalMilliseconds:F0}ms");
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 测试表值参数
        /// </summary>
        private async Task TestTableValuedParametersAsync(SqlConnection connection)
        {
            // 创建用户定义的表类型
            await connection.ExecuteAsync(@"
                IF NOT EXISTS (SELECT * FROM sys.types WHERE name = 'UserTableType')
                CREATE TYPE UserTableType AS TABLE (
                    Name NVARCHAR(100),
                    Email NVARCHAR(255),
                    Age INT
                )");

            // 创建使用表值参数的存储过程
            await connection.ExecuteAsync(@"
                IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'BulkInsertUsers')
                    DROP PROCEDURE BulkInsertUsers
                GO
                CREATE PROCEDURE BulkInsertUsers
                    @Users UserTableType READONLY
                AS
                BEGIN
                    INSERT INTO Users (Name, Email, Age)
                    SELECT Name, Email, Age FROM @Users;

                    SELECT @@ROWCOUNT as InsertedCount;
                END");

            Logging.LogInfo("DapperSqlServerAdvancedDemo", "表值参数功能已创建（需要 SqlBulkCopy 或其他方式实现）");
        }

        /// <summary>
        /// 测试 MERGE 语句
        /// </summary>
        private async Task TestMergeStatementAsync(SqlConnection connection)
        {
            // 创建临时表
            await connection.ExecuteAsync(@"
                CREATE TABLE #TempUsers (
                    Name NVARCHAR(100),
                    Email NVARCHAR(255),
                    Age INT
                )");

            // 插入一些数据到临时表
            await connection.ExecuteAsync(@"
                INSERT INTO #TempUsers (Name, Email, Age) VALUES
                ('更新用户1', '<EMAIL>', 25),
                ('更新用户2', '<EMAIL>', 30),
                ('新用户1', '<EMAIL>', 35)");

            // 使用 MERGE 语句
            var mergeQuery = @"
                MERGE Users AS target
                USING #TempUsers AS source ON target.Email = source.Email
                WHEN MATCHED THEN
                    UPDATE SET Name = source.Name, Age = source.Age
                WHEN NOT MATCHED THEN
                    INSERT (Name, Email, Age) VALUES (source.Name, source.Email, source.Age)
                OUTPUT $action, inserted.Name, inserted.Email;";

            var mergeResults = await connection.QueryAsync(mergeQuery);

            Logging.LogInfo("DapperSqlServerAdvancedDemo", "MERGE 操作结果:");
            foreach (var result in mergeResults)
            {
                Logging.LogInfo("DapperSqlServerAdvancedDemo", $"  操作: {result.action}, 用户: {result.Name}");
            }

            // 清理临时表
            await connection.ExecuteAsync("DROP TABLE #TempUsers");
        }

        /// <summary>
        /// GUI 显示
        /// </summary>
        void OnGUI()
        {
            if (!showGUI) return;

            GUILayout.BeginArea(new Rect(520, 10, 500, 300));
            GUILayout.BeginVertical("box");

            GUILayout.Label("Dapper SQL Server 高级功能演示", GUI.skin.label);
            GUILayout.Space(5);

            GUILayout.BeginHorizontal();
            if (GUILayout.Button($"存储过程 ({storedProcTestKey})"))
            {
                _ = TestStoredProceduresAsync();
            }
            if (GUILayout.Button($"高级事务 ({transactionTestKey})"))
            {
                _ = TestAdvancedTransactionsAsync();
            }
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            if (GUILayout.Button($"窗口函数 ({windowFunctionKey})"))
            {
                _ = TestWindowFunctionsAsync();
            }
            if (GUILayout.Button($"批量操作 ({bulkOperationKey})"))
            {
                _ = TestBulkOperationsAsync();
            }
            GUILayout.EndHorizontal();

            GUILayout.Space(10);

            GUILayout.Label("SQL Server 特有功能:", GUI.skin.label);
            GUILayout.Label("• T-SQL 存储过程和函数", GUI.skin.label);
            GUILayout.Label("• 高级事务和隔离级别", GUI.skin.label);
            GUILayout.Label("• 窗口函数 (ROW_NUMBER, RANK, etc.)", GUI.skin.label);
            GUILayout.Label("• CTE 和递归查询", GUI.skin.label);
            GUILayout.Label("• MERGE 语句", GUI.skin.label);
            GUILayout.Label("• 表值参数", GUI.skin.label);

            GUILayout.Space(10);

            if (GUILayout.Button("清空测试数据"))
            {
                ClearTestData();
            }

            GUILayout.EndVertical();
            GUILayout.EndArea();
        }

        /// <summary>
        /// 清空测试数据
        /// </summary>
        [ContextMenu("清空SQL Server测试数据")]
        public void ClearTestData()
        {
            try
            {
                using var connection = new SqlConnection(connectionString);
                connection.Open();

                connection.Execute("DELETE FROM Posts");
                connection.Execute("DELETE FROM Users");
                connection.Execute("IF EXISTS (SELECT * FROM sysobjects WHERE name='Categories' AND xtype='U') DELETE FROM Categories");

                Logging.LogInfo("DapperSqlServerAdvancedDemo", "SQL Server 测试数据已清空");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperSqlServerAdvancedDemo", $"清空数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查 SQL Server 功能
        /// </summary>
        [ContextMenu("检查SQL Server功能")]
        public void CheckSqlServerFeatures()
        {
            try
            {
                using var connection = new SqlConnection(connectionString);
                connection.Open();

                // 检查 SQL Server 版本
                var version = connection.QuerySingle<string>("SELECT @@VERSION");
                Logging.LogInfo("DapperSqlServerAdvancedDemo", $"SQL Server 版本: {version.Substring(0, Math.Min(100, version.Length))}...");

                // 检查数据库兼容级别
                var compatLevel = connection.QuerySingle<int>("SELECT compatibility_level FROM sys.databases WHERE name = DB_NAME()");
                Logging.LogInfo("DapperSqlServerAdvancedDemo", $"数据库兼容级别: {compatLevel}");

                // 检查是否启用了 SNAPSHOT 隔离
                var snapshotEnabled = connection.QuerySingle<bool>(@"
                    SELECT is_read_committed_snapshot_on
                    FROM sys.databases
                    WHERE name = DB_NAME()");
                Logging.LogInfo("DapperSqlServerAdvancedDemo", $"SNAPSHOT 隔离已启用: {snapshotEnabled}");

                Logging.LogInfo("DapperSqlServerAdvancedDemo", "SQL Server 功能检查完成");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperSqlServerAdvancedDemo", $"功能检查失败: {ex.Message}");
            }
        }
    }
}
