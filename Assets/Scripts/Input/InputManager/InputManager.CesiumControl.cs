using UnityEngine;
using UnityEngine.InputSystem;

/// <summary>
/// InputManager Cesium控制 - Cesium地形控制和交互球体管理
/// </summary>
public partial class InputManager
{
    #region Cesium地形控制方法

    /// <summary>
    /// 切换Cesium地形的启用状态
    /// </summary>
    public void ToggleCesiumTerrain()
    {
        if (cesiumGeoreference == null)
        {
            // 尝试自动查找CesiumGeoreference对象
            cesiumGeoreference = GameObject.Find("CesiumGeoreference");

            if (cesiumGeoreference == null)
            {
                Debug.LogWarning("未找到CesiumGeoreference对象！请在InputManager的Inspector中设置cesiumGeoreference字段，或确保场景中存在名为'CesiumGeoreference'的GameObject。");
                return;
            }
        }

        // 切换激活状态
        bool newState = !cesiumGeoreference.activeSelf;
        cesiumGeoreference.SetActive(newState);

        string stateText = newState ? "启用" : "禁用";
        Debug.Log($"Cesium地形已{stateText} (按{terrainToggleKey}键切换)");
    }

    /// <summary>
    /// 启用Cesium地形
    /// </summary>
    public void EnableCesiumTerrain()
    {
        if (cesiumGeoreference != null)
        {
            cesiumGeoreference.SetActive(true);
            Debug.Log("Cesium地形已启用");
        }
        else
        {
            Debug.LogWarning("CesiumGeoreference对象引用为空！");
        }
    }

    /// <summary>
    /// 禁用Cesium地形
    /// </summary>
    public void DisableCesiumTerrain()
    {
        if (cesiumGeoreference != null)
        {
            cesiumGeoreference.SetActive(false);
            Debug.Log("Cesium地形已禁用");
        }
        else
        {
            Debug.LogWarning("CesiumGeoreference对象引用为空！");
        }
    }

    /// <summary>
    /// 检查Cesium地形是否启用
    /// </summary>
    /// <returns>如果地形启用返回true，否则返回false</returns>
    public bool IsCesiumTerrainEnabled()
    {
        if (cesiumGeoreference != null)
        {
            return cesiumGeoreference.activeSelf;
        }
        return false;
    }

    #endregion

    #region Cesium交互球体管理方法

    /// <summary>
    /// 更新Cesium交互球体位置
    /// </summary>
    private void UpdateCesiumInteractionSphere()
    {
        // 只有在Cesium地形启用且显示交互球体时才更新
        if (!IsCesiumTerrainEnabled() || !showCesiumInteraction)
        {
            if (cesiumInteractionSphere != null)
            {
                cesiumInteractionSphere.SetActive(false);
            }
            return;
        }

        // 获取鼠标位置
        Vector2 mousePosition = Mouse.current.position.ReadValue();

        // 计算射线与Cesium模型的交点
        Vector3 hitPoint;
        if (RaycastToCesiumTileset(mousePosition, out hitPoint))
        {
            // 确保球体存在
            if (cesiumInteractionSphere == null)
            {
                CreateCesiumInteractionSphere();
            }

            // 更新球体位置
            if (cesiumInteractionSphere != null)
            {
                cesiumInteractionSphere.SetActive(true);
                cesiumInteractionSphere.transform.position = hitPoint;
            }
        }
        else
        {
            // 如果没有击中Cesium模型，隐藏球体
            if (cesiumInteractionSphere != null)
            {
                cesiumInteractionSphere.SetActive(false);
            }
        }
    }

    /// <summary>
    /// 创建Cesium交互球体
    /// </summary>
    private void CreateCesiumInteractionSphere()
    {
        if (cesiumInteractionSphere != null) return;

        // 创建球体
        cesiumInteractionSphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        cesiumInteractionSphere.name = "Cesium Interaction Sphere";
        cesiumInteractionSphere.transform.localScale = Vector3.one * sphereSize;

        // 移除碰撞器，避免干扰射线检测
        if (cesiumInteractionSphere.TryGetComponent<Collider>(out Collider sphereCollider))
        {
            DestroyImmediate(sphereCollider);
        }

        // 设置材质
        Renderer sphereRenderer = cesiumInteractionSphere.GetComponent<Renderer>();
        if (sphereRenderer != null && MaterialManager.Instance != null)
        {
            Material sphereMaterial = MaterialManager.Instance.CreateDynamicMaterial(sphereColor, 0.7f, 0.5f);
            sphereRenderer.material = sphereMaterial;
        }

        // 初始状态为隐藏
        cesiumInteractionSphere.SetActive(false);
    }

    /// <summary>
    /// 销毁Cesium交互球体
    /// </summary>
    public void DestroyCesiumInteractionSphere()
    {
        if (cesiumInteractionSphere != null)
        {
            DestroyImmediate(cesiumInteractionSphere);
            cesiumInteractionSphere = null;
        }
    }

    #endregion
}