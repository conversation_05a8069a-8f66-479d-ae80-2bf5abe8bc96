using UnityEngine;
using UnityEngine.InputSystem;
using DG.Tweening;

/// <summary>
/// 缩放敏感度级别配置
/// </summary>
[System.Serializable]
public class ZoomSensitivityLevel
{
    [Tooltip("距离阈值 - 当相机距离焦点超过此值时使用该敏感度")]
    public float distanceThreshold;
    
    [Tooltip("敏感度倍数 - 相对于基础缩放敏感度的倍数")]
    public float sensitivityMultiplier;
}

/// <summary>
/// InputManager配置管理 - 所有配置字段和验证方法
/// </summary>
public partial class InputManager
{
    #region 配置字段

    [Header("输入系统设置")]
    public InputActionAsset inputActions;

    [Header("相机控制")]
    public Camera mainCamera;

    [Header("旋转设置")]
    public float rotationSensitivity = 2f;
    public Transform rotationCenter;
    public float minVerticalAngle = 30f;
    public float maxVerticalAngle = 75f;
    public bool useScreenCenterRotation = true;
    public bool invertVerticalRotation = false;

    [Header("平移设置")]
    public float panSensitivity = 1f;
    public bool useDistanceBasedPanSensitivity = true;
    public float panSensitivityMultiplier = 1f;

    [Header("缩放设置")]
    public float zoomSensitivity = 2f;
    public float minZoomDistance = 1f;
    public float maxZoomDistance = 50f;
    public float smoothZoomSensitivity = 0.1f;
    public bool useScreenCenterZoom = true;
    public float zoomEasingDuration = 0.3f;
    public Ease zoomEasingCurve = Ease.OutQuart;
    public bool useTerrainAdaptiveZoom = true;
    public bool useTerrainAdaptivePan = true;
    public float terrainFollowSmoothness = 0.1f;
    public float adaptiveMinZoomDistance = 0.5f;

    [Header("动态缩放设置")]
    public bool useDistanceBasedZoomSensitivity = true;
    [SerializeField] private ZoomSensitivityLevel[] zoomSensitivityLevels = new ZoomSensitivityLevel[]
    {
        new ZoomSensitivityLevel { distanceThreshold = 2f, sensitivityMultiplier = 0.2f },
        new ZoomSensitivityLevel { distanceThreshold = 5f, sensitivityMultiplier = 0.5f },
        new ZoomSensitivityLevel { distanceThreshold = 10f, sensitivityMultiplier = 1.0f },
        new ZoomSensitivityLevel { distanceThreshold = 20f, sensitivityMultiplier = 2.0f },
        new ZoomSensitivityLevel { distanceThreshold = 50f, sensitivityMultiplier = 4.0f }
    };

    [Header("平滑设置")]
    public float smoothTime = 0.1f;

    [Header("键盘移动设置")]
    public float keyboardMoveSensitivity = 10f;
    public float verticalMoveSensitivity = 5f;
    public float keyboardSpeedMultiplier = 3f;
    public bool useDistanceBasedKeyboardSensitivity = true;

    [Header("相机初始化设置")]
    public float initialCameraHeight = 10f;
    public float initialCameraAngle = 45f;
    public bool autoSetInitialPosition = true;

    [Header("基面检测设置")]
    public LayerMask groundLayerMask = 1;
    public float groundPlaneY = 0f;
    public bool usePhysicsRaycast = true;
    public bool showDebugRaycast = false;

    [Header("Cesium地形控制")]
    public GameObject cesiumGeoreference;
    public KeyCode terrainToggleKey = KeyCode.T;

    [Header("Cesium交互球体")]
    public GameObject cesiumInteractionSphere;
    public LayerMask cesiumLayerMask = -1;
    public float sphereSize = 0.2f;
    public Color sphereColor = Color.red;
    public bool showCesiumInteraction = true;

    [Header("鼠标包裹设置")]
    public bool enableMouseWrapping = true;
    public float wrapBorderSize = 2f;
    public float wrapCooldown = 0.0f;
    public float wrapDeadZone = 2f;

    [Header("选择设置")]
    public SelectionManager selectionManager;
    public bool enableSelection = true;
    public LayerMask selectionLayerMask = -1;

    [Header("EventSystem设置")]
    public bool useEventSystemManager = true;

    [Header("事件优先级管理")]
    public bool useEventPriorityManager = true;

    [Header("相机高度自适应")]
    public bool useCameraHeightAdaptation = true;

    [Header("调试UI设置")]
    [Tooltip("是否在OnGUI中显示调试信息")]
    public bool showDebugUI = true;

    #endregion

    #region 配置验证方法

    /// <summary>
    /// 验证移动设置参数
    /// </summary>
    void ValidateMovementSettings()
    {
        // 确保移动设置在合理范围内
        keyboardMoveSensitivity = Mathf.Max(0.1f, keyboardMoveSensitivity);
        verticalMoveSensitivity = Mathf.Max(0.1f, verticalMoveSensitivity);
        keyboardSpeedMultiplier = Mathf.Max(1f, keyboardSpeedMultiplier);
        panSensitivity = Mathf.Max(0.1f, panSensitivity);
        panSensitivityMultiplier = Mathf.Max(0.1f, panSensitivityMultiplier);
        initialCameraHeight = Mathf.Max(1f, initialCameraHeight);
        initialCameraAngle = Mathf.Clamp(initialCameraAngle, minVerticalAngle, maxVerticalAngle);

        Debug.Log($"移动设置已验证 - 键盘移动敏感度: {keyboardMoveSensitivity}, 平移敏感度: {panSensitivity}, 动态敏感度: 已启用");
    }

    /// <summary>
    /// 验证缩放敏感度级别配置
    /// </summary>
    void ValidateZoomSensitivityLevels()
    {
        if (zoomSensitivityLevels == null || zoomSensitivityLevels.Length == 0)
        {
            Debug.LogWarning("[InputManager] 缩放敏感度级别未配置，将使用默认设置");
            return;
        }

        // 验证并修正配置
        for (int i = 0; i < zoomSensitivityLevels.Length; i++)
        {
            // 确保距离阈值为正数
            zoomSensitivityLevels[i].distanceThreshold = Mathf.Max(0.1f, zoomSensitivityLevels[i].distanceThreshold);
            
            // 确保敏感度倍数为正数
            zoomSensitivityLevels[i].sensitivityMultiplier = Mathf.Max(0.01f, zoomSensitivityLevels[i].sensitivityMultiplier);
            
            // 检查距离阈值是否递增
            if (i > 0 && zoomSensitivityLevels[i].distanceThreshold <= zoomSensitivityLevels[i - 1].distanceThreshold)
            {
                Debug.LogWarning($"[InputManager] 缩放敏感度级别 {i} 的距离阈值应大于前一级别");
                zoomSensitivityLevels[i].distanceThreshold = zoomSensitivityLevels[i - 1].distanceThreshold + 0.1f;
            }
        }

        Debug.Log($"[InputManager] 缩放敏感度级别已验证，共 {zoomSensitivityLevels.Length} 个级别");
    }

    /// <summary>
    /// Unity编辑器中重置组件时调用
    /// </summary>
    void Reset()
    {
        // 设置推荐的默认值
        rotationSensitivity = 2f;
        panSensitivity = 1f;
        useDistanceBasedPanSensitivity = true;
        panSensitivityMultiplier = 1f;
        zoomSensitivity = 2f;
        minZoomDistance = 1f;
        maxZoomDistance = 50f;
        smoothZoomSensitivity = 0.1f;
        smoothTime = 0.1f;

        // 键盘移动设置的推荐默认值
        keyboardMoveSensitivity = 10f;
        verticalMoveSensitivity = 5f;
        keyboardSpeedMultiplier = 3f;
        useDistanceBasedKeyboardSensitivity = true;

        // 初始相机设置
        initialCameraHeight = 10f;
        initialCameraAngle = 45f;
        autoSetInitialPosition = true;

        // 其他设置
        groundLayerMask = 1;
        groundPlaneY = 0f;
        usePhysicsRaycast = true;
        showDebugRaycast = false;
        enableMouseWrapping = true;
        wrapBorderSize = 2f;
        wrapCooldown = 0.0f;
        wrapDeadZone = 2f;
        enableSelection = true;
        selectionLayerMask = -1;
        useEventSystemManager = true;
        useCameraHeightAdaptation = true;

        // Cesium地形控制设置
        terrainToggleKey = KeyCode.T;

        // 旋转和缩放设置（30-75度俯视角范围）
        minVerticalAngle = 30f;
        maxVerticalAngle = 75f;
        useScreenCenterRotation = true;
        invertVerticalRotation = false;
        useScreenCenterZoom = true;
        zoomEasingDuration = 0.3f;
        zoomEasingCurve = Ease.OutQuart;
        useTerrainAdaptiveZoom = true;
        useTerrainAdaptivePan = true;
        terrainFollowSmoothness = 0.1f;
        adaptiveMinZoomDistance = 0.5f;
        useDistanceBasedZoomSensitivity = true;
        
        // 调试UI设置
        showDebugUI = true;
    }

    #endregion
}