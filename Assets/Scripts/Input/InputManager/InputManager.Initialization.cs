using UnityEngine;
using UnityEngine.InputSystem;
using BlastingDesign.Core.Input;
using BlastingDesign.Core.Camera;

/// <summary>
/// InputManager初始化管理 - 所有初始化相关方法
/// </summary>
public partial class InputManager
{
    #region 主要初始化方法

    /// <summary>
    /// 初始化输入系统
    /// </summary>
    private void InitializeInputSystem()
    {
        // 如果没有指定输入资源，尝试加载默认的
        if (inputActions == null)
        {
            inputActions = Resources.Load<InputActionAsset>("InputSystem_Actions");
        }

        if (inputActions == null)
        {
            Debug.LogError("未找到InputSystem_Actions资源！请在Inspector中设置Input Actions字段。");
            return;
        }

        // 获取Action Maps
        playerActionMap = inputActions.FindActionMap("Player");
        uiActionMap = inputActions.FindActionMap("UI");

        if (playerActionMap == null)
        {
            Debug.LogError("未找到Player Action Map！");
            return;
        }

        if (uiActionMap == null)
        {
            Debug.LogError("未找到UI Action Map！");
            return;
        }

        // 获取具体的Actions
        moveAction = playerActionMap.FindAction("Move");
        lookAction = playerActionMap.FindAction("Look");
        scrollAction = playerActionMap.FindAction("Scroll");
        verticalMoveAction = playerActionMap.FindAction("VerticalMove");
    }

    /// <summary>
    /// 初始化相机
    /// </summary>
    private void InitializeCamera()
    {
        // 设置相机引用
        if (mainCamera == null)
        {
            mainCamera = Camera.main;
        }

        // 初始化旋转中心
        if (rotationCenter == null)
        {
            rotationCenterPosition = Vector3.zero;
        }
        else
        {
            rotationCenterPosition = rotationCenter.position;
        }

        // 设置初始相机位置
        if (autoSetInitialPosition && mainCamera != null)
        {
            SetInitialCameraPosition();
        }

        // 计算初始距离
        if (mainCamera != null)
        {
            currentDistance = Vector3.Distance(mainCamera.transform.position, rotationCenterPosition);
        }
    }

    /// <summary>
    /// 初始化所有管理器
    /// </summary>
    private void InitializeManagers()
    {
        InitializeSelectionManager();
        InitializeEventSystemManager();
        InitializeEventPriorityManager();
        InitializeCameraHeightAdaptation();
        ValidateConfigurations();
    }

    /// <summary>
    /// 验证所有配置
    /// </summary>
    private void ValidateConfigurations()
    {
        ValidateMovementSettings();
        ValidateZoomSensitivityLevels();
    }

    #endregion

    #region 具体初始化方法

    /// <summary>
    /// 初始化选择管理器
    /// </summary>
    void InitializeSelectionManager()
    {
        if (selectionManager == null)
        {
            selectionManager = FindFirstObjectByType<SelectionManager>();
        }

        if (selectionManager == null && enableSelection)
        {
            // 如果没有找到SelectionManager，创建一个
            GameObject selectionManagerGO = new GameObject("SelectionManager");
            selectionManager = selectionManagerGO.AddComponent<SelectionManager>();
            Debug.Log("自动创建了SelectionManager");
        }
    }

    /// <summary>
    /// 初始化EventSystem管理器
    /// </summary>
    void InitializeEventSystemManager()
    {
        if (!useEventSystemManager) return;

        // 查找或创建EventSystemManager
        eventSystemManager = FindFirstObjectByType<EventSystemManager>();
        if (eventSystemManager == null)
        {
            GameObject eventSystemManagerGO = new GameObject("EventSystemManager");
            eventSystemManager = eventSystemManagerGO.AddComponent<EventSystemManager>();
            Debug.Log("自动创建了EventSystemManager");
        }

        Debug.Log($"EventSystemManager已初始化");
    }

    /// <summary>
    /// 初始化事件优先级管理器
    /// </summary>
    void InitializeEventPriorityManager()
    {
        if (!useEventPriorityManager) return;

        // 查找或创建InputEventPriorityManager
        eventPriorityManager = InputEventPriorityManager.Instance;
        if (eventPriorityManager == null)
        {
            GameObject priorityManagerGO = new GameObject("InputEventPriorityManager");
            eventPriorityManager = priorityManagerGO.AddComponent<InputEventPriorityManager>();
            Debug.Log("自动创建了InputEventPriorityManager");
        }
        else
        {
            Debug.Log("已连接到现有的InputEventPriorityManager实例");
        }

        Debug.Log($"InputEventPriorityManager已初始化");
    }

    /// <summary>
    /// 初始化相机高度自适应系统
    /// </summary>
    void InitializeCameraHeightAdaptation()
    {
        if (!useCameraHeightAdaptation) return;

        // 查找现有的CameraHeightAdaptationSystem
        cameraHeightAdaptationSystem = FindFirstObjectByType<CameraHeightAdaptationSystem>();

        if (cameraHeightAdaptationSystem == null)
        {
            // 创建新的CameraHeightAdaptationSystem
            GameObject adaptationSystemGO = new GameObject("CameraHeightAdaptationSystem");
            cameraHeightAdaptationSystem = adaptationSystemGO.AddComponent<CameraHeightAdaptationSystem>();

            // 配置系统
            cameraHeightAdaptationSystem.targetCamera = mainCamera;
            cameraHeightAdaptationSystem.enableHeightAdaptation = true;
            cameraHeightAdaptationSystem.terrainLayerMask = groundLayerMask;
            cameraHeightAdaptationSystem.cesiumLayerMask = cesiumLayerMask;
            cameraHeightAdaptationSystem.groundPlaneY = groundPlaneY;
            cameraHeightAdaptationSystem.distanceSmoothness = 0.2f;
            cameraHeightAdaptationSystem.showDebugInfo = showDebugRaycast;
            cameraHeightAdaptationSystem.drawDebugRays = showDebugRaycast;

            Debug.Log("自动创建了CameraHeightAdaptationSystem");
        }
        else
        {
            // 配置现有系统
            cameraHeightAdaptationSystem.targetCamera = mainCamera;
            cameraHeightAdaptationSystem.enableHeightAdaptation = true;
            Debug.Log("已连接到现有的CameraHeightAdaptationSystem实例");
        }

        Debug.Log($"CameraHeightAdaptationSystem已初始化");
    }

    #endregion

    #region 相机初始化

    /// <summary>
    /// 设置初始相机位置：高度10m，45度俯视原点
    /// </summary>
    private void SetInitialCameraPosition()
    {
        if (mainCamera == null) return;

        // 计算初始位置（俯视角为45度，高度为10m）
        float angleRad = initialCameraAngle * Mathf.Deg2Rad;
        float horizontalDistance = initialCameraHeight / Mathf.Tan(angleRad);

        Vector3 initialPosition = new Vector3(0, initialCameraHeight, -horizontalDistance);
        mainCamera.transform.position = initialPosition;

        // 让相机朝向原点
        mainCamera.transform.LookAt(Vector3.zero);

        // 更新旋转中心为原点
        rotationCenterPosition = Vector3.zero;
        if (rotationCenter != null)
        {
            rotationCenter.position = rotationCenterPosition;
        }

        Debug.Log($"初始相机位置设置完成 - 位置: {initialPosition}, 俯视角: {initialCameraAngle}度");
    }

    #endregion
}