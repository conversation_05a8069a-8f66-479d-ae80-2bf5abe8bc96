using UnityEngine;

namespace BlastingDesign.Core.Camera
{
    /// <summary>
    /// 相机高度自适应系统 - 公共接口和方法
    /// </summary>
    public partial class CameraHeightAdaptationSystem
    {
        #region 公共方法

        /// <summary>
        /// 强制更新距离保持系统（用于缩放和QE键操作）
        /// 此方法会暂停自动调整一段时间，避免与手动操作冲突
        /// </summary>
        public void ForceUpdateDistance()
        {
            if (!isInitialized) return;

            // 暂停自动调整，避免与手动操作冲突
            SuspendSystem();

            Vector3 hitPoint = GetCurrentHitPoint();
            if (hitPoint != Vector3.zero)
            {
                float newDistance = Vector3.Distance(targetCamera.transform.position, hitPoint);
                newDistance = Mathf.Clamp(newDistance, minKeepDistance, maxKeepDistance);

                // 更新存储的距离
                storedDistance = newDistance;
                lastHitPoint = hitPoint;
                hasValidDistance = true;

                if (showDebugInfo)
                {
                    Debug.Log($"[CameraHeightAdaptationSystem] 强制更新距离: 新目标距离={storedDistance:F2}m, 交点={hitPoint}, 系统暂停{suspensionDuration}s");
                }
            }
        }

        /// <summary>
        /// 设置目标距离
        /// </summary>
        /// <param name="distance">目标距离</param>
        public void SetTargetDistance(float distance)
        {
            storedDistance = Mathf.Clamp(distance, minKeepDistance, maxKeepDistance);
            hasValidDistance = true;

            if (showDebugInfo)
            {
                Debug.Log($"[CameraHeightAdaptationSystem] 设置目标距离: {storedDistance:F2}m");
            }
        }

        /// <summary>
        /// 获取当前存储的距离
        /// </summary>
        /// <returns>当前距离</returns>
        public float GetStoredDistance()
        {
            return storedDistance;
        }

        /// <summary>
        /// 启用或禁用高度自适应
        /// </summary>
        /// <param name="enable">是否启用</param>
        public void SetEnabled(bool enable)
        {
            enableHeightAdaptation = enable;

            if (enable && isInitialized)
            {
                InitializeDistanceKeeping();
            }
        }

        /// <summary>
        /// 设置更新频率
        /// </summary>
        /// <param name="frequency">更新频率（Hz）</param>
        public void SetUpdateFrequency(float frequency)
        {
            updateFrequency = Mathf.Max(1f, frequency);
            updateInterval = 1f / updateFrequency;
        }

        /// <summary>
        /// 设置目标相机
        /// </summary>
        /// <param name="camera">目标相机</param>
        public void SetTargetCamera(UnityEngine.Camera camera)
        {
            targetCamera = camera;
            if (targetCamera != null)
            {
                lastCameraPosition = targetCamera.transform.position;
                Initialize();
            }
        }

        /// <summary>
        /// 获取当前是否有有效的距离数据
        /// </summary>
        /// <returns>是否有有效距离</returns>
        public bool HasValidDistance()
        {
            return hasValidDistance;
        }

        /// <summary>
        /// 重置距离保持系统
        /// </summary>
        public void ResetDistanceKeeping()
        {
            hasValidDistance = false;
            storedDistance = defaultDistance;
            lastHitPoint = Vector3.zero;

            if (showDebugInfo)
            {
                Debug.Log("[CameraHeightAdaptationSystem] 距离保持系统已重置");
            }
        }

        /// <summary>
        /// 检查系统状态并诊断问题
        /// </summary>
        public void DiagnoseSystem()
        {
            Debug.Log("=== CameraHeightAdaptationSystem 诊断 ===");
            Debug.Log($"启用状态: {enableHeightAdaptation}");
            Debug.Log($"初始化状态: {isInitialized}");
            Debug.Log($"暂停状态: {isSuspended}");
            if (isSuspended)
            {
                Debug.Log($"暂停剩余时间: {suspensionEndTime - Time.time:F2}s");
            }
            Debug.Log($"目标相机: {(targetCamera != null ? targetCamera.name : "null")}");
            Debug.Log($"有效距离: {hasValidDistance}");
            Debug.Log($"存储距离: {storedDistance:F2}m");
            Debug.Log($"更新频率: {updateFrequency}Hz");
            Debug.Log($"平滑度: {distanceSmoothness}");
            Debug.Log($"强制模式: {forceMode}");

            if (targetCamera != null)
            {
                Vector3 hitPoint = GetCurrentHitPoint();
                if (hitPoint != Vector3.zero)
                {
                    float currentDistance = Vector3.Distance(targetCamera.transform.position, hitPoint);
                    Debug.Log($"当前距离: {currentDistance:F2}m");
                    Debug.Log($"距离差: {Mathf.Abs(currentDistance - storedDistance):F2}m");
                    Debug.Log($"检测点: {hitPoint}");
                }
                else
                {
                    Debug.LogWarning("无法检测到地面交点！");
                }
            }
            Debug.Log("=====================================");
        }

        /// <summary>
        /// 暂停系统自动调整
        /// </summary>
        public void SuspendSystem()
        {
            isSuspended = true;
            suspensionEndTime = Time.time + suspensionDuration;

            if (showDebugInfo)
            {
                Debug.Log($"[CameraHeightAdaptationSystem] 系统暂停 {suspensionDuration}s，直到 {suspensionEndTime}");
            }
        }

        /// <summary>
        /// 暂停系统指定时间
        /// </summary>
        /// <param name="duration">暂停时长（秒）</param>
        public void SuspendSystem(float duration)
        {
            isSuspended = true;
            suspensionEndTime = Time.time + duration;

            if (showDebugInfo)
            {
                Debug.Log($"[CameraHeightAdaptationSystem] 系统暂停 {duration}s，直到 {suspensionEndTime}");
            }
        }

        /// <summary>
        /// 立即恢复系统自动调整
        /// </summary>
        public void ResumeSystem()
        {
            isSuspended = false;
            suspensionEndTime = 0f;

            if (showDebugInfo)
            {
                Debug.Log($"[CameraHeightAdaptationSystem] 系统立即恢复");
            }
        }

        /// <summary>
        /// 检查系统是否被暂停
        /// </summary>
        /// <returns>是否暂停</returns>
        public bool IsSuspended()
        {
            return isSuspended && Time.time < suspensionEndTime;
        }

        #endregion
    }
}