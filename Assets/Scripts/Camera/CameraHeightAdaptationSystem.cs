using UnityEngine;

namespace BlastingDesign.Core.Camera
{
    /// <summary>
    /// 相机高度自适应系统
    /// 独立于输入系统，持续维护相机与地形的适当距离
    /// 
    /// 此类已拆分为多个partial文件：
    /// - CameraHeightAdaptationSystem.Core.cs: 核心配置和生命周期
    /// - CameraHeightAdaptationSystem.RaycastUtils.cs: 射线检测功能
    /// - CameraHeightAdaptationSystem.PositionCalculation.cs: 位置计算逻辑
    /// - CameraHeightAdaptationSystem.PublicAPI.cs: 公共接口方法
    /// - CameraHeightAdaptationSystem.EditorSupport.cs: Unity Editor支持
    /// </summary>
    public partial class CameraHeightAdaptationSystem : MonoBehaviour
    {
        // 所有实现已移动到对应的partial类文件中
        // 请查看 CameraHeightAdaptationSystem 文件夹中的各个partial文件
    }
}