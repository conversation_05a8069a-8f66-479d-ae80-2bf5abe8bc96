.m-0 {
  margin: 0px;
}

.mx-0 {
  margin-left: 0px;
  margin-right: 0px;
}

.my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}

.mt-0 {
  margin-top: 0px;
}

.mr-0 {
  margin-right: 0px;
}

.mb-0 {
  margin-bottom: 0px;
}

.ml-0 {
  margin-left: 0px;
}

.m-px {
  margin: 1px;
}

.mx-px {
  margin-left: 1px;
  margin-right: 1px;
}

.my-px {
  margin-top: 1px;
  margin-bottom: 1px;
}

.mt-px {
  margin-top: 1px;
}

.mr-px {
  margin-right: 1px;
}

.mb-px {
  margin-bottom: 1px;
}

.ml-px {
  margin-left: 1px;
}

.m-1 {
  margin: 4px;
}

.mx-1 {
  margin-left: 4px;
  margin-right: 4px;
}

.my-1 {
  margin-top: 4px;
  margin-bottom: 4px;
}

.mt-1 {
  margin-top: 4px;
}

.mr-1 {
  margin-right: 4px;
}

.mb-1 {
  margin-bottom: 4px;
}

.ml-1 {
  margin-left: 4px;
}

.m-2 {
  margin: 8px;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.my-2 {
  margin-top: 8px;
  margin-bottom: 8px;
}

.mt-2 {
  margin-top: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.mb-2 {
  margin-bottom: 8px;
}

.ml-2 {
  margin-left: 8px;
}

.m-3 {
  margin: 12px;
}

.mx-3 {
  margin-left: 12px;
  margin-right: 12px;
}

.my-3 {
  margin-top: 12px;
  margin-bottom: 12px;
}

.mt-3 {
  margin-top: 12px;
}

.mr-3 {
  margin-right: 12px;
}

.mb-3 {
  margin-bottom: 12px;
}

.ml-3 {
  margin-left: 12px;
}

.m-4 {
  margin: 16px;
}

.mx-4 {
  margin-left: 16px;
  margin-right: 16px;
}

.my-4 {
  margin-top: 16px;
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.mr-4 {
  margin-right: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.ml-4 {
  margin-left: 16px;
}

.m-5 {
  margin: 20px;
}

.mx-5 {
  margin-left: 20px;
  margin-right: 20px;
}

.my-5 {
  margin-top: 20px;
  margin-bottom: 20px;
}

.mt-5 {
  margin-top: 20px;
}

.mr-5 {
  margin-right: 20px;
}

.mb-5 {
  margin-bottom: 20px;
}

.ml-5 {
  margin-left: 20px;
}

.m-6 {
  margin: 24px;
}

.mx-6 {
  margin-left: 24px;
  margin-right: 24px;
}

.my-6 {
  margin-top: 24px;
  margin-bottom: 24px;
}

.mt-6 {
  margin-top: 24px;
}

.mr-6 {
  margin-right: 24px;
}

.mb-6 {
  margin-bottom: 24px;
}

.ml-6 {
  margin-left: 24px;
}

.m-7 {
  margin: 28px;
}

.mx-7 {
  margin-left: 28px;
  margin-right: 28px;
}

.my-7 {
  margin-top: 28px;
  margin-bottom: 28px;
}

.mt-7 {
  margin-top: 28px;
}

.mr-7 {
  margin-right: 28px;
}

.mb-7 {
  margin-bottom: 28px;
}

.ml-7 {
  margin-left: 28px;
}

.m-8 {
  margin: 32px;
}

.mx-8 {
  margin-left: 32px;
  margin-right: 32px;
}

.my-8 {
  margin-top: 32px;
  margin-bottom: 32px;
}

.mt-8 {
  margin-top: 32px;
}

.mr-8 {
  margin-right: 32px;
}

.mb-8 {
  margin-bottom: 32px;
}

.ml-8 {
  margin-left: 32px;
}

.m-9 {
  margin: 36px;
}

.mx-9 {
  margin-left: 36px;
  margin-right: 36px;
}

.my-9 {
  margin-top: 36px;
  margin-bottom: 36px;
}

.mt-9 {
  margin-top: 36px;
}

.mr-9 {
  margin-right: 36px;
}

.mb-9 {
  margin-bottom: 36px;
}

.ml-9 {
  margin-left: 36px;
}

.m-10 {
  margin: 40px;
}

.mx-10 {
  margin-left: 40px;
  margin-right: 40px;
}

.my-10 {
  margin-top: 40px;
  margin-bottom: 40px;
}

.mt-10 {
  margin-top: 40px;
}

.mr-10 {
  margin-right: 40px;
}

.mb-10 {
  margin-bottom: 40px;
}

.ml-10 {
  margin-left: 40px;
}

.m-11 {
  margin: 44px;
}

.mx-11 {
  margin-left: 44px;
  margin-right: 44px;
}

.my-11 {
  margin-top: 44px;
  margin-bottom: 44px;
}

.mt-11 {
  margin-top: 44px;
}

.mr-11 {
  margin-right: 44px;
}

.mb-11 {
  margin-bottom: 44px;
}

.ml-11 {
  margin-left: 44px;
}

.m-12 {
  margin: 48px;
}

.mx-12 {
  margin-left: 48px;
  margin-right: 48px;
}

.my-12 {
  margin-top: 48px;
  margin-bottom: 48px;
}

.mt-12 {
  margin-top: 48px;
}

.mr-12 {
  margin-right: 48px;
}

.mb-12 {
  margin-bottom: 48px;
}

.ml-12 {
  margin-left: 48px;
}

.m-14 {
  margin: 56px;
}

.mx-14 {
  margin-left: 56px;
  margin-right: 56px;
}

.my-14 {
  margin-top: 56px;
  margin-bottom: 56px;
}

.mt-14 {
  margin-top: 56px;
}

.mr-14 {
  margin-right: 56px;
}

.mb-14 {
  margin-bottom: 56px;
}

.ml-14 {
  margin-left: 56px;
}

.m-16 {
  margin: 64px;
}

.mx-16 {
  margin-left: 64px;
  margin-right: 64px;
}

.my-16 {
  margin-top: 64px;
  margin-bottom: 64px;
}

.mt-16 {
  margin-top: 64px;
}

.mr-16 {
  margin-right: 64px;
}

.mb-16 {
  margin-bottom: 64px;
}

.ml-16 {
  margin-left: 64px;
}

.m-20 {
  margin: 80px;
}

.mx-20 {
  margin-left: 80px;
  margin-right: 80px;
}

.my-20 {
  margin-top: 80px;
  margin-bottom: 80px;
}

.mt-20 {
  margin-top: 80px;
}

.mr-20 {
  margin-right: 80px;
}

.mb-20 {
  margin-bottom: 80px;
}

.ml-20 {
  margin-left: 80px;
}

.m-24 {
  margin: 96px;
}

.mx-24 {
  margin-left: 96px;
  margin-right: 96px;
}

.my-24 {
  margin-top: 96px;
  margin-bottom: 96px;
}

.mt-24 {
  margin-top: 96px;
}

.mr-24 {
  margin-right: 96px;
}

.mb-24 {
  margin-bottom: 96px;
}

.ml-24 {
  margin-left: 96px;
}

.m-28 {
  margin: 112px;
}

.mx-28 {
  margin-left: 112px;
  margin-right: 112px;
}

.my-28 {
  margin-top: 112px;
  margin-bottom: 112px;
}

.mt-28 {
  margin-top: 112px;
}

.mr-28 {
  margin-right: 112px;
}

.mb-28 {
  margin-bottom: 112px;
}

.ml-28 {
  margin-left: 112px;
}

.m-32 {
  margin: 128px;
}

.mx-32 {
  margin-left: 128px;
  margin-right: 128px;
}

.my-32 {
  margin-top: 128px;
  margin-bottom: 128px;
}

.mt-32 {
  margin-top: 128px;
}

.mr-32 {
  margin-right: 128px;
}

.mb-32 {
  margin-bottom: 128px;
}

.ml-32 {
  margin-left: 128px;
}

.m-36 {
  margin: 144px;
}

.mx-36 {
  margin-left: 144px;
  margin-right: 144px;
}

.my-36 {
  margin-top: 144px;
  margin-bottom: 144px;
}

.mt-36 {
  margin-top: 144px;
}

.mr-36 {
  margin-right: 144px;
}

.mb-36 {
  margin-bottom: 144px;
}

.ml-36 {
  margin-left: 144px;
}

.m-40 {
  margin: 160px;
}

.mx-40 {
  margin-left: 160px;
  margin-right: 160px;
}

.my-40 {
  margin-top: 160px;
  margin-bottom: 160px;
}

.mt-40 {
  margin-top: 160px;
}

.mr-40 {
  margin-right: 160px;
}

.mb-40 {
  margin-bottom: 160px;
}

.ml-40 {
  margin-left: 160px;
}

.m-44 {
  margin: 176px;
}

.mx-44 {
  margin-left: 176px;
  margin-right: 176px;
}

.my-44 {
  margin-top: 176px;
  margin-bottom: 176px;
}

.mt-44 {
  margin-top: 176px;
}

.mr-44 {
  margin-right: 176px;
}

.mb-44 {
  margin-bottom: 176px;
}

.ml-44 {
  margin-left: 176px;
}

.m-48 {
  margin: 192px;
}

.mx-48 {
  margin-left: 192px;
  margin-right: 192px;
}

.my-48 {
  margin-top: 192px;
  margin-bottom: 192px;
}

.mt-48 {
  margin-top: 192px;
}

.mr-48 {
  margin-right: 192px;
}

.mb-48 {
  margin-bottom: 192px;
}

.ml-48 {
  margin-left: 192px;
}

.m-52 {
  margin: 208px;
}

.mx-52 {
  margin-left: 208px;
  margin-right: 208px;
}

.my-52 {
  margin-top: 208px;
  margin-bottom: 208px;
}

.mt-52 {
  margin-top: 208px;
}

.mr-52 {
  margin-right: 208px;
}

.mb-52 {
  margin-bottom: 208px;
}

.ml-52 {
  margin-left: 208px;
}

.m-56 {
  margin: 224px;
}

.mx-56 {
  margin-left: 224px;
  margin-right: 224px;
}

.my-56 {
  margin-top: 224px;
  margin-bottom: 224px;
}

.mt-56 {
  margin-top: 224px;
}

.mr-56 {
  margin-right: 224px;
}

.mb-56 {
  margin-bottom: 224px;
}

.ml-56 {
  margin-left: 224px;
}

.m-60 {
  margin: 240px;
}

.mx-60 {
  margin-left: 240px;
  margin-right: 240px;
}

.my-60 {
  margin-top: 240px;
  margin-bottom: 240px;
}

.mt-60 {
  margin-top: 240px;
}

.mr-60 {
  margin-right: 240px;
}

.mb-60 {
  margin-bottom: 240px;
}

.ml-60 {
  margin-left: 240px;
}

.m-64 {
  margin: 256px;
}

.mx-64 {
  margin-left: 256px;
  margin-right: 256px;
}

.my-64 {
  margin-top: 256px;
  margin-bottom: 256px;
}

.mt-64 {
  margin-top: 256px;
}

.mr-64 {
  margin-right: 256px;
}

.mb-64 {
  margin-bottom: 256px;
}

.ml-64 {
  margin-left: 256px;
}

.m-72 {
  margin: 288px;
}

.mx-72 {
  margin-left: 288px;
  margin-right: 288px;
}

.my-72 {
  margin-top: 288px;
  margin-bottom: 288px;
}

.mt-72 {
  margin-top: 288px;
}

.mr-72 {
  margin-right: 288px;
}

.mb-72 {
  margin-bottom: 288px;
}

.ml-72 {
  margin-left: 288px;
}

.m-80 {
  margin: 320px;
}

.mx-80 {
  margin-left: 320px;
  margin-right: 320px;
}

.my-80 {
  margin-top: 320px;
  margin-bottom: 320px;
}

.mt-80 {
  margin-top: 320px;
}

.mr-80 {
  margin-right: 320px;
}

.mb-80 {
  margin-bottom: 320px;
}

.ml-80 {
  margin-left: 320px;
}

.m-96 {
  margin: 384px;
}

.mx-96 {
  margin-left: 384px;
  margin-right: 384px;
}

.my-96 {
  margin-top: 384px;
  margin-bottom: 384px;
}

.mt-96 {
  margin-top: 384px;
}

.mr-96 {
  margin-right: 384px;
}

.mb-96 {
  margin-bottom: 384px;
}

.ml-96 {
  margin-left: 384px;
}

.p-0 {
  padding: 0px;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.pt-0 {
  padding-top: 0px;
}

.pr-0 {
  padding-right: 0px;
}

.pb-0 {
  padding-bottom: 0px;
}

.pl-0 {
  padding-left: 0px;
}

.p-px {
  padding: 1px;
}

.px-px {
  padding-left: 1px;
  padding-right: 1px;
}

.py-px {
  padding-top: 1px;
  padding-bottom: 1px;
}

.pt-px {
  padding-top: 1px;
}

.pr-px {
  padding-right: 1px;
}

.pb-px {
  padding-bottom: 1px;
}

.pl-px {
  padding-left: 1px;
}

.p-1 {
  padding: 4px;
}

.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}

.py-1 {
  padding-top: 4px;
  padding-bottom: 4px;
}

.pt-1 {
  padding-top: 4px;
}

.pr-1 {
  padding-right: 4px;
}

.pb-1 {
  padding-bottom: 4px;
}

.pl-1 {
  padding-left: 4px;
}

.p-2 {
  padding: 8px;
}

.px-2 {
  padding-left: 8px;
  padding-right: 8px;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.pt-2 {
  padding-top: 8px;
}

.pr-2 {
  padding-right: 8px;
}

.pb-2 {
  padding-bottom: 8px;
}

.pl-2 {
  padding-left: 8px;
}

.p-3 {
  padding: 12px;
}

.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}

.py-3 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.pt-3 {
  padding-top: 12px;
}

.pr-3 {
  padding-right: 12px;
}

.pb-3 {
  padding-bottom: 12px;
}

.pl-3 {
  padding-left: 12px;
}

.p-4 {
  padding: 16px;
}

.px-4 {
  padding-left: 16px;
  padding-right: 16px;
}

.py-4 {
  padding-top: 16px;
  padding-bottom: 16px;
}

.pt-4 {
  padding-top: 16px;
}

.pr-4 {
  padding-right: 16px;
}

.pb-4 {
  padding-bottom: 16px;
}

.pl-4 {
  padding-left: 16px;
}

.p-5 {
  padding: 20px;
}

.px-5 {
  padding-left: 20px;
  padding-right: 20px;
}

.py-5 {
  padding-top: 20px;
  padding-bottom: 20px;
}

.pt-5 {
  padding-top: 20px;
}

.pr-5 {
  padding-right: 20px;
}

.pb-5 {
  padding-bottom: 20px;
}

.pl-5 {
  padding-left: 20px;
}

.p-6 {
  padding: 24px;
}

.px-6 {
  padding-left: 24px;
  padding-right: 24px;
}

.py-6 {
  padding-top: 24px;
  padding-bottom: 24px;
}

.pt-6 {
  padding-top: 24px;
}

.pr-6 {
  padding-right: 24px;
}

.pb-6 {
  padding-bottom: 24px;
}

.pl-6 {
  padding-left: 24px;
}

.p-7 {
  padding: 28px;
}

.px-7 {
  padding-left: 28px;
  padding-right: 28px;
}

.py-7 {
  padding-top: 28px;
  padding-bottom: 28px;
}

.pt-7 {
  padding-top: 28px;
}

.pr-7 {
  padding-right: 28px;
}

.pb-7 {
  padding-bottom: 28px;
}

.pl-7 {
  padding-left: 28px;
}

.p-8 {
  padding: 32px;
}

.px-8 {
  padding-left: 32px;
  padding-right: 32px;
}

.py-8 {
  padding-top: 32px;
  padding-bottom: 32px;
}

.pt-8 {
  padding-top: 32px;
}

.pr-8 {
  padding-right: 32px;
}

.pb-8 {
  padding-bottom: 32px;
}

.pl-8 {
  padding-left: 32px;
}

.p-9 {
  padding: 36px;
}

.px-9 {
  padding-left: 36px;
  padding-right: 36px;
}

.py-9 {
  padding-top: 36px;
  padding-bottom: 36px;
}

.pt-9 {
  padding-top: 36px;
}

.pr-9 {
  padding-right: 36px;
}

.pb-9 {
  padding-bottom: 36px;
}

.pl-9 {
  padding-left: 36px;
}

.p-10 {
  padding: 40px;
}

.px-10 {
  padding-left: 40px;
  padding-right: 40px;
}

.py-10 {
  padding-top: 40px;
  padding-bottom: 40px;
}

.pt-10 {
  padding-top: 40px;
}

.pr-10 {
  padding-right: 40px;
}

.pb-10 {
  padding-bottom: 40px;
}

.pl-10 {
  padding-left: 40px;
}

.p-11 {
  padding: 44px;
}

.px-11 {
  padding-left: 44px;
  padding-right: 44px;
}

.py-11 {
  padding-top: 44px;
  padding-bottom: 44px;
}

.pt-11 {
  padding-top: 44px;
}

.pr-11 {
  padding-right: 44px;
}

.pb-11 {
  padding-bottom: 44px;
}

.pl-11 {
  padding-left: 44px;
}

.p-12 {
  padding: 48px;
}

.px-12 {
  padding-left: 48px;
  padding-right: 48px;
}

.py-12 {
  padding-top: 48px;
  padding-bottom: 48px;
}

.pt-12 {
  padding-top: 48px;
}

.pr-12 {
  padding-right: 48px;
}

.pb-12 {
  padding-bottom: 48px;
}

.pl-12 {
  padding-left: 48px;
}

.p-14 {
  padding: 56px;
}

.px-14 {
  padding-left: 56px;
  padding-right: 56px;
}

.py-14 {
  padding-top: 56px;
  padding-bottom: 56px;
}

.pt-14 {
  padding-top: 56px;
}

.pr-14 {
  padding-right: 56px;
}

.pb-14 {
  padding-bottom: 56px;
}

.pl-14 {
  padding-left: 56px;
}

.p-16 {
  padding: 64px;
}

.px-16 {
  padding-left: 64px;
  padding-right: 64px;
}

.py-16 {
  padding-top: 64px;
  padding-bottom: 64px;
}

.pt-16 {
  padding-top: 64px;
}

.pr-16 {
  padding-right: 64px;
}

.pb-16 {
  padding-bottom: 64px;
}

.pl-16 {
  padding-left: 64px;
}

.p-20 {
  padding: 80px;
}

.px-20 {
  padding-left: 80px;
  padding-right: 80px;
}

.py-20 {
  padding-top: 80px;
  padding-bottom: 80px;
}

.pt-20 {
  padding-top: 80px;
}

.pr-20 {
  padding-right: 80px;
}

.pb-20 {
  padding-bottom: 80px;
}

.pl-20 {
  padding-left: 80px;
}

.p-24 {
  padding: 96px;
}

.px-24 {
  padding-left: 96px;
  padding-right: 96px;
}

.py-24 {
  padding-top: 96px;
  padding-bottom: 96px;
}

.pt-24 {
  padding-top: 96px;
}

.pr-24 {
  padding-right: 96px;
}

.pb-24 {
  padding-bottom: 96px;
}

.pl-24 {
  padding-left: 96px;
}

.p-28 {
  padding: 112px;
}

.px-28 {
  padding-left: 112px;
  padding-right: 112px;
}

.py-28 {
  padding-top: 112px;
  padding-bottom: 112px;
}

.pt-28 {
  padding-top: 112px;
}

.pr-28 {
  padding-right: 112px;
}

.pb-28 {
  padding-bottom: 112px;
}

.pl-28 {
  padding-left: 112px;
}

.p-32 {
  padding: 128px;
}

.px-32 {
  padding-left: 128px;
  padding-right: 128px;
}

.py-32 {
  padding-top: 128px;
  padding-bottom: 128px;
}

.pt-32 {
  padding-top: 128px;
}

.pr-32 {
  padding-right: 128px;
}

.pb-32 {
  padding-bottom: 128px;
}

.pl-32 {
  padding-left: 128px;
}

.p-36 {
  padding: 144px;
}

.px-36 {
  padding-left: 144px;
  padding-right: 144px;
}

.py-36 {
  padding-top: 144px;
  padding-bottom: 144px;
}

.pt-36 {
  padding-top: 144px;
}

.pr-36 {
  padding-right: 144px;
}

.pb-36 {
  padding-bottom: 144px;
}

.pl-36 {
  padding-left: 144px;
}

.p-40 {
  padding: 160px;
}

.px-40 {
  padding-left: 160px;
  padding-right: 160px;
}

.py-40 {
  padding-top: 160px;
  padding-bottom: 160px;
}

.pt-40 {
  padding-top: 160px;
}

.pr-40 {
  padding-right: 160px;
}

.pb-40 {
  padding-bottom: 160px;
}

.pl-40 {
  padding-left: 160px;
}

.p-44 {
  padding: 176px;
}

.px-44 {
  padding-left: 176px;
  padding-right: 176px;
}

.py-44 {
  padding-top: 176px;
  padding-bottom: 176px;
}

.pt-44 {
  padding-top: 176px;
}

.pr-44 {
  padding-right: 176px;
}

.pb-44 {
  padding-bottom: 176px;
}

.pl-44 {
  padding-left: 176px;
}

.p-48 {
  padding: 192px;
}

.px-48 {
  padding-left: 192px;
  padding-right: 192px;
}

.py-48 {
  padding-top: 192px;
  padding-bottom: 192px;
}

.pt-48 {
  padding-top: 192px;
}

.pr-48 {
  padding-right: 192px;
}

.pb-48 {
  padding-bottom: 192px;
}

.pl-48 {
  padding-left: 192px;
}

.p-52 {
  padding: 208px;
}

.px-52 {
  padding-left: 208px;
  padding-right: 208px;
}

.py-52 {
  padding-top: 208px;
  padding-bottom: 208px;
}

.pt-52 {
  padding-top: 208px;
}

.pr-52 {
  padding-right: 208px;
}

.pb-52 {
  padding-bottom: 208px;
}

.pl-52 {
  padding-left: 208px;
}

.p-56 {
  padding: 224px;
}

.px-56 {
  padding-left: 224px;
  padding-right: 224px;
}

.py-56 {
  padding-top: 224px;
  padding-bottom: 224px;
}

.pt-56 {
  padding-top: 224px;
}

.pr-56 {
  padding-right: 224px;
}

.pb-56 {
  padding-bottom: 224px;
}

.pl-56 {
  padding-left: 224px;
}

.p-60 {
  padding: 240px;
}

.px-60 {
  padding-left: 240px;
  padding-right: 240px;
}

.py-60 {
  padding-top: 240px;
  padding-bottom: 240px;
}

.pt-60 {
  padding-top: 240px;
}

.pr-60 {
  padding-right: 240px;
}

.pb-60 {
  padding-bottom: 240px;
}

.pl-60 {
  padding-left: 240px;
}

.p-64 {
  padding: 256px;
}

.px-64 {
  padding-left: 256px;
  padding-right: 256px;
}

.py-64 {
  padding-top: 256px;
  padding-bottom: 256px;
}

.pt-64 {
  padding-top: 256px;
}

.pr-64 {
  padding-right: 256px;
}

.pb-64 {
  padding-bottom: 256px;
}

.pl-64 {
  padding-left: 256px;
}

.p-72 {
  padding: 288px;
}

.px-72 {
  padding-left: 288px;
  padding-right: 288px;
}

.py-72 {
  padding-top: 288px;
  padding-bottom: 288px;
}

.pt-72 {
  padding-top: 288px;
}

.pr-72 {
  padding-right: 288px;
}

.pb-72 {
  padding-bottom: 288px;
}

.pl-72 {
  padding-left: 288px;
}

.p-80 {
  padding: 320px;
}

.px-80 {
  padding-left: 320px;
  padding-right: 320px;
}

.py-80 {
  padding-top: 320px;
  padding-bottom: 320px;
}

.pt-80 {
  padding-top: 320px;
}

.pr-80 {
  padding-right: 320px;
}

.pb-80 {
  padding-bottom: 320px;
}

.pl-80 {
  padding-left: 320px;
}

.p-96 {
  padding: 384px;
}

.px-96 {
  padding-left: 384px;
  padding-right: 384px;
}

.py-96 {
  padding-top: 384px;
  padding-bottom: 384px;
}

.pt-96 {
  padding-top: 384px;
}

.pr-96 {
  padding-right: 384px;
}

.pb-96 {
  padding-bottom: 384px;
}

.pl-96 {
  padding-left: 384px;
}

.w-0 {
  width: 0px;
}

.w-px {
  width: 1px;
}

.w-1 {
  width: 4px;
}

.w-2 {
  width: 8px;
}

.w-3 {
  width: 12px;
}

.w-4 {
  width: 16px;
}

.w-5 {
  width: 20px;
}

.w-6 {
  width: 24px;
}

.w-7 {
  width: 28px;
}

.w-8 {
  width: 32px;
}

.w-9 {
  width: 36px;
}

.w-10 {
  width: 40px;
}

.w-11 {
  width: 44px;
}

.w-12 {
  width: 48px;
}

.w-14 {
  width: 56px;
}

.w-16 {
  width: 64px;
}

.w-20 {
  width: 80px;
}

.w-24 {
  width: 96px;
}

.w-28 {
  width: 112px;
}

.w-32 {
  width: 128px;
}

.w-36 {
  width: 144px;
}

.w-40 {
  width: 160px;
}

.w-44 {
  width: 176px;
}

.w-48 {
  width: 192px;
}

.w-52 {
  width: 208px;
}

.w-56 {
  width: 224px;
}

.w-60 {
  width: 240px;
}

.w-64 {
  width: 256px;
}

.w-72 {
  width: 288px;
}

.w-80 {
  width: 320px;
}

.w-96 {
  width: 384px;
}

.w-auto {
  width: auto;
}

.w-one-half {
  width: 50%;
}

.w-one-third {
  width: 33.33333%;
}

.w-two-thirds {
  width: 66.66667%;
}

.w-one-quarter {
  width: 25%;
}

.w-three-quarters {
  width: 75%;
}

.w-one-fifth {
  width: 20%;
}

.w-two-fifths {
  width: 40%;
}

.w-three-fifths {
  width: 60%;
}

.w-four-fifths {
  width: 80%;
}

.w-one-sixth {
  width: 16.66667%;
}

.w-five-sixths {
  width: 83.33333%;
}

.w-one-twelfth {
  width: 8.33333%;
}

.w-five-twelfths {
  width: 41.66667%;
}

.w-seven-twelfths {
  width: 58.33333%;
}

.w-ten-twelfths {
  width: 83.33333%;
}

.w-eleven-twelfths {
  width: 91.66667%;
}

.w-full {
  width: 100%;
}

.h-0 {
  height: 0px;
}

.h-px {
  height: 1px;
}

.h-1 {
  height: 4px;
}

.h-2 {
  height: 8px;
}

.h-3 {
  height: 12px;
}

.h-4 {
  height: 16px;
}

.h-5 {
  height: 20px;
}

.h-6 {
  height: 24px;
}

.h-7 {
  height: 28px;
}

.h-8 {
  height: 32px;
}

.h-9 {
  height: 36px;
}

.h-10 {
  height: 40px;
}

.h-11 {
  height: 44px;
}

.h-12 {
  height: 48px;
}

.h-14 {
  height: 56px;
}

.h-16 {
  height: 64px;
}

.h-20 {
  height: 80px;
}

.h-24 {
  height: 96px;
}

.h-28 {
  height: 112px;
}

.h-32 {
  height: 128px;
}

.h-36 {
  height: 144px;
}

.h-40 {
  height: 160px;
}

.h-44 {
  height: 176px;
}

.h-48 {
  height: 192px;
}

.h-52 {
  height: 208px;
}

.h-56 {
  height: 224px;
}

.h-60 {
  height: 240px;
}

.h-64 {
  height: 256px;
}

.h-72 {
  height: 288px;
}

.h-80 {
  height: 320px;
}

.h-96 {
  height: 384px;
}

.h-auto {
  height: auto;
}

.h-one-half {
  height: 50%;
}

.h-one-third {
  height: 33.33333%;
}

.h-two-thirds {
  height: 66.66667%;
}

.h-one-quarter {
  height: 25%;
}

.h-three-quarters {
  height: 75%;
}

.h-one-fifth {
  height: 20%;
}

.h-two-fifths {
  height: 40%;
}

.h-three-fifths {
  height: 60%;
}

.h-four-fifths {
  height: 80%;
}

.h-one-sixth {
  height: 16.66667%;
}

.h-five-sixths {
  height: 83.33333%;
}

.h-full {
  height: 100%;
}

.min-w-0 {
  min-width: 0px;
}

.min-w-full {
  min-width: 100%;
}

.max-w-0 {
  max-width: 0px;
}

.max-w-none {
  max-width: none;
}

.max-w-xs {
  max-width: 320px;
}

.max-w-sm {
  max-width: 384px;
}

.max-w-md {
  max-width: 448px;
}

.max-w-lg {
  max-width: 512px;
}

.max-w-xl {
  max-width: 576px;
}

.max-w-2xl {
  max-width: 672px;
}

.max-w-3xl {
  max-width: 768px;
}

.max-w-4xl {
  max-width: 896px;
}

.max-w-5xl {
  max-width: 1024px;
}

.max-w-6xl {
  max-width: 1152px;
}

.max-w-7xl {
  max-width: 1280px;
}

.max-w-full {
  max-width: 100%;
}

.max-w-screen-sm {
  max-width: 640px;
}

.max-w-screen-md {
  max-width: 768px;
}

.max-w-screen-lg {
  max-width: 1024px;
}

.max-w-screen-xl {
  max-width: 1280px;
}

.max-w-screen-2xl {
  max-width: 1536px;
}

.min-h-0 {
  min-height: 0px;
}

.min-h-full {
  min-height: 100%;
}

.max-h-0 {
  max-height: 0px;
}

.max-h-px {
  max-height: 1px;
}

.max-h-1 {
  max-height: 4px;
}

.max-h-2 {
  max-height: 8px;
}

.max-h-3 {
  max-height: 12px;
}

.max-h-4 {
  max-height: 16px;
}

.max-h-5 {
  max-height: 20px;
}

.max-h-6 {
  max-height: 24px;
}

.max-h-7 {
  max-height: 28px;
}

.max-h-8 {
  max-height: 32px;
}

.max-h-9 {
  max-height: 36px;
}

.max-h-10 {
  max-height: 40px;
}

.max-h-11 {
  max-height: 44px;
}

.max-h-12 {
  max-height: 48px;
}

.max-h-14 {
  max-height: 56px;
}

.max-h-16 {
  max-height: 64px;
}

.max-h-20 {
  max-height: 80px;
}

.max-h-24 {
  max-height: 96px;
}

.max-h-28 {
  max-height: 112px;
}

.max-h-32 {
  max-height: 128px;
}

.max-h-36 {
  max-height: 144px;
}

.max-h-40 {
  max-height: 160px;
}

.max-h-44 {
  max-height: 176px;
}

.max-h-48 {
  max-height: 192px;
}

.max-h-52 {
  max-height: 208px;
}

.max-h-56 {
  max-height: 224px;
}

.max-h-60 {
  max-height: 240px;
}

.max-h-64 {
  max-height: 256px;
}

.max-h-72 {
  max-height: 288px;
}

.max-h-80 {
  max-height: 320px;
}

.max-h-96 {
  max-height: 384px;
}

.max-h-none {
  max-height: none;
}

.max-h-full {
  max-height: 100%;
}