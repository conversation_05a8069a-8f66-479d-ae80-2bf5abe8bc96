.text-slate-50 {
  color: var(--slate-50);
}

.text-slate-100 {
  color: var(--slate-100);
}

.text-slate-200 {
  color: var(--slate-200);
}

.text-slate-300 {
  color: var(--slate-300);
}

.text-slate-400 {
  color: var(--slate-400);
}

.text-slate-500 {
  color: var(--slate-500);
}

.text-slate-600 {
  color: var(--slate-600);
}

.text-slate-700 {
  color: var(--slate-700);
}

.text-slate-800 {
  color: var(--slate-800);
}

.text-slate-900 {
  color: var(--slate-900);
}

.text-slate-950 {
  color: var(--slate-950);
}

.text-gray-50 {
  color: var(--gray-50);
}

.text-gray-100 {
  color: var(--gray-100);
}

.text-gray-200 {
  color: var(--gray-200);
}

.text-gray-300 {
  color: var(--gray-300);
}

.text-gray-400 {
  color: var(--gray-400);
}

.text-gray-500 {
  color: var(--gray-500);
}

.text-gray-600 {
  color: var(--gray-600);
}

.text-gray-700 {
  color: var(--gray-700);
}

.text-gray-800 {
  color: var(--gray-800);
}

.text-gray-900 {
  color: var(--gray-900);
}

.text-gray-950 {
  color: var(--gray-950);
}

.text-zinc-50 {
  color: var(--zinc-50);
}

.text-zinc-100 {
  color: var(--zinc-100);
}

.text-zinc-200 {
  color: var(--zinc-200);
}

.text-zinc-300 {
  color: var(--zinc-300);
}

.text-zinc-400 {
  color: var(--zinc-400);
}

.text-zinc-500 {
  color: var(--zinc-500);
}

.text-zinc-600 {
  color: var(--zinc-600);
}

.text-zinc-700 {
  color: var(--zinc-700);
}

.text-zinc-800 {
  color: var(--zinc-800);
}

.text-zinc-900 {
  color: var(--zinc-900);
}

.text-zinc-950 {
  color: var(--zinc-950);
}

.text-neutral-50 {
  color: var(--neutral-50);
}

.text-neutral-100 {
  color: var(--neutral-100);
}

.text-neutral-200 {
  color: var(--neutral-200);
}

.text-neutral-300 {
  color: var(--neutral-300);
}

.text-neutral-400 {
  color: var(--neutral-400);
}

.text-neutral-500 {
  color: var(--neutral-500);
}

.text-neutral-600 {
  color: var(--neutral-600);
}

.text-neutral-700 {
  color: var(--neutral-700);
}

.text-neutral-800 {
  color: var(--neutral-800);
}

.text-neutral-900 {
  color: var(--neutral-900);
}

.text-neutral-950 {
  color: var(--neutral-950);
}

.text-stone-50 {
  color: var(--stone-50);
}

.text-stone-100 {
  color: var(--stone-100);
}

.text-stone-200 {
  color: var(--stone-200);
}

.text-stone-300 {
  color: var(--stone-300);
}

.text-stone-400 {
  color: var(--stone-400);
}

.text-stone-500 {
  color: var(--stone-500);
}

.text-stone-600 {
  color: var(--stone-600);
}

.text-stone-700 {
  color: var(--stone-700);
}

.text-stone-800 {
  color: var(--stone-800);
}

.text-stone-900 {
  color: var(--stone-900);
}

.text-stone-950 {
  color: var(--stone-950);
}

.text-red-50 {
  color: var(--red-50);
}

.text-red-100 {
  color: var(--red-100);
}

.text-red-200 {
  color: var(--red-200);
}

.text-red-300 {
  color: var(--red-300);
}

.text-red-400 {
  color: var(--red-400);
}

.text-red-500 {
  color: var(--red-500);
}

.text-red-600 {
  color: var(--red-600);
}

.text-red-700 {
  color: var(--red-700);
}

.text-red-800 {
  color: var(--red-800);
}

.text-red-900 {
  color: var(--red-900);
}

.text-red-950 {
  color: var(--red-950);
}

.text-orange-50 {
  color: var(--orange-50);
}

.text-orange-100 {
  color: var(--orange-100);
}

.text-orange-200 {
  color: var(--orange-200);
}

.text-orange-300 {
  color: var(--orange-300);
}

.text-orange-400 {
  color: var(--orange-400);
}

.text-orange-500 {
  color: var(--orange-500);
}

.text-orange-600 {
  color: var(--orange-600);
}

.text-orange-700 {
  color: var(--orange-700);
}

.text-orange-800 {
  color: var(--orange-800);
}

.text-orange-900 {
  color: var(--orange-900);
}

.text-orange-950 {
  color: var(--orange-950);
}

.text-amber-50 {
  color: var(--amber-50);
}

.text-amber-100 {
  color: var(--amber-100);
}

.text-amber-200 {
  color: var(--amber-200);
}

.text-amber-300 {
  color: var(--amber-300);
}

.text-amber-400 {
  color: var(--amber-400);
}

.text-amber-500 {
  color: var(--amber-500);
}

.text-amber-600 {
  color: var(--amber-600);
}

.text-amber-700 {
  color: var(--amber-700);
}

.text-amber-800 {
  color: var(--amber-800);
}

.text-amber-900 {
  color: var(--amber-900);
}

.text-amber-950 {
  color: var(--amber-950);
}

.text-yellow-50 {
  color: var(--yellow-50);
}

.text-yellow-100 {
  color: var(--yellow-100);
}

.text-yellow-200 {
  color: var(--yellow-200);
}

.text-yellow-300 {
  color: var(--yellow-300);
}

.text-yellow-400 {
  color: var(--yellow-400);
}

.text-yellow-500 {
  color: var(--yellow-500);
}

.text-yellow-600 {
  color: var(--yellow-600);
}

.text-yellow-700 {
  color: var(--yellow-700);
}

.text-yellow-800 {
  color: var(--yellow-800);
}

.text-yellow-900 {
  color: var(--yellow-900);
}

.text-yellow-950 {
  color: var(--yellow-950);
}

.text-lime-50 {
  color: var(--lime-50);
}

.text-lime-100 {
  color: var(--lime-100);
}

.text-lime-200 {
  color: var(--lime-200);
}

.text-lime-300 {
  color: var(--lime-300);
}

.text-lime-400 {
  color: var(--lime-400);
}

.text-lime-500 {
  color: var(--lime-500);
}

.text-lime-600 {
  color: var(--lime-600);
}

.text-lime-700 {
  color: var(--lime-700);
}

.text-lime-800 {
  color: var(--lime-800);
}

.text-lime-900 {
  color: var(--lime-900);
}

.text-lime-950 {
  color: var(--lime-950);
}

.text-green-50 {
  color: var(--green-50);
}

.text-green-100 {
  color: var(--green-100);
}

.text-green-200 {
  color: var(--green-200);
}

.text-green-300 {
  color: var(--green-300);
}

.text-green-400 {
  color: var(--green-400);
}

.text-green-500 {
  color: var(--green-500);
}

.text-green-600 {
  color: var(--green-600);
}

.text-green-700 {
  color: var(--green-700);
}

.text-green-800 {
  color: var(--green-800);
}

.text-green-900 {
  color: var(--green-900);
}

.text-green-950 {
  color: var(--green-950);
}

.text-emerald-50 {
  color: var(--emerald-50);
}

.text-emerald-100 {
  color: var(--emerald-100);
}

.text-emerald-200 {
  color: var(--emerald-200);
}

.text-emerald-300 {
  color: var(--emerald-300);
}

.text-emerald-400 {
  color: var(--emerald-400);
}

.text-emerald-500 {
  color: var(--emerald-500);
}

.text-emerald-600 {
  color: var(--emerald-600);
}

.text-emerald-700 {
  color: var(--emerald-700);
}

.text-emerald-800 {
  color: var(--emerald-800);
}

.text-emerald-900 {
  color: var(--emerald-900);
}

.text-emerald-950 {
  color: var(--emerald-950);
}

.text-teal-50 {
  color: var(--teal-50);
}

.text-teal-100 {
  color: var(--teal-100);
}

.text-teal-200 {
  color: var(--teal-200);
}

.text-teal-300 {
  color: var(--teal-300);
}

.text-teal-400 {
  color: var(--teal-400);
}

.text-teal-500 {
  color: var(--teal-500);
}

.text-teal-600 {
  color: var(--teal-600);
}

.text-teal-700 {
  color: var(--teal-700);
}

.text-teal-800 {
  color: var(--teal-800);
}

.text-teal-900 {
  color: var(--teal-900);
}

.text-teal-950 {
  color: var(--teal-950);
}

.text-cyan-50 {
  color: var(--cyan-50);
}

.text-cyan-100 {
  color: var(--cyan-100);
}

.text-cyan-200 {
  color: var(--cyan-200);
}

.text-cyan-300 {
  color: var(--cyan-300);
}

.text-cyan-400 {
  color: var(--cyan-400);
}

.text-cyan-500 {
  color: var(--cyan-500);
}

.text-cyan-600 {
  color: var(--cyan-600);
}

.text-cyan-700 {
  color: var(--cyan-700);
}

.text-cyan-800 {
  color: var(--cyan-800);
}

.text-cyan-900 {
  color: var(--cyan-900);
}

.text-cyan-950 {
  color: var(--cyan-950);
}

.text-sky-50 {
  color: var(--sky-50);
}

.text-sky-100 {
  color: var(--sky-100);
}

.text-sky-200 {
  color: var(--sky-200);
}

.text-sky-300 {
  color: var(--sky-300);
}

.text-sky-400 {
  color: var(--sky-400);
}

.text-sky-500 {
  color: var(--sky-500);
}

.text-sky-600 {
  color: var(--sky-600);
}

.text-sky-700 {
  color: var(--sky-700);
}

.text-sky-800 {
  color: var(--sky-800);
}

.text-sky-900 {
  color: var(--sky-900);
}

.text-sky-950 {
  color: var(--sky-950);
}

.text-blue-50 {
  color: var(--blue-50);
}

.text-blue-100 {
  color: var(--blue-100);
}

.text-blue-200 {
  color: var(--blue-200);
}

.text-blue-300 {
  color: var(--blue-300);
}

.text-blue-400 {
  color: var(--blue-400);
}

.text-blue-500 {
  color: var(--blue-500);
}

.text-blue-600 {
  color: var(--blue-600);
}

.text-blue-700 {
  color: var(--blue-700);
}

.text-blue-800 {
  color: var(--blue-800);
}

.text-blue-900 {
  color: var(--blue-900);
}

.text-blue-950 {
  color: var(--blue-950);
}

.text-indigo-50 {
  color: var(--indigo-50);
}

.text-indigo-100 {
  color: var(--indigo-100);
}

.text-indigo-200 {
  color: var(--indigo-200);
}

.text-indigo-300 {
  color: var(--indigo-300);
}

.text-indigo-400 {
  color: var(--indigo-400);
}

.text-indigo-500 {
  color: var(--indigo-500);
}

.text-indigo-600 {
  color: var(--indigo-600);
}

.text-indigo-700 {
  color: var(--indigo-700);
}

.text-indigo-800 {
  color: var(--indigo-800);
}

.text-indigo-900 {
  color: var(--indigo-900);
}

.text-indigo-950 {
  color: var(--indigo-950);
}

.text-violet-50 {
  color: var(--violet-50);
}

.text-violet-100 {
  color: var(--violet-100);
}

.text-violet-200 {
  color: var(--violet-200);
}

.text-violet-300 {
  color: var(--violet-300);
}

.text-violet-400 {
  color: var(--violet-400);
}

.text-violet-500 {
  color: var(--violet-500);
}

.text-violet-600 {
  color: var(--violet-600);
}

.text-violet-700 {
  color: var(--violet-700);
}

.text-violet-800 {
  color: var(--violet-800);
}

.text-violet-900 {
  color: var(--violet-900);
}

.text-violet-950 {
  color: var(--violet-950);
}

.text-purple-50 {
  color: var(--purple-50);
}

.text-purple-100 {
  color: var(--purple-100);
}

.text-purple-200 {
  color: var(--purple-200);
}

.text-purple-300 {
  color: var(--purple-300);
}

.text-purple-400 {
  color: var(--purple-400);
}

.text-purple-500 {
  color: var(--purple-500);
}

.text-purple-600 {
  color: var(--purple-600);
}

.text-purple-700 {
  color: var(--purple-700);
}

.text-purple-800 {
  color: var(--purple-800);
}

.text-purple-900 {
  color: var(--purple-900);
}

.text-purple-950 {
  color: var(--purple-950);
}

.text-fuchsia-50 {
  color: var(--fuchsia-50);
}

.text-fuchsia-100 {
  color: var(--fuchsia-100);
}

.text-fuchsia-200 {
  color: var(--fuchsia-200);
}

.text-fuchsia-300 {
  color: var(--fuchsia-300);
}

.text-fuchsia-400 {
  color: var(--fuchsia-400);
}

.text-fuchsia-500 {
  color: var(--fuchsia-500);
}

.text-fuchsia-600 {
  color: var(--fuchsia-600);
}

.text-fuchsia-700 {
  color: var(--fuchsia-700);
}

.text-fuchsia-800 {
  color: var(--fuchsia-800);
}

.text-fuchsia-900 {
  color: var(--fuchsia-900);
}

.text-fuchsia-950 {
  color: var(--fuchsia-950);
}

.text-pink-50 {
  color: var(--pink-50);
}

.text-pink-100 {
  color: var(--pink-100);
}

.text-pink-200 {
  color: var(--pink-200);
}

.text-pink-300 {
  color: var(--pink-300);
}

.text-pink-400 {
  color: var(--pink-400);
}

.text-pink-500 {
  color: var(--pink-500);
}

.text-pink-600 {
  color: var(--pink-600);
}

.text-pink-700 {
  color: var(--pink-700);
}

.text-pink-800 {
  color: var(--pink-800);
}

.text-pink-900 {
  color: var(--pink-900);
}

.text-pink-950 {
  color: var(--pink-950);
}

.text-rose-50 {
  color: var(--rose-50);
}

.text-rose-100 {
  color: var(--rose-100);
}

.text-rose-200 {
  color: var(--rose-200);
}

.text-rose-300 {
  color: var(--rose-300);
}

.text-rose-400 {
  color: var(--rose-400);
}

.text-rose-500 {
  color: var(--rose-500);
}

.text-rose-600 {
  color: var(--rose-600);
}

.text-rose-700 {
  color: var(--rose-700);
}

.text-rose-800 {
  color: var(--rose-800);
}

.text-rose-900 {
  color: var(--rose-900);
}

.text-rose-950 {
  color: var(--rose-950);
}

.text-white {
  color: #ffffff;
}

.text-black {
  color: #000000;
}

.bg-slate-50 {
  background-color: var(--slate-50);
}

.bg-slate-100 {
  background-color: var(--slate-100);
}

.bg-slate-200 {
  background-color: var(--slate-200);
}

.bg-slate-300 {
  background-color: var(--slate-300);
}

.bg-slate-400 {
  background-color: var(--slate-400);
}

.bg-slate-500 {
  background-color: var(--slate-500);
}

.bg-slate-600 {
  background-color: var(--slate-600);
}

.bg-slate-700 {
  background-color: var(--slate-700);
}

.bg-slate-800 {
  background-color: var(--slate-800);
}

.bg-slate-900 {
  background-color: var(--slate-900);
}

.bg-slate-950 {
  background-color: var(--slate-950);
}

.bg-gray-50 {
  background-color: var(--gray-50);
}

.bg-gray-100 {
  background-color: var(--gray-100);
}

.bg-gray-200 {
  background-color: var(--gray-200);
}

.bg-gray-300 {
  background-color: var(--gray-300);
}

.bg-gray-400 {
  background-color: var(--gray-400);
}

.bg-gray-500 {
  background-color: var(--gray-500);
}

.bg-gray-600 {
  background-color: var(--gray-600);
}

.bg-gray-700 {
  background-color: var(--gray-700);
}

.bg-gray-800 {
  background-color: var(--gray-800);
}

.bg-gray-900 {
  background-color: var(--gray-900);
}

.bg-gray-950 {
  background-color: var(--gray-950);
}

.bg-zinc-50 {
  background-color: var(--zinc-50);
}

.bg-zinc-100 {
  background-color: var(--zinc-100);
}

.bg-zinc-200 {
  background-color: var(--zinc-200);
}

.bg-zinc-300 {
  background-color: var(--zinc-300);
}

.bg-zinc-400 {
  background-color: var(--zinc-400);
}

.bg-zinc-500 {
  background-color: var(--zinc-500);
}

.bg-zinc-600 {
  background-color: var(--zinc-600);
}

.bg-zinc-700 {
  background-color: var(--zinc-700);
}

.bg-zinc-800 {
  background-color: var(--zinc-800);
}

.bg-zinc-900 {
  background-color: var(--zinc-900);
}

.bg-zinc-950 {
  background-color: var(--zinc-950);
}

.bg-neutral-50 {
  background-color: var(--neutral-50);
}

.bg-neutral-100 {
  background-color: var(--neutral-100);
}

.bg-neutral-200 {
  background-color: var(--neutral-200);
}

.bg-neutral-300 {
  background-color: var(--neutral-300);
}

.bg-neutral-400 {
  background-color: var(--neutral-400);
}

.bg-neutral-500 {
  background-color: var(--neutral-500);
}

.bg-neutral-600 {
  background-color: var(--neutral-600);
}

.bg-neutral-700 {
  background-color: var(--neutral-700);
}

.bg-neutral-800 {
  background-color: var(--neutral-800);
}

.bg-neutral-900 {
  background-color: var(--neutral-900);
}

.bg-neutral-950 {
  background-color: var(--neutral-950);
}

.bg-stone-50 {
  background-color: var(--stone-50);
}

.bg-stone-100 {
  background-color: var(--stone-100);
}

.bg-stone-200 {
  background-color: var(--stone-200);
}

.bg-stone-300 {
  background-color: var(--stone-300);
}

.bg-stone-400 {
  background-color: var(--stone-400);
}

.bg-stone-500 {
  background-color: var(--stone-500);
}

.bg-stone-600 {
  background-color: var(--stone-600);
}

.bg-stone-700 {
  background-color: var(--stone-700);
}

.bg-stone-800 {
  background-color: var(--stone-800);
}

.bg-stone-900 {
  background-color: var(--stone-900);
}

.bg-stone-950 {
  background-color: var(--stone-950);
}

.bg-red-50 {
  background-color: var(--red-50);
}

.bg-red-100 {
  background-color: var(--red-100);
}

.bg-red-200 {
  background-color: var(--red-200);
}

.bg-red-300 {
  background-color: var(--red-300);
}

.bg-red-400 {
  background-color: var(--red-400);
}

.bg-red-500 {
  background-color: var(--red-500);
}

.bg-red-600 {
  background-color: var(--red-600);
}

.bg-red-700 {
  background-color: var(--red-700);
}

.bg-red-800 {
  background-color: var(--red-800);
}

.bg-red-900 {
  background-color: var(--red-900);
}

.bg-red-950 {
  background-color: var(--red-950);
}

.bg-orange-50 {
  background-color: var(--orange-50);
}

.bg-orange-100 {
  background-color: var(--orange-100);
}

.bg-orange-200 {
  background-color: var(--orange-200);
}

.bg-orange-300 {
  background-color: var(--orange-300);
}

.bg-orange-400 {
  background-color: var(--orange-400);
}

.bg-orange-500 {
  background-color: var(--orange-500);
}

.bg-orange-600 {
  background-color: var(--orange-600);
}

.bg-orange-700 {
  background-color: var(--orange-700);
}

.bg-orange-800 {
  background-color: var(--orange-800);
}

.bg-orange-900 {
  background-color: var(--orange-900);
}

.bg-orange-950 {
  background-color: var(--orange-950);
}

.bg-amber-50 {
  background-color: var(--amber-50);
}

.bg-amber-100 {
  background-color: var(--amber-100);
}

.bg-amber-200 {
  background-color: var(--amber-200);
}

.bg-amber-300 {
  background-color: var(--amber-300);
}

.bg-amber-400 {
  background-color: var(--amber-400);
}

.bg-amber-500 {
  background-color: var(--amber-500);
}

.bg-amber-600 {
  background-color: var(--amber-600);
}

.bg-amber-700 {
  background-color: var(--amber-700);
}

.bg-amber-800 {
  background-color: var(--amber-800);
}

.bg-amber-900 {
  background-color: var(--amber-900);
}

.bg-amber-950 {
  background-color: var(--amber-950);
}

.bg-yellow-50 {
  background-color: var(--yellow-50);
}

.bg-yellow-100 {
  background-color: var(--yellow-100);
}

.bg-yellow-200 {
  background-color: var(--yellow-200);
}

.bg-yellow-300 {
  background-color: var(--yellow-300);
}

.bg-yellow-400 {
  background-color: var(--yellow-400);
}

.bg-yellow-500 {
  background-color: var(--yellow-500);
}

.bg-yellow-600 {
  background-color: var(--yellow-600);
}

.bg-yellow-700 {
  background-color: var(--yellow-700);
}

.bg-yellow-800 {
  background-color: var(--yellow-800);
}

.bg-yellow-900 {
  background-color: var(--yellow-900);
}

.bg-yellow-950 {
  background-color: var(--yellow-950);
}

.bg-lime-50 {
  background-color: var(--lime-50);
}

.bg-lime-100 {
  background-color: var(--lime-100);
}

.bg-lime-200 {
  background-color: var(--lime-200);
}

.bg-lime-300 {
  background-color: var(--lime-300);
}

.bg-lime-400 {
  background-color: var(--lime-400);
}

.bg-lime-500 {
  background-color: var(--lime-500);
}

.bg-lime-600 {
  background-color: var(--lime-600);
}

.bg-lime-700 {
  background-color: var(--lime-700);
}

.bg-lime-800 {
  background-color: var(--lime-800);
}

.bg-lime-900 {
  background-color: var(--lime-900);
}

.bg-lime-950 {
  background-color: var(--lime-950);
}

.bg-green-50 {
  background-color: var(--green-50);
}

.bg-green-100 {
  background-color: var(--green-100);
}

.bg-green-200 {
  background-color: var(--green-200);
}

.bg-green-300 {
  background-color: var(--green-300);
}

.bg-green-400 {
  background-color: var(--green-400);
}

.bg-green-500 {
  background-color: var(--green-500);
}

.bg-green-600 {
  background-color: var(--green-600);
}

.bg-green-700 {
  background-color: var(--green-700);
}

.bg-green-800 {
  background-color: var(--green-800);
}

.bg-green-900 {
  background-color: var(--green-900);
}

.bg-green-950 {
  background-color: var(--green-950);
}

.bg-emerald-50 {
  background-color: var(--emerald-50);
}

.bg-emerald-100 {
  background-color: var(--emerald-100);
}

.bg-emerald-200 {
  background-color: var(--emerald-200);
}

.bg-emerald-300 {
  background-color: var(--emerald-300);
}

.bg-emerald-400 {
  background-color: var(--emerald-400);
}

.bg-emerald-500 {
  background-color: var(--emerald-500);
}

.bg-emerald-600 {
  background-color: var(--emerald-600);
}

.bg-emerald-700 {
  background-color: var(--emerald-700);
}

.bg-emerald-800 {
  background-color: var(--emerald-800);
}

.bg-emerald-900 {
  background-color: var(--emerald-900);
}

.bg-emerald-950 {
  background-color: var(--emerald-950);
}

.bg-teal-50 {
  background-color: var(--teal-50);
}

.bg-teal-100 {
  background-color: var(--teal-100);
}

.bg-teal-200 {
  background-color: var(--teal-200);
}

.bg-teal-300 {
  background-color: var(--teal-300);
}

.bg-teal-400 {
  background-color: var(--teal-400);
}

.bg-teal-500 {
  background-color: var(--teal-500);
}

.bg-teal-600 {
  background-color: var(--teal-600);
}

.bg-teal-700 {
  background-color: var(--teal-700);
}

.bg-teal-800 {
  background-color: var(--teal-800);
}

.bg-teal-900 {
  background-color: var(--teal-900);
}

.bg-teal-950 {
  background-color: var(--teal-950);
}

.bg-cyan-50 {
  background-color: var(--cyan-50);
}

.bg-cyan-100 {
  background-color: var(--cyan-100);
}

.bg-cyan-200 {
  background-color: var(--cyan-200);
}

.bg-cyan-300 {
  background-color: var(--cyan-300);
}

.bg-cyan-400 {
  background-color: var(--cyan-400);
}

.bg-cyan-500 {
  background-color: var(--cyan-500);
}

.bg-cyan-600 {
  background-color: var(--cyan-600);
}

.bg-cyan-700 {
  background-color: var(--cyan-700);
}

.bg-cyan-800 {
  background-color: var(--cyan-800);
}

.bg-cyan-900 {
  background-color: var(--cyan-900);
}

.bg-cyan-950 {
  background-color: var(--cyan-950);
}

.bg-sky-50 {
  background-color: var(--sky-50);
}

.bg-sky-100 {
  background-color: var(--sky-100);
}

.bg-sky-200 {
  background-color: var(--sky-200);
}

.bg-sky-300 {
  background-color: var(--sky-300);
}

.bg-sky-400 {
  background-color: var(--sky-400);
}

.bg-sky-500 {
  background-color: var(--sky-500);
}

.bg-sky-600 {
  background-color: var(--sky-600);
}

.bg-sky-700 {
  background-color: var(--sky-700);
}

.bg-sky-800 {
  background-color: var(--sky-800);
}

.bg-sky-900 {
  background-color: var(--sky-900);
}

.bg-sky-950 {
  background-color: var(--sky-950);
}

.bg-blue-50 {
  background-color: var(--blue-50);
}

.bg-blue-100 {
  background-color: var(--blue-100);
}

.bg-blue-200 {
  background-color: var(--blue-200);
}

.bg-blue-300 {
  background-color: var(--blue-300);
}

.bg-blue-400 {
  background-color: var(--blue-400);
}

.bg-blue-500 {
  background-color: var(--blue-500);
}

.bg-blue-600 {
  background-color: var(--blue-600);
}

.bg-blue-700 {
  background-color: var(--blue-700);
}

.bg-blue-800 {
  background-color: var(--blue-800);
}

.bg-blue-900 {
  background-color: var(--blue-900);
}

.bg-blue-950 {
  background-color: var(--blue-950);
}

.bg-indigo-50 {
  background-color: var(--indigo-50);
}

.bg-indigo-100 {
  background-color: var(--indigo-100);
}

.bg-indigo-200 {
  background-color: var(--indigo-200);
}

.bg-indigo-300 {
  background-color: var(--indigo-300);
}

.bg-indigo-400 {
  background-color: var(--indigo-400);
}

.bg-indigo-500 {
  background-color: var(--indigo-500);
}

.bg-indigo-600 {
  background-color: var(--indigo-600);
}

.bg-indigo-700 {
  background-color: var(--indigo-700);
}

.bg-indigo-800 {
  background-color: var(--indigo-800);
}

.bg-indigo-900 {
  background-color: var(--indigo-900);
}

.bg-indigo-950 {
  background-color: var(--indigo-950);
}

.bg-violet-50 {
  background-color: var(--violet-50);
}

.bg-violet-100 {
  background-color: var(--violet-100);
}

.bg-violet-200 {
  background-color: var(--violet-200);
}

.bg-violet-300 {
  background-color: var(--violet-300);
}

.bg-violet-400 {
  background-color: var(--violet-400);
}

.bg-violet-500 {
  background-color: var(--violet-500);
}

.bg-violet-600 {
  background-color: var(--violet-600);
}

.bg-violet-700 {
  background-color: var(--violet-700);
}

.bg-violet-800 {
  background-color: var(--violet-800);
}

.bg-violet-900 {
  background-color: var(--violet-900);
}

.bg-violet-950 {
  background-color: var(--violet-950);
}

.bg-purple-50 {
  background-color: var(--purple-50);
}

.bg-purple-100 {
  background-color: var(--purple-100);
}

.bg-purple-200 {
  background-color: var(--purple-200);
}

.bg-purple-300 {
  background-color: var(--purple-300);
}

.bg-purple-400 {
  background-color: var(--purple-400);
}

.bg-purple-500 {
  background-color: var(--purple-500);
}

.bg-purple-600 {
  background-color: var(--purple-600);
}

.bg-purple-700 {
  background-color: var(--purple-700);
}

.bg-purple-800 {
  background-color: var(--purple-800);
}

.bg-purple-900 {
  background-color: var(--purple-900);
}

.bg-purple-950 {
  background-color: var(--purple-950);
}

.bg-fuchsia-50 {
  background-color: var(--fuchsia-50);
}

.bg-fuchsia-100 {
  background-color: var(--fuchsia-100);
}

.bg-fuchsia-200 {
  background-color: var(--fuchsia-200);
}

.bg-fuchsia-300 {
  background-color: var(--fuchsia-300);
}

.bg-fuchsia-400 {
  background-color: var(--fuchsia-400);
}

.bg-fuchsia-500 {
  background-color: var(--fuchsia-500);
}

.bg-fuchsia-600 {
  background-color: var(--fuchsia-600);
}

.bg-fuchsia-700 {
  background-color: var(--fuchsia-700);
}

.bg-fuchsia-800 {
  background-color: var(--fuchsia-800);
}

.bg-fuchsia-900 {
  background-color: var(--fuchsia-900);
}

.bg-fuchsia-950 {
  background-color: var(--fuchsia-950);
}

.bg-pink-50 {
  background-color: var(--pink-50);
}

.bg-pink-100 {
  background-color: var(--pink-100);
}

.bg-pink-200 {
  background-color: var(--pink-200);
}

.bg-pink-300 {
  background-color: var(--pink-300);
}

.bg-pink-400 {
  background-color: var(--pink-400);
}

.bg-pink-500 {
  background-color: var(--pink-500);
}

.bg-pink-600 {
  background-color: var(--pink-600);
}

.bg-pink-700 {
  background-color: var(--pink-700);
}

.bg-pink-800 {
  background-color: var(--pink-800);
}

.bg-pink-900 {
  background-color: var(--pink-900);
}

.bg-pink-950 {
  background-color: var(--pink-950);
}

.bg-rose-50 {
  background-color: var(--rose-50);
}

.bg-rose-100 {
  background-color: var(--rose-100);
}

.bg-rose-200 {
  background-color: var(--rose-200);
}

.bg-rose-300 {
  background-color: var(--rose-300);
}

.bg-rose-400 {
  background-color: var(--rose-400);
}

.bg-rose-500 {
  background-color: var(--rose-500);
}

.bg-rose-600 {
  background-color: var(--rose-600);
}

.bg-rose-700 {
  background-color: var(--rose-700);
}

.bg-rose-800 {
  background-color: var(--rose-800);
}

.bg-rose-900 {
  background-color: var(--rose-900);
}

.bg-rose-950 {
  background-color: var(--rose-950);
}

.bg-white {
  background-color: #ffffff;
}

.bg-black {
  background-color: #000000;
}

.border-slate-50 {
  border-color: var(--slate-50);
}

.border-slate-100 {
  border-color: var(--slate-100);
}

.border-slate-200 {
  border-color: var(--slate-200);
}

.border-slate-300 {
  border-color: var(--slate-300);
}

.border-slate-400 {
  border-color: var(--slate-400);
}

.border-slate-500 {
  border-color: var(--slate-500);
}

.border-slate-600 {
  border-color: var(--slate-600);
}

.border-slate-700 {
  border-color: var(--slate-700);
}

.border-slate-800 {
  border-color: var(--slate-800);
}

.border-slate-900 {
  border-color: var(--slate-900);
}

.border-slate-950 {
  border-color: var(--slate-950);
}

.border-gray-50 {
  border-color: var(--gray-50);
}

.border-gray-100 {
  border-color: var(--gray-100);
}

.border-gray-200 {
  border-color: var(--gray-200);
}

.border-gray-300 {
  border-color: var(--gray-300);
}

.border-gray-400 {
  border-color: var(--gray-400);
}

.border-gray-500 {
  border-color: var(--gray-500);
}

.border-gray-600 {
  border-color: var(--gray-600);
}

.border-gray-700 {
  border-color: var(--gray-700);
}

.border-gray-800 {
  border-color: var(--gray-800);
}

.border-gray-900 {
  border-color: var(--gray-900);
}

.border-gray-950 {
  border-color: var(--gray-950);
}

.border-zinc-50 {
  border-color: var(--zinc-50);
}

.border-zinc-100 {
  border-color: var(--zinc-100);
}

.border-zinc-200 {
  border-color: var(--zinc-200);
}

.border-zinc-300 {
  border-color: var(--zinc-300);
}

.border-zinc-400 {
  border-color: var(--zinc-400);
}

.border-zinc-500 {
  border-color: var(--zinc-500);
}

.border-zinc-600 {
  border-color: var(--zinc-600);
}

.border-zinc-700 {
  border-color: var(--zinc-700);
}

.border-zinc-800 {
  border-color: var(--zinc-800);
}

.border-zinc-900 {
  border-color: var(--zinc-900);
}

.border-zinc-950 {
  border-color: var(--zinc-950);
}

.border-neutral-50 {
  border-color: var(--neutral-50);
}

.border-neutral-100 {
  border-color: var(--neutral-100);
}

.border-neutral-200 {
  border-color: var(--neutral-200);
}

.border-neutral-300 {
  border-color: var(--neutral-300);
}

.border-neutral-400 {
  border-color: var(--neutral-400);
}

.border-neutral-500 {
  border-color: var(--neutral-500);
}

.border-neutral-600 {
  border-color: var(--neutral-600);
}

.border-neutral-700 {
  border-color: var(--neutral-700);
}

.border-neutral-800 {
  border-color: var(--neutral-800);
}

.border-neutral-900 {
  border-color: var(--neutral-900);
}

.border-neutral-950 {
  border-color: var(--neutral-950);
}

.border-stone-50 {
  border-color: var(--stone-50);
}

.border-stone-100 {
  border-color: var(--stone-100);
}

.border-stone-200 {
  border-color: var(--stone-200);
}

.border-stone-300 {
  border-color: var(--stone-300);
}

.border-stone-400 {
  border-color: var(--stone-400);
}

.border-stone-500 {
  border-color: var(--stone-500);
}

.border-stone-600 {
  border-color: var(--stone-600);
}

.border-stone-700 {
  border-color: var(--stone-700);
}

.border-stone-800 {
  border-color: var(--stone-800);
}

.border-stone-900 {
  border-color: var(--stone-900);
}

.border-stone-950 {
  border-color: var(--stone-950);
}

.border-red-50 {
  border-color: var(--red-50);
}

.border-red-100 {
  border-color: var(--red-100);
}

.border-red-200 {
  border-color: var(--red-200);
}

.border-red-300 {
  border-color: var(--red-300);
}

.border-red-400 {
  border-color: var(--red-400);
}

.border-red-500 {
  border-color: var(--red-500);
}

.border-red-600 {
  border-color: var(--red-600);
}

.border-red-700 {
  border-color: var(--red-700);
}

.border-red-800 {
  border-color: var(--red-800);
}

.border-red-900 {
  border-color: var(--red-900);
}

.border-red-950 {
  border-color: var(--red-950);
}

.border-orange-50 {
  border-color: var(--orange-50);
}

.border-orange-100 {
  border-color: var(--orange-100);
}

.border-orange-200 {
  border-color: var(--orange-200);
}

.border-orange-300 {
  border-color: var(--orange-300);
}

.border-orange-400 {
  border-color: var(--orange-400);
}

.border-orange-500 {
  border-color: var(--orange-500);
}

.border-orange-600 {
  border-color: var(--orange-600);
}

.border-orange-700 {
  border-color: var(--orange-700);
}

.border-orange-800 {
  border-color: var(--orange-800);
}

.border-orange-900 {
  border-color: var(--orange-900);
}

.border-orange-950 {
  border-color: var(--orange-950);
}

.border-amber-50 {
  border-color: var(--amber-50);
}

.border-amber-100 {
  border-color: var(--amber-100);
}

.border-amber-200 {
  border-color: var(--amber-200);
}

.border-amber-300 {
  border-color: var(--amber-300);
}

.border-amber-400 {
  border-color: var(--amber-400);
}

.border-amber-500 {
  border-color: var(--amber-500);
}

.border-amber-600 {
  border-color: var(--amber-600);
}

.border-amber-700 {
  border-color: var(--amber-700);
}

.border-amber-800 {
  border-color: var(--amber-800);
}

.border-amber-900 {
  border-color: var(--amber-900);
}

.border-amber-950 {
  border-color: var(--amber-950);
}

.border-yellow-50 {
  border-color: var(--yellow-50);
}

.border-yellow-100 {
  border-color: var(--yellow-100);
}

.border-yellow-200 {
  border-color: var(--yellow-200);
}

.border-yellow-300 {
  border-color: var(--yellow-300);
}

.border-yellow-400 {
  border-color: var(--yellow-400);
}

.border-yellow-500 {
  border-color: var(--yellow-500);
}

.border-yellow-600 {
  border-color: var(--yellow-600);
}

.border-yellow-700 {
  border-color: var(--yellow-700);
}

.border-yellow-800 {
  border-color: var(--yellow-800);
}

.border-yellow-900 {
  border-color: var(--yellow-900);
}

.border-yellow-950 {
  border-color: var(--yellow-950);
}

.border-lime-50 {
  border-color: var(--lime-50);
}

.border-lime-100 {
  border-color: var(--lime-100);
}

.border-lime-200 {
  border-color: var(--lime-200);
}

.border-lime-300 {
  border-color: var(--lime-300);
}

.border-lime-400 {
  border-color: var(--lime-400);
}

.border-lime-500 {
  border-color: var(--lime-500);
}

.border-lime-600 {
  border-color: var(--lime-600);
}

.border-lime-700 {
  border-color: var(--lime-700);
}

.border-lime-800 {
  border-color: var(--lime-800);
}

.border-lime-900 {
  border-color: var(--lime-900);
}

.border-lime-950 {
  border-color: var(--lime-950);
}

.border-green-50 {
  border-color: var(--green-50);
}

.border-green-100 {
  border-color: var(--green-100);
}

.border-green-200 {
  border-color: var(--green-200);
}

.border-green-300 {
  border-color: var(--green-300);
}

.border-green-400 {
  border-color: var(--green-400);
}

.border-green-500 {
  border-color: var(--green-500);
}

.border-green-600 {
  border-color: var(--green-600);
}

.border-green-700 {
  border-color: var(--green-700);
}

.border-green-800 {
  border-color: var(--green-800);
}

.border-green-900 {
  border-color: var(--green-900);
}

.border-green-950 {
  border-color: var(--green-950);
}

.border-emerald-50 {
  border-color: var(--emerald-50);
}

.border-emerald-100 {
  border-color: var(--emerald-100);
}

.border-emerald-200 {
  border-color: var(--emerald-200);
}

.border-emerald-300 {
  border-color: var(--emerald-300);
}

.border-emerald-400 {
  border-color: var(--emerald-400);
}

.border-emerald-500 {
  border-color: var(--emerald-500);
}

.border-emerald-600 {
  border-color: var(--emerald-600);
}

.border-emerald-700 {
  border-color: var(--emerald-700);
}

.border-emerald-800 {
  border-color: var(--emerald-800);
}

.border-emerald-900 {
  border-color: var(--emerald-900);
}

.border-emerald-950 {
  border-color: var(--emerald-950);
}

.border-teal-50 {
  border-color: var(--teal-50);
}

.border-teal-100 {
  border-color: var(--teal-100);
}

.border-teal-200 {
  border-color: var(--teal-200);
}

.border-teal-300 {
  border-color: var(--teal-300);
}

.border-teal-400 {
  border-color: var(--teal-400);
}

.border-teal-500 {
  border-color: var(--teal-500);
}

.border-teal-600 {
  border-color: var(--teal-600);
}

.border-teal-700 {
  border-color: var(--teal-700);
}

.border-teal-800 {
  border-color: var(--teal-800);
}

.border-teal-900 {
  border-color: var(--teal-900);
}

.border-teal-950 {
  border-color: var(--teal-950);
}

.border-cyan-50 {
  border-color: var(--cyan-50);
}

.border-cyan-100 {
  border-color: var(--cyan-100);
}

.border-cyan-200 {
  border-color: var(--cyan-200);
}

.border-cyan-300 {
  border-color: var(--cyan-300);
}

.border-cyan-400 {
  border-color: var(--cyan-400);
}

.border-cyan-500 {
  border-color: var(--cyan-500);
}

.border-cyan-600 {
  border-color: var(--cyan-600);
}

.border-cyan-700 {
  border-color: var(--cyan-700);
}

.border-cyan-800 {
  border-color: var(--cyan-800);
}

.border-cyan-900 {
  border-color: var(--cyan-900);
}

.border-cyan-950 {
  border-color: var(--cyan-950);
}

.border-sky-50 {
  border-color: var(--sky-50);
}

.border-sky-100 {
  border-color: var(--sky-100);
}

.border-sky-200 {
  border-color: var(--sky-200);
}

.border-sky-300 {
  border-color: var(--sky-300);
}

.border-sky-400 {
  border-color: var(--sky-400);
}

.border-sky-500 {
  border-color: var(--sky-500);
}

.border-sky-600 {
  border-color: var(--sky-600);
}

.border-sky-700 {
  border-color: var(--sky-700);
}

.border-sky-800 {
  border-color: var(--sky-800);
}

.border-sky-900 {
  border-color: var(--sky-900);
}

.border-sky-950 {
  border-color: var(--sky-950);
}

.border-blue-50 {
  border-color: var(--blue-50);
}

.border-blue-100 {
  border-color: var(--blue-100);
}

.border-blue-200 {
  border-color: var(--blue-200);
}

.border-blue-300 {
  border-color: var(--blue-300);
}

.border-blue-400 {
  border-color: var(--blue-400);
}

.border-blue-500 {
  border-color: var(--blue-500);
}

.border-blue-600 {
  border-color: var(--blue-600);
}

.border-blue-700 {
  border-color: var(--blue-700);
}

.border-blue-800 {
  border-color: var(--blue-800);
}

.border-blue-900 {
  border-color: var(--blue-900);
}

.border-blue-950 {
  border-color: var(--blue-950);
}

.border-indigo-50 {
  border-color: var(--indigo-50);
}

.border-indigo-100 {
  border-color: var(--indigo-100);
}

.border-indigo-200 {
  border-color: var(--indigo-200);
}

.border-indigo-300 {
  border-color: var(--indigo-300);
}

.border-indigo-400 {
  border-color: var(--indigo-400);
}

.border-indigo-500 {
  border-color: var(--indigo-500);
}

.border-indigo-600 {
  border-color: var(--indigo-600);
}

.border-indigo-700 {
  border-color: var(--indigo-700);
}

.border-indigo-800 {
  border-color: var(--indigo-800);
}

.border-indigo-900 {
  border-color: var(--indigo-900);
}

.border-indigo-950 {
  border-color: var(--indigo-950);
}

.border-violet-50 {
  border-color: var(--violet-50);
}

.border-violet-100 {
  border-color: var(--violet-100);
}

.border-violet-200 {
  border-color: var(--violet-200);
}

.border-violet-300 {
  border-color: var(--violet-300);
}

.border-violet-400 {
  border-color: var(--violet-400);
}

.border-violet-500 {
  border-color: var(--violet-500);
}

.border-violet-600 {
  border-color: var(--violet-600);
}

.border-violet-700 {
  border-color: var(--violet-700);
}

.border-violet-800 {
  border-color: var(--violet-800);
}

.border-violet-900 {
  border-color: var(--violet-900);
}

.border-violet-950 {
  border-color: var(--violet-950);
}

.border-purple-50 {
  border-color: var(--purple-50);
}

.border-purple-100 {
  border-color: var(--purple-100);
}

.border-purple-200 {
  border-color: var(--purple-200);
}

.border-purple-300 {
  border-color: var(--purple-300);
}

.border-purple-400 {
  border-color: var(--purple-400);
}

.border-purple-500 {
  border-color: var(--purple-500);
}

.border-purple-600 {
  border-color: var(--purple-600);
}

.border-purple-700 {
  border-color: var(--purple-700);
}

.border-purple-800 {
  border-color: var(--purple-800);
}

.border-purple-900 {
  border-color: var(--purple-900);
}

.border-purple-950 {
  border-color: var(--purple-950);
}

.border-fuchsia-50 {
  border-color: var(--fuchsia-50);
}

.border-fuchsia-100 {
  border-color: var(--fuchsia-100);
}

.border-fuchsia-200 {
  border-color: var(--fuchsia-200);
}

.border-fuchsia-300 {
  border-color: var(--fuchsia-300);
}

.border-fuchsia-400 {
  border-color: var(--fuchsia-400);
}

.border-fuchsia-500 {
  border-color: var(--fuchsia-500);
}

.border-fuchsia-600 {
  border-color: var(--fuchsia-600);
}

.border-fuchsia-700 {
  border-color: var(--fuchsia-700);
}

.border-fuchsia-800 {
  border-color: var(--fuchsia-800);
}

.border-fuchsia-900 {
  border-color: var(--fuchsia-900);
}

.border-fuchsia-950 {
  border-color: var(--fuchsia-950);
}

.border-pink-50 {
  border-color: var(--pink-50);
}

.border-pink-100 {
  border-color: var(--pink-100);
}

.border-pink-200 {
  border-color: var(--pink-200);
}

.border-pink-300 {
  border-color: var(--pink-300);
}

.border-pink-400 {
  border-color: var(--pink-400);
}

.border-pink-500 {
  border-color: var(--pink-500);
}

.border-pink-600 {
  border-color: var(--pink-600);
}

.border-pink-700 {
  border-color: var(--pink-700);
}

.border-pink-800 {
  border-color: var(--pink-800);
}

.border-pink-900 {
  border-color: var(--pink-900);
}

.border-pink-950 {
  border-color: var(--pink-950);
}

.border-rose-50 {
  border-color: var(--rose-50);
}

.border-rose-100 {
  border-color: var(--rose-100);
}

.border-rose-200 {
  border-color: var(--rose-200);
}

.border-rose-300 {
  border-color: var(--rose-300);
}

.border-rose-400 {
  border-color: var(--rose-400);
}

.border-rose-500 {
  border-color: var(--rose-500);
}

.border-rose-600 {
  border-color: var(--rose-600);
}

.border-rose-700 {
  border-color: var(--rose-700);
}

.border-rose-800 {
  border-color: var(--rose-800);
}

.border-rose-900 {
  border-color: var(--rose-900);
}

.border-rose-950 {
  border-color: var(--rose-950);
}

.border-t-slate-50 {
  border-top-color: var(--slate-50);
}

.border-r-slate-50 {
  border-right-color: var(--slate-50);
}

.border-b-slate-50 {
  border-bottom-color: var(--slate-50);
}

.border-l-slate-50 {
  border-left-color: var(--slate-50);
}

.border-x-slate-50 {
  border-left-color: var(--slate-50);
  border-right-color: var(--slate-50);
}

.border-y-slate-50 {
  border-top-color: var(--slate-50);
  border-bottom-color: var(--slate-50);
}

.border-t-slate-100 {
  border-top-color: var(--slate-100);
}

.border-r-slate-100 {
  border-right-color: var(--slate-100);
}

.border-b-slate-100 {
  border-bottom-color: var(--slate-100);
}

.border-l-slate-100 {
  border-left-color: var(--slate-100);
}

.border-x-slate-100 {
  border-left-color: var(--slate-100);
  border-right-color: var(--slate-100);
}

.border-y-slate-100 {
  border-top-color: var(--slate-100);
  border-bottom-color: var(--slate-100);
}

.border-t-slate-200 {
  border-top-color: var(--slate-200);
}

.border-r-slate-200 {
  border-right-color: var(--slate-200);
}

.border-b-slate-200 {
  border-bottom-color: var(--slate-200);
}

.border-l-slate-200 {
  border-left-color: var(--slate-200);
}

.border-x-slate-200 {
  border-left-color: var(--slate-200);
  border-right-color: var(--slate-200);
}

.border-y-slate-200 {
  border-top-color: var(--slate-200);
  border-bottom-color: var(--slate-200);
}

.border-t-slate-300 {
  border-top-color: var(--slate-300);
}

.border-r-slate-300 {
  border-right-color: var(--slate-300);
}

.border-b-slate-300 {
  border-bottom-color: var(--slate-300);
}

.border-l-slate-300 {
  border-left-color: var(--slate-300);
}

.border-x-slate-300 {
  border-left-color: var(--slate-300);
  border-right-color: var(--slate-300);
}

.border-y-slate-300 {
  border-top-color: var(--slate-300);
  border-bottom-color: var(--slate-300);
}

.border-t-slate-400 {
  border-top-color: var(--slate-400);
}

.border-r-slate-400 {
  border-right-color: var(--slate-400);
}

.border-b-slate-400 {
  border-bottom-color: var(--slate-400);
}

.border-l-slate-400 {
  border-left-color: var(--slate-400);
}

.border-x-slate-400 {
  border-left-color: var(--slate-400);
  border-right-color: var(--slate-400);
}

.border-y-slate-400 {
  border-top-color: var(--slate-400);
  border-bottom-color: var(--slate-400);
}

.border-t-slate-500 {
  border-top-color: var(--slate-500);
}

.border-r-slate-500 {
  border-right-color: var(--slate-500);
}

.border-b-slate-500 {
  border-bottom-color: var(--slate-500);
}

.border-l-slate-500 {
  border-left-color: var(--slate-500);
}

.border-x-slate-500 {
  border-left-color: var(--slate-500);
  border-right-color: var(--slate-500);
}

.border-y-slate-500 {
  border-top-color: var(--slate-500);
  border-bottom-color: var(--slate-500);
}

.border-t-slate-600 {
  border-top-color: var(--slate-600);
}

.border-r-slate-600 {
  border-right-color: var(--slate-600);
}

.border-b-slate-600 {
  border-bottom-color: var(--slate-600);
}

.border-l-slate-600 {
  border-left-color: var(--slate-600);
}

.border-x-slate-600 {
  border-left-color: var(--slate-600);
  border-right-color: var(--slate-600);
}

.border-y-slate-600 {
  border-top-color: var(--slate-600);
  border-bottom-color: var(--slate-600);
}

.border-t-slate-700 {
  border-top-color: var(--slate-700);
}

.border-r-slate-700 {
  border-right-color: var(--slate-700);
}

.border-b-slate-700 {
  border-bottom-color: var(--slate-700);
}

.border-l-slate-700 {
  border-left-color: var(--slate-700);
}

.border-x-slate-700 {
  border-left-color: var(--slate-700);
  border-right-color: var(--slate-700);
}

.border-y-slate-700 {
  border-top-color: var(--slate-700);
  border-bottom-color: var(--slate-700);
}

.border-t-slate-800 {
  border-top-color: var(--slate-800);
}

.border-r-slate-800 {
  border-right-color: var(--slate-800);
}

.border-b-slate-800 {
  border-bottom-color: var(--slate-800);
}

.border-l-slate-800 {
  border-left-color: var(--slate-800);
}

.border-x-slate-800 {
  border-left-color: var(--slate-800);
  border-right-color: var(--slate-800);
}

.border-y-slate-800 {
  border-top-color: var(--slate-800);
  border-bottom-color: var(--slate-800);
}

.border-t-slate-900 {
  border-top-color: var(--slate-900);
}

.border-r-slate-900 {
  border-right-color: var(--slate-900);
}

.border-b-slate-900 {
  border-bottom-color: var(--slate-900);
}

.border-l-slate-900 {
  border-left-color: var(--slate-900);
}

.border-x-slate-900 {
  border-left-color: var(--slate-900);
  border-right-color: var(--slate-900);
}

.border-y-slate-900 {
  border-top-color: var(--slate-900);
  border-bottom-color: var(--slate-900);
}

.border-t-slate-950 {
  border-top-color: var(--slate-950);
}

.border-r-slate-950 {
  border-right-color: var(--slate-950);
}

.border-b-slate-950 {
  border-bottom-color: var(--slate-950);
}

.border-l-slate-950 {
  border-left-color: var(--slate-950);
}

.border-x-slate-950 {
  border-left-color: var(--slate-950);
  border-right-color: var(--slate-950);
}

.border-y-slate-950 {
  border-top-color: var(--slate-950);
  border-bottom-color: var(--slate-950);
}

.border-t-gray-50 {
  border-top-color: var(--gray-50);
}

.border-r-gray-50 {
  border-right-color: var(--gray-50);
}

.border-b-gray-50 {
  border-bottom-color: var(--gray-50);
}

.border-l-gray-50 {
  border-left-color: var(--gray-50);
}

.border-x-gray-50 {
  border-left-color: var(--gray-50);
  border-right-color: var(--gray-50);
}

.border-y-gray-50 {
  border-top-color: var(--gray-50);
  border-bottom-color: var(--gray-50);
}

.border-t-gray-100 {
  border-top-color: var(--gray-100);
}

.border-r-gray-100 {
  border-right-color: var(--gray-100);
}

.border-b-gray-100 {
  border-bottom-color: var(--gray-100);
}

.border-l-gray-100 {
  border-left-color: var(--gray-100);
}

.border-x-gray-100 {
  border-left-color: var(--gray-100);
  border-right-color: var(--gray-100);
}

.border-y-gray-100 {
  border-top-color: var(--gray-100);
  border-bottom-color: var(--gray-100);
}

.border-t-gray-200 {
  border-top-color: var(--gray-200);
}

.border-r-gray-200 {
  border-right-color: var(--gray-200);
}

.border-b-gray-200 {
  border-bottom-color: var(--gray-200);
}

.border-l-gray-200 {
  border-left-color: var(--gray-200);
}

.border-x-gray-200 {
  border-left-color: var(--gray-200);
  border-right-color: var(--gray-200);
}

.border-y-gray-200 {
  border-top-color: var(--gray-200);
  border-bottom-color: var(--gray-200);
}

.border-t-gray-300 {
  border-top-color: var(--gray-300);
}

.border-r-gray-300 {
  border-right-color: var(--gray-300);
}

.border-b-gray-300 {
  border-bottom-color: var(--gray-300);
}

.border-l-gray-300 {
  border-left-color: var(--gray-300);
}

.border-x-gray-300 {
  border-left-color: var(--gray-300);
  border-right-color: var(--gray-300);
}

.border-y-gray-300 {
  border-top-color: var(--gray-300);
  border-bottom-color: var(--gray-300);
}

.border-t-gray-400 {
  border-top-color: var(--gray-400);
}

.border-r-gray-400 {
  border-right-color: var(--gray-400);
}

.border-b-gray-400 {
  border-bottom-color: var(--gray-400);
}

.border-l-gray-400 {
  border-left-color: var(--gray-400);
}

.border-x-gray-400 {
  border-left-color: var(--gray-400);
  border-right-color: var(--gray-400);
}

.border-y-gray-400 {
  border-top-color: var(--gray-400);
  border-bottom-color: var(--gray-400);
}

.border-t-gray-500 {
  border-top-color: var(--gray-500);
}

.border-r-gray-500 {
  border-right-color: var(--gray-500);
}

.border-b-gray-500 {
  border-bottom-color: var(--gray-500);
}

.border-l-gray-500 {
  border-left-color: var(--gray-500);
}

.border-x-gray-500 {
  border-left-color: var(--gray-500);
  border-right-color: var(--gray-500);
}

.border-y-gray-500 {
  border-top-color: var(--gray-500);
  border-bottom-color: var(--gray-500);
}

.border-t-gray-600 {
  border-top-color: var(--gray-600);
}

.border-r-gray-600 {
  border-right-color: var(--gray-600);
}

.border-b-gray-600 {
  border-bottom-color: var(--gray-600);
}

.border-l-gray-600 {
  border-left-color: var(--gray-600);
}

.border-x-gray-600 {
  border-left-color: var(--gray-600);
  border-right-color: var(--gray-600);
}

.border-y-gray-600 {
  border-top-color: var(--gray-600);
  border-bottom-color: var(--gray-600);
}

.border-t-gray-700 {
  border-top-color: var(--gray-700);
}

.border-r-gray-700 {
  border-right-color: var(--gray-700);
}

.border-b-gray-700 {
  border-bottom-color: var(--gray-700);
}

.border-l-gray-700 {
  border-left-color: var(--gray-700);
}

.border-x-gray-700 {
  border-left-color: var(--gray-700);
  border-right-color: var(--gray-700);
}

.border-y-gray-700 {
  border-top-color: var(--gray-700);
  border-bottom-color: var(--gray-700);
}

.border-t-gray-800 {
  border-top-color: var(--gray-800);
}

.border-r-gray-800 {
  border-right-color: var(--gray-800);
}

.border-b-gray-800 {
  border-bottom-color: var(--gray-800);
}

.border-l-gray-800 {
  border-left-color: var(--gray-800);
}

.border-x-gray-800 {
  border-left-color: var(--gray-800);
  border-right-color: var(--gray-800);
}

.border-y-gray-800 {
  border-top-color: var(--gray-800);
  border-bottom-color: var(--gray-800);
}

.border-t-gray-900 {
  border-top-color: var(--gray-900);
}

.border-r-gray-900 {
  border-right-color: var(--gray-900);
}

.border-b-gray-900 {
  border-bottom-color: var(--gray-900);
}

.border-l-gray-900 {
  border-left-color: var(--gray-900);
}

.border-x-gray-900 {
  border-left-color: var(--gray-900);
  border-right-color: var(--gray-900);
}

.border-y-gray-900 {
  border-top-color: var(--gray-900);
  border-bottom-color: var(--gray-900);
}

.border-t-gray-950 {
  border-top-color: var(--gray-950);
}

.border-r-gray-950 {
  border-right-color: var(--gray-950);
}

.border-b-gray-950 {
  border-bottom-color: var(--gray-950);
}

.border-l-gray-950 {
  border-left-color: var(--gray-950);
}

.border-x-gray-950 {
  border-left-color: var(--gray-950);
  border-right-color: var(--gray-950);
}

.border-y-gray-950 {
  border-top-color: var(--gray-950);
  border-bottom-color: var(--gray-950);
}

.border-t-zinc-50 {
  border-top-color: var(--zinc-50);
}

.border-r-zinc-50 {
  border-right-color: var(--zinc-50);
}

.border-b-zinc-50 {
  border-bottom-color: var(--zinc-50);
}

.border-l-zinc-50 {
  border-left-color: var(--zinc-50);
}

.border-x-zinc-50 {
  border-left-color: var(--zinc-50);
  border-right-color: var(--zinc-50);
}

.border-y-zinc-50 {
  border-top-color: var(--zinc-50);
  border-bottom-color: var(--zinc-50);
}

.border-t-zinc-100 {
  border-top-color: var(--zinc-100);
}

.border-r-zinc-100 {
  border-right-color: var(--zinc-100);
}

.border-b-zinc-100 {
  border-bottom-color: var(--zinc-100);
}

.border-l-zinc-100 {
  border-left-color: var(--zinc-100);
}

.border-x-zinc-100 {
  border-left-color: var(--zinc-100);
  border-right-color: var(--zinc-100);
}

.border-y-zinc-100 {
  border-top-color: var(--zinc-100);
  border-bottom-color: var(--zinc-100);
}

.border-t-zinc-200 {
  border-top-color: var(--zinc-200);
}

.border-r-zinc-200 {
  border-right-color: var(--zinc-200);
}

.border-b-zinc-200 {
  border-bottom-color: var(--zinc-200);
}

.border-l-zinc-200 {
  border-left-color: var(--zinc-200);
}

.border-x-zinc-200 {
  border-left-color: var(--zinc-200);
  border-right-color: var(--zinc-200);
}

.border-y-zinc-200 {
  border-top-color: var(--zinc-200);
  border-bottom-color: var(--zinc-200);
}

.border-t-zinc-300 {
  border-top-color: var(--zinc-300);
}

.border-r-zinc-300 {
  border-right-color: var(--zinc-300);
}

.border-b-zinc-300 {
  border-bottom-color: var(--zinc-300);
}

.border-l-zinc-300 {
  border-left-color: var(--zinc-300);
}

.border-x-zinc-300 {
  border-left-color: var(--zinc-300);
  border-right-color: var(--zinc-300);
}

.border-y-zinc-300 {
  border-top-color: var(--zinc-300);
  border-bottom-color: var(--zinc-300);
}

.border-t-zinc-400 {
  border-top-color: var(--zinc-400);
}

.border-r-zinc-400 {
  border-right-color: var(--zinc-400);
}

.border-b-zinc-400 {
  border-bottom-color: var(--zinc-400);
}

.border-l-zinc-400 {
  border-left-color: var(--zinc-400);
}

.border-x-zinc-400 {
  border-left-color: var(--zinc-400);
  border-right-color: var(--zinc-400);
}

.border-y-zinc-400 {
  border-top-color: var(--zinc-400);
  border-bottom-color: var(--zinc-400);
}

.border-t-zinc-500 {
  border-top-color: var(--zinc-500);
}

.border-r-zinc-500 {
  border-right-color: var(--zinc-500);
}

.border-b-zinc-500 {
  border-bottom-color: var(--zinc-500);
}

.border-l-zinc-500 {
  border-left-color: var(--zinc-500);
}

.border-x-zinc-500 {
  border-left-color: var(--zinc-500);
  border-right-color: var(--zinc-500);
}

.border-y-zinc-500 {
  border-top-color: var(--zinc-500);
  border-bottom-color: var(--zinc-500);
}

.border-t-zinc-600 {
  border-top-color: var(--zinc-600);
}

.border-r-zinc-600 {
  border-right-color: var(--zinc-600);
}

.border-b-zinc-600 {
  border-bottom-color: var(--zinc-600);
}

.border-l-zinc-600 {
  border-left-color: var(--zinc-600);
}

.border-x-zinc-600 {
  border-left-color: var(--zinc-600);
  border-right-color: var(--zinc-600);
}

.border-y-zinc-600 {
  border-top-color: var(--zinc-600);
  border-bottom-color: var(--zinc-600);
}

.border-t-zinc-700 {
  border-top-color: var(--zinc-700);
}

.border-r-zinc-700 {
  border-right-color: var(--zinc-700);
}

.border-b-zinc-700 {
  border-bottom-color: var(--zinc-700);
}

.border-l-zinc-700 {
  border-left-color: var(--zinc-700);
}

.border-x-zinc-700 {
  border-left-color: var(--zinc-700);
  border-right-color: var(--zinc-700);
}

.border-y-zinc-700 {
  border-top-color: var(--zinc-700);
  border-bottom-color: var(--zinc-700);
}

.border-t-zinc-800 {
  border-top-color: var(--zinc-800);
}

.border-r-zinc-800 {
  border-right-color: var(--zinc-800);
}

.border-b-zinc-800 {
  border-bottom-color: var(--zinc-800);
}

.border-l-zinc-800 {
  border-left-color: var(--zinc-800);
}

.border-x-zinc-800 {
  border-left-color: var(--zinc-800);
  border-right-color: var(--zinc-800);
}

.border-y-zinc-800 {
  border-top-color: var(--zinc-800);
  border-bottom-color: var(--zinc-800);
}

.border-t-zinc-900 {
  border-top-color: var(--zinc-900);
}

.border-r-zinc-900 {
  border-right-color: var(--zinc-900);
}

.border-b-zinc-900 {
  border-bottom-color: var(--zinc-900);
}

.border-l-zinc-900 {
  border-left-color: var(--zinc-900);
}

.border-x-zinc-900 {
  border-left-color: var(--zinc-900);
  border-right-color: var(--zinc-900);
}

.border-y-zinc-900 {
  border-top-color: var(--zinc-900);
  border-bottom-color: var(--zinc-900);
}

.border-t-zinc-950 {
  border-top-color: var(--zinc-950);
}

.border-r-zinc-950 {
  border-right-color: var(--zinc-950);
}

.border-b-zinc-950 {
  border-bottom-color: var(--zinc-950);
}

.border-l-zinc-950 {
  border-left-color: var(--zinc-950);
}

.border-x-zinc-950 {
  border-left-color: var(--zinc-950);
  border-right-color: var(--zinc-950);
}

.border-y-zinc-950 {
  border-top-color: var(--zinc-950);
  border-bottom-color: var(--zinc-950);
}

.border-t-neutral-50 {
  border-top-color: var(--neutral-50);
}

.border-r-neutral-50 {
  border-right-color: var(--neutral-50);
}

.border-b-neutral-50 {
  border-bottom-color: var(--neutral-50);
}

.border-l-neutral-50 {
  border-left-color: var(--neutral-50);
}

.border-x-neutral-50 {
  border-left-color: var(--neutral-50);
  border-right-color: var(--neutral-50);
}

.border-y-neutral-50 {
  border-top-color: var(--neutral-50);
  border-bottom-color: var(--neutral-50);
}

.border-t-neutral-100 {
  border-top-color: var(--neutral-100);
}

.border-r-neutral-100 {
  border-right-color: var(--neutral-100);
}

.border-b-neutral-100 {
  border-bottom-color: var(--neutral-100);
}

.border-l-neutral-100 {
  border-left-color: var(--neutral-100);
}

.border-x-neutral-100 {
  border-left-color: var(--neutral-100);
  border-right-color: var(--neutral-100);
}

.border-y-neutral-100 {
  border-top-color: var(--neutral-100);
  border-bottom-color: var(--neutral-100);
}

.border-t-neutral-200 {
  border-top-color: var(--neutral-200);
}

.border-r-neutral-200 {
  border-right-color: var(--neutral-200);
}

.border-b-neutral-200 {
  border-bottom-color: var(--neutral-200);
}

.border-l-neutral-200 {
  border-left-color: var(--neutral-200);
}

.border-x-neutral-200 {
  border-left-color: var(--neutral-200);
  border-right-color: var(--neutral-200);
}

.border-y-neutral-200 {
  border-top-color: var(--neutral-200);
  border-bottom-color: var(--neutral-200);
}

.border-t-neutral-300 {
  border-top-color: var(--neutral-300);
}

.border-r-neutral-300 {
  border-right-color: var(--neutral-300);
}

.border-b-neutral-300 {
  border-bottom-color: var(--neutral-300);
}

.border-l-neutral-300 {
  border-left-color: var(--neutral-300);
}

.border-x-neutral-300 {
  border-left-color: var(--neutral-300);
  border-right-color: var(--neutral-300);
}

.border-y-neutral-300 {
  border-top-color: var(--neutral-300);
  border-bottom-color: var(--neutral-300);
}

.border-t-neutral-400 {
  border-top-color: var(--neutral-400);
}

.border-r-neutral-400 {
  border-right-color: var(--neutral-400);
}

.border-b-neutral-400 {
  border-bottom-color: var(--neutral-400);
}

.border-l-neutral-400 {
  border-left-color: var(--neutral-400);
}

.border-x-neutral-400 {
  border-left-color: var(--neutral-400);
  border-right-color: var(--neutral-400);
}

.border-y-neutral-400 {
  border-top-color: var(--neutral-400);
  border-bottom-color: var(--neutral-400);
}

.border-t-neutral-500 {
  border-top-color: var(--neutral-500);
}

.border-r-neutral-500 {
  border-right-color: var(--neutral-500);
}

.border-b-neutral-500 {
  border-bottom-color: var(--neutral-500);
}

.border-l-neutral-500 {
  border-left-color: var(--neutral-500);
}

.border-x-neutral-500 {
  border-left-color: var(--neutral-500);
  border-right-color: var(--neutral-500);
}

.border-y-neutral-500 {
  border-top-color: var(--neutral-500);
  border-bottom-color: var(--neutral-500);
}

.border-t-neutral-600 {
  border-top-color: var(--neutral-600);
}

.border-r-neutral-600 {
  border-right-color: var(--neutral-600);
}

.border-b-neutral-600 {
  border-bottom-color: var(--neutral-600);
}

.border-l-neutral-600 {
  border-left-color: var(--neutral-600);
}

.border-x-neutral-600 {
  border-left-color: var(--neutral-600);
  border-right-color: var(--neutral-600);
}

.border-y-neutral-600 {
  border-top-color: var(--neutral-600);
  border-bottom-color: var(--neutral-600);
}

.border-t-neutral-700 {
  border-top-color: var(--neutral-700);
}

.border-r-neutral-700 {
  border-right-color: var(--neutral-700);
}

.border-b-neutral-700 {
  border-bottom-color: var(--neutral-700);
}

.border-l-neutral-700 {
  border-left-color: var(--neutral-700);
}

.border-x-neutral-700 {
  border-left-color: var(--neutral-700);
  border-right-color: var(--neutral-700);
}

.border-y-neutral-700 {
  border-top-color: var(--neutral-700);
  border-bottom-color: var(--neutral-700);
}

.border-t-neutral-800 {
  border-top-color: var(--neutral-800);
}

.border-r-neutral-800 {
  border-right-color: var(--neutral-800);
}

.border-b-neutral-800 {
  border-bottom-color: var(--neutral-800);
}

.border-l-neutral-800 {
  border-left-color: var(--neutral-800);
}

.border-x-neutral-800 {
  border-left-color: var(--neutral-800);
  border-right-color: var(--neutral-800);
}

.border-y-neutral-800 {
  border-top-color: var(--neutral-800);
  border-bottom-color: var(--neutral-800);
}

.border-t-neutral-900 {
  border-top-color: var(--neutral-900);
}

.border-r-neutral-900 {
  border-right-color: var(--neutral-900);
}

.border-b-neutral-900 {
  border-bottom-color: var(--neutral-900);
}

.border-l-neutral-900 {
  border-left-color: var(--neutral-900);
}

.border-x-neutral-900 {
  border-left-color: var(--neutral-900);
  border-right-color: var(--neutral-900);
}

.border-y-neutral-900 {
  border-top-color: var(--neutral-900);
  border-bottom-color: var(--neutral-900);
}

.border-t-neutral-950 {
  border-top-color: var(--neutral-950);
}

.border-r-neutral-950 {
  border-right-color: var(--neutral-950);
}

.border-b-neutral-950 {
  border-bottom-color: var(--neutral-950);
}

.border-l-neutral-950 {
  border-left-color: var(--neutral-950);
}

.border-x-neutral-950 {
  border-left-color: var(--neutral-950);
  border-right-color: var(--neutral-950);
}

.border-y-neutral-950 {
  border-top-color: var(--neutral-950);
  border-bottom-color: var(--neutral-950);
}

.border-t-stone-50 {
  border-top-color: var(--stone-50);
}

.border-r-stone-50 {
  border-right-color: var(--stone-50);
}

.border-b-stone-50 {
  border-bottom-color: var(--stone-50);
}

.border-l-stone-50 {
  border-left-color: var(--stone-50);
}

.border-x-stone-50 {
  border-left-color: var(--stone-50);
  border-right-color: var(--stone-50);
}

.border-y-stone-50 {
  border-top-color: var(--stone-50);
  border-bottom-color: var(--stone-50);
}

.border-t-stone-100 {
  border-top-color: var(--stone-100);
}

.border-r-stone-100 {
  border-right-color: var(--stone-100);
}

.border-b-stone-100 {
  border-bottom-color: var(--stone-100);
}

.border-l-stone-100 {
  border-left-color: var(--stone-100);
}

.border-x-stone-100 {
  border-left-color: var(--stone-100);
  border-right-color: var(--stone-100);
}

.border-y-stone-100 {
  border-top-color: var(--stone-100);
  border-bottom-color: var(--stone-100);
}

.border-t-stone-200 {
  border-top-color: var(--stone-200);
}

.border-r-stone-200 {
  border-right-color: var(--stone-200);
}

.border-b-stone-200 {
  border-bottom-color: var(--stone-200);
}

.border-l-stone-200 {
  border-left-color: var(--stone-200);
}

.border-x-stone-200 {
  border-left-color: var(--stone-200);
  border-right-color: var(--stone-200);
}

.border-y-stone-200 {
  border-top-color: var(--stone-200);
  border-bottom-color: var(--stone-200);
}

.border-t-stone-300 {
  border-top-color: var(--stone-300);
}

.border-r-stone-300 {
  border-right-color: var(--stone-300);
}

.border-b-stone-300 {
  border-bottom-color: var(--stone-300);
}

.border-l-stone-300 {
  border-left-color: var(--stone-300);
}

.border-x-stone-300 {
  border-left-color: var(--stone-300);
  border-right-color: var(--stone-300);
}

.border-y-stone-300 {
  border-top-color: var(--stone-300);
  border-bottom-color: var(--stone-300);
}

.border-t-stone-400 {
  border-top-color: var(--stone-400);
}

.border-r-stone-400 {
  border-right-color: var(--stone-400);
}

.border-b-stone-400 {
  border-bottom-color: var(--stone-400);
}

.border-l-stone-400 {
  border-left-color: var(--stone-400);
}

.border-x-stone-400 {
  border-left-color: var(--stone-400);
  border-right-color: var(--stone-400);
}

.border-y-stone-400 {
  border-top-color: var(--stone-400);
  border-bottom-color: var(--stone-400);
}

.border-t-stone-500 {
  border-top-color: var(--stone-500);
}

.border-r-stone-500 {
  border-right-color: var(--stone-500);
}

.border-b-stone-500 {
  border-bottom-color: var(--stone-500);
}

.border-l-stone-500 {
  border-left-color: var(--stone-500);
}

.border-x-stone-500 {
  border-left-color: var(--stone-500);
  border-right-color: var(--stone-500);
}

.border-y-stone-500 {
  border-top-color: var(--stone-500);
  border-bottom-color: var(--stone-500);
}

.border-t-stone-600 {
  border-top-color: var(--stone-600);
}

.border-r-stone-600 {
  border-right-color: var(--stone-600);
}

.border-b-stone-600 {
  border-bottom-color: var(--stone-600);
}

.border-l-stone-600 {
  border-left-color: var(--stone-600);
}

.border-x-stone-600 {
  border-left-color: var(--stone-600);
  border-right-color: var(--stone-600);
}

.border-y-stone-600 {
  border-top-color: var(--stone-600);
  border-bottom-color: var(--stone-600);
}

.border-t-stone-700 {
  border-top-color: var(--stone-700);
}

.border-r-stone-700 {
  border-right-color: var(--stone-700);
}

.border-b-stone-700 {
  border-bottom-color: var(--stone-700);
}

.border-l-stone-700 {
  border-left-color: var(--stone-700);
}

.border-x-stone-700 {
  border-left-color: var(--stone-700);
  border-right-color: var(--stone-700);
}

.border-y-stone-700 {
  border-top-color: var(--stone-700);
  border-bottom-color: var(--stone-700);
}

.border-t-stone-800 {
  border-top-color: var(--stone-800);
}

.border-r-stone-800 {
  border-right-color: var(--stone-800);
}

.border-b-stone-800 {
  border-bottom-color: var(--stone-800);
}

.border-l-stone-800 {
  border-left-color: var(--stone-800);
}

.border-x-stone-800 {
  border-left-color: var(--stone-800);
  border-right-color: var(--stone-800);
}

.border-y-stone-800 {
  border-top-color: var(--stone-800);
  border-bottom-color: var(--stone-800);
}

.border-t-stone-900 {
  border-top-color: var(--stone-900);
}

.border-r-stone-900 {
  border-right-color: var(--stone-900);
}

.border-b-stone-900 {
  border-bottom-color: var(--stone-900);
}

.border-l-stone-900 {
  border-left-color: var(--stone-900);
}

.border-x-stone-900 {
  border-left-color: var(--stone-900);
  border-right-color: var(--stone-900);
}

.border-y-stone-900 {
  border-top-color: var(--stone-900);
  border-bottom-color: var(--stone-900);
}

.border-t-stone-950 {
  border-top-color: var(--stone-950);
}

.border-r-stone-950 {
  border-right-color: var(--stone-950);
}

.border-b-stone-950 {
  border-bottom-color: var(--stone-950);
}

.border-l-stone-950 {
  border-left-color: var(--stone-950);
}

.border-x-stone-950 {
  border-left-color: var(--stone-950);
  border-right-color: var(--stone-950);
}

.border-y-stone-950 {
  border-top-color: var(--stone-950);
  border-bottom-color: var(--stone-950);
}

.border-t-red-50 {
  border-top-color: var(--red-50);
}

.border-r-red-50 {
  border-right-color: var(--red-50);
}

.border-b-red-50 {
  border-bottom-color: var(--red-50);
}

.border-l-red-50 {
  border-left-color: var(--red-50);
}

.border-x-red-50 {
  border-left-color: var(--red-50);
  border-right-color: var(--red-50);
}

.border-y-red-50 {
  border-top-color: var(--red-50);
  border-bottom-color: var(--red-50);
}

.border-t-red-100 {
  border-top-color: var(--red-100);
}

.border-r-red-100 {
  border-right-color: var(--red-100);
}

.border-b-red-100 {
  border-bottom-color: var(--red-100);
}

.border-l-red-100 {
  border-left-color: var(--red-100);
}

.border-x-red-100 {
  border-left-color: var(--red-100);
  border-right-color: var(--red-100);
}

.border-y-red-100 {
  border-top-color: var(--red-100);
  border-bottom-color: var(--red-100);
}

.border-t-red-200 {
  border-top-color: var(--red-200);
}

.border-r-red-200 {
  border-right-color: var(--red-200);
}

.border-b-red-200 {
  border-bottom-color: var(--red-200);
}

.border-l-red-200 {
  border-left-color: var(--red-200);
}

.border-x-red-200 {
  border-left-color: var(--red-200);
  border-right-color: var(--red-200);
}

.border-y-red-200 {
  border-top-color: var(--red-200);
  border-bottom-color: var(--red-200);
}

.border-t-red-300 {
  border-top-color: var(--red-300);
}

.border-r-red-300 {
  border-right-color: var(--red-300);
}

.border-b-red-300 {
  border-bottom-color: var(--red-300);
}

.border-l-red-300 {
  border-left-color: var(--red-300);
}

.border-x-red-300 {
  border-left-color: var(--red-300);
  border-right-color: var(--red-300);
}

.border-y-red-300 {
  border-top-color: var(--red-300);
  border-bottom-color: var(--red-300);
}

.border-t-red-400 {
  border-top-color: var(--red-400);
}

.border-r-red-400 {
  border-right-color: var(--red-400);
}

.border-b-red-400 {
  border-bottom-color: var(--red-400);
}

.border-l-red-400 {
  border-left-color: var(--red-400);
}

.border-x-red-400 {
  border-left-color: var(--red-400);
  border-right-color: var(--red-400);
}

.border-y-red-400 {
  border-top-color: var(--red-400);
  border-bottom-color: var(--red-400);
}

.border-t-red-500 {
  border-top-color: var(--red-500);
}

.border-r-red-500 {
  border-right-color: var(--red-500);
}

.border-b-red-500 {
  border-bottom-color: var(--red-500);
}

.border-l-red-500 {
  border-left-color: var(--red-500);
}

.border-x-red-500 {
  border-left-color: var(--red-500);
  border-right-color: var(--red-500);
}

.border-y-red-500 {
  border-top-color: var(--red-500);
  border-bottom-color: var(--red-500);
}

.border-t-red-600 {
  border-top-color: var(--red-600);
}

.border-r-red-600 {
  border-right-color: var(--red-600);
}

.border-b-red-600 {
  border-bottom-color: var(--red-600);
}

.border-l-red-600 {
  border-left-color: var(--red-600);
}

.border-x-red-600 {
  border-left-color: var(--red-600);
  border-right-color: var(--red-600);
}

.border-y-red-600 {
  border-top-color: var(--red-600);
  border-bottom-color: var(--red-600);
}

.border-t-red-700 {
  border-top-color: var(--red-700);
}

.border-r-red-700 {
  border-right-color: var(--red-700);
}

.border-b-red-700 {
  border-bottom-color: var(--red-700);
}

.border-l-red-700 {
  border-left-color: var(--red-700);
}

.border-x-red-700 {
  border-left-color: var(--red-700);
  border-right-color: var(--red-700);
}

.border-y-red-700 {
  border-top-color: var(--red-700);
  border-bottom-color: var(--red-700);
}

.border-t-red-800 {
  border-top-color: var(--red-800);
}

.border-r-red-800 {
  border-right-color: var(--red-800);
}

.border-b-red-800 {
  border-bottom-color: var(--red-800);
}

.border-l-red-800 {
  border-left-color: var(--red-800);
}

.border-x-red-800 {
  border-left-color: var(--red-800);
  border-right-color: var(--red-800);
}

.border-y-red-800 {
  border-top-color: var(--red-800);
  border-bottom-color: var(--red-800);
}

.border-t-red-900 {
  border-top-color: var(--red-900);
}

.border-r-red-900 {
  border-right-color: var(--red-900);
}

.border-b-red-900 {
  border-bottom-color: var(--red-900);
}

.border-l-red-900 {
  border-left-color: var(--red-900);
}

.border-x-red-900 {
  border-left-color: var(--red-900);
  border-right-color: var(--red-900);
}

.border-y-red-900 {
  border-top-color: var(--red-900);
  border-bottom-color: var(--red-900);
}

.border-t-red-950 {
  border-top-color: var(--red-950);
}

.border-r-red-950 {
  border-right-color: var(--red-950);
}

.border-b-red-950 {
  border-bottom-color: var(--red-950);
}

.border-l-red-950 {
  border-left-color: var(--red-950);
}

.border-x-red-950 {
  border-left-color: var(--red-950);
  border-right-color: var(--red-950);
}

.border-y-red-950 {
  border-top-color: var(--red-950);
  border-bottom-color: var(--red-950);
}

.border-t-orange-50 {
  border-top-color: var(--orange-50);
}

.border-r-orange-50 {
  border-right-color: var(--orange-50);
}

.border-b-orange-50 {
  border-bottom-color: var(--orange-50);
}

.border-l-orange-50 {
  border-left-color: var(--orange-50);
}

.border-x-orange-50 {
  border-left-color: var(--orange-50);
  border-right-color: var(--orange-50);
}

.border-y-orange-50 {
  border-top-color: var(--orange-50);
  border-bottom-color: var(--orange-50);
}

.border-t-orange-100 {
  border-top-color: var(--orange-100);
}

.border-r-orange-100 {
  border-right-color: var(--orange-100);
}

.border-b-orange-100 {
  border-bottom-color: var(--orange-100);
}

.border-l-orange-100 {
  border-left-color: var(--orange-100);
}

.border-x-orange-100 {
  border-left-color: var(--orange-100);
  border-right-color: var(--orange-100);
}

.border-y-orange-100 {
  border-top-color: var(--orange-100);
  border-bottom-color: var(--orange-100);
}

.border-t-orange-200 {
  border-top-color: var(--orange-200);
}

.border-r-orange-200 {
  border-right-color: var(--orange-200);
}

.border-b-orange-200 {
  border-bottom-color: var(--orange-200);
}

.border-l-orange-200 {
  border-left-color: var(--orange-200);
}

.border-x-orange-200 {
  border-left-color: var(--orange-200);
  border-right-color: var(--orange-200);
}

.border-y-orange-200 {
  border-top-color: var(--orange-200);
  border-bottom-color: var(--orange-200);
}

.border-t-orange-300 {
  border-top-color: var(--orange-300);
}

.border-r-orange-300 {
  border-right-color: var(--orange-300);
}

.border-b-orange-300 {
  border-bottom-color: var(--orange-300);
}

.border-l-orange-300 {
  border-left-color: var(--orange-300);
}

.border-x-orange-300 {
  border-left-color: var(--orange-300);
  border-right-color: var(--orange-300);
}

.border-y-orange-300 {
  border-top-color: var(--orange-300);
  border-bottom-color: var(--orange-300);
}

.border-t-orange-400 {
  border-top-color: var(--orange-400);
}

.border-r-orange-400 {
  border-right-color: var(--orange-400);
}

.border-b-orange-400 {
  border-bottom-color: var(--orange-400);
}

.border-l-orange-400 {
  border-left-color: var(--orange-400);
}

.border-x-orange-400 {
  border-left-color: var(--orange-400);
  border-right-color: var(--orange-400);
}

.border-y-orange-400 {
  border-top-color: var(--orange-400);
  border-bottom-color: var(--orange-400);
}

.border-t-orange-500 {
  border-top-color: var(--orange-500);
}

.border-r-orange-500 {
  border-right-color: var(--orange-500);
}

.border-b-orange-500 {
  border-bottom-color: var(--orange-500);
}

.border-l-orange-500 {
  border-left-color: var(--orange-500);
}

.border-x-orange-500 {
  border-left-color: var(--orange-500);
  border-right-color: var(--orange-500);
}

.border-y-orange-500 {
  border-top-color: var(--orange-500);
  border-bottom-color: var(--orange-500);
}

.border-t-orange-600 {
  border-top-color: var(--orange-600);
}

.border-r-orange-600 {
  border-right-color: var(--orange-600);
}

.border-b-orange-600 {
  border-bottom-color: var(--orange-600);
}

.border-l-orange-600 {
  border-left-color: var(--orange-600);
}

.border-x-orange-600 {
  border-left-color: var(--orange-600);
  border-right-color: var(--orange-600);
}

.border-y-orange-600 {
  border-top-color: var(--orange-600);
  border-bottom-color: var(--orange-600);
}

.border-t-orange-700 {
  border-top-color: var(--orange-700);
}

.border-r-orange-700 {
  border-right-color: var(--orange-700);
}

.border-b-orange-700 {
  border-bottom-color: var(--orange-700);
}

.border-l-orange-700 {
  border-left-color: var(--orange-700);
}

.border-x-orange-700 {
  border-left-color: var(--orange-700);
  border-right-color: var(--orange-700);
}

.border-y-orange-700 {
  border-top-color: var(--orange-700);
  border-bottom-color: var(--orange-700);
}

.border-t-orange-800 {
  border-top-color: var(--orange-800);
}

.border-r-orange-800 {
  border-right-color: var(--orange-800);
}

.border-b-orange-800 {
  border-bottom-color: var(--orange-800);
}

.border-l-orange-800 {
  border-left-color: var(--orange-800);
}

.border-x-orange-800 {
  border-left-color: var(--orange-800);
  border-right-color: var(--orange-800);
}

.border-y-orange-800 {
  border-top-color: var(--orange-800);
  border-bottom-color: var(--orange-800);
}

.border-t-orange-900 {
  border-top-color: var(--orange-900);
}

.border-r-orange-900 {
  border-right-color: var(--orange-900);
}

.border-b-orange-900 {
  border-bottom-color: var(--orange-900);
}

.border-l-orange-900 {
  border-left-color: var(--orange-900);
}

.border-x-orange-900 {
  border-left-color: var(--orange-900);
  border-right-color: var(--orange-900);
}

.border-y-orange-900 {
  border-top-color: var(--orange-900);
  border-bottom-color: var(--orange-900);
}

.border-t-orange-950 {
  border-top-color: var(--orange-950);
}

.border-r-orange-950 {
  border-right-color: var(--orange-950);
}

.border-b-orange-950 {
  border-bottom-color: var(--orange-950);
}

.border-l-orange-950 {
  border-left-color: var(--orange-950);
}

.border-x-orange-950 {
  border-left-color: var(--orange-950);
  border-right-color: var(--orange-950);
}

.border-y-orange-950 {
  border-top-color: var(--orange-950);
  border-bottom-color: var(--orange-950);
}

.border-t-amber-50 {
  border-top-color: var(--amber-50);
}

.border-r-amber-50 {
  border-right-color: var(--amber-50);
}

.border-b-amber-50 {
  border-bottom-color: var(--amber-50);
}

.border-l-amber-50 {
  border-left-color: var(--amber-50);
}

.border-x-amber-50 {
  border-left-color: var(--amber-50);
  border-right-color: var(--amber-50);
}

.border-y-amber-50 {
  border-top-color: var(--amber-50);
  border-bottom-color: var(--amber-50);
}

.border-t-amber-100 {
  border-top-color: var(--amber-100);
}

.border-r-amber-100 {
  border-right-color: var(--amber-100);
}

.border-b-amber-100 {
  border-bottom-color: var(--amber-100);
}

.border-l-amber-100 {
  border-left-color: var(--amber-100);
}

.border-x-amber-100 {
  border-left-color: var(--amber-100);
  border-right-color: var(--amber-100);
}

.border-y-amber-100 {
  border-top-color: var(--amber-100);
  border-bottom-color: var(--amber-100);
}

.border-t-amber-200 {
  border-top-color: var(--amber-200);
}

.border-r-amber-200 {
  border-right-color: var(--amber-200);
}

.border-b-amber-200 {
  border-bottom-color: var(--amber-200);
}

.border-l-amber-200 {
  border-left-color: var(--amber-200);
}

.border-x-amber-200 {
  border-left-color: var(--amber-200);
  border-right-color: var(--amber-200);
}

.border-y-amber-200 {
  border-top-color: var(--amber-200);
  border-bottom-color: var(--amber-200);
}

.border-t-amber-300 {
  border-top-color: var(--amber-300);
}

.border-r-amber-300 {
  border-right-color: var(--amber-300);
}

.border-b-amber-300 {
  border-bottom-color: var(--amber-300);
}

.border-l-amber-300 {
  border-left-color: var(--amber-300);
}

.border-x-amber-300 {
  border-left-color: var(--amber-300);
  border-right-color: var(--amber-300);
}

.border-y-amber-300 {
  border-top-color: var(--amber-300);
  border-bottom-color: var(--amber-300);
}

.border-t-amber-400 {
  border-top-color: var(--amber-400);
}

.border-r-amber-400 {
  border-right-color: var(--amber-400);
}

.border-b-amber-400 {
  border-bottom-color: var(--amber-400);
}

.border-l-amber-400 {
  border-left-color: var(--amber-400);
}

.border-x-amber-400 {
  border-left-color: var(--amber-400);
  border-right-color: var(--amber-400);
}

.border-y-amber-400 {
  border-top-color: var(--amber-400);
  border-bottom-color: var(--amber-400);
}

.border-t-amber-500 {
  border-top-color: var(--amber-500);
}

.border-r-amber-500 {
  border-right-color: var(--amber-500);
}

.border-b-amber-500 {
  border-bottom-color: var(--amber-500);
}

.border-l-amber-500 {
  border-left-color: var(--amber-500);
}

.border-x-amber-500 {
  border-left-color: var(--amber-500);
  border-right-color: var(--amber-500);
}

.border-y-amber-500 {
  border-top-color: var(--amber-500);
  border-bottom-color: var(--amber-500);
}

.border-t-amber-600 {
  border-top-color: var(--amber-600);
}

.border-r-amber-600 {
  border-right-color: var(--amber-600);
}

.border-b-amber-600 {
  border-bottom-color: var(--amber-600);
}

.border-l-amber-600 {
  border-left-color: var(--amber-600);
}

.border-x-amber-600 {
  border-left-color: var(--amber-600);
  border-right-color: var(--amber-600);
}

.border-y-amber-600 {
  border-top-color: var(--amber-600);
  border-bottom-color: var(--amber-600);
}

.border-t-amber-700 {
  border-top-color: var(--amber-700);
}

.border-r-amber-700 {
  border-right-color: var(--amber-700);
}

.border-b-amber-700 {
  border-bottom-color: var(--amber-700);
}

.border-l-amber-700 {
  border-left-color: var(--amber-700);
}

.border-x-amber-700 {
  border-left-color: var(--amber-700);
  border-right-color: var(--amber-700);
}

.border-y-amber-700 {
  border-top-color: var(--amber-700);
  border-bottom-color: var(--amber-700);
}

.border-t-amber-800 {
  border-top-color: var(--amber-800);
}

.border-r-amber-800 {
  border-right-color: var(--amber-800);
}

.border-b-amber-800 {
  border-bottom-color: var(--amber-800);
}

.border-l-amber-800 {
  border-left-color: var(--amber-800);
}

.border-x-amber-800 {
  border-left-color: var(--amber-800);
  border-right-color: var(--amber-800);
}

.border-y-amber-800 {
  border-top-color: var(--amber-800);
  border-bottom-color: var(--amber-800);
}

.border-t-amber-900 {
  border-top-color: var(--amber-900);
}

.border-r-amber-900 {
  border-right-color: var(--amber-900);
}

.border-b-amber-900 {
  border-bottom-color: var(--amber-900);
}

.border-l-amber-900 {
  border-left-color: var(--amber-900);
}

.border-x-amber-900 {
  border-left-color: var(--amber-900);
  border-right-color: var(--amber-900);
}

.border-y-amber-900 {
  border-top-color: var(--amber-900);
  border-bottom-color: var(--amber-900);
}

.border-t-amber-950 {
  border-top-color: var(--amber-950);
}

.border-r-amber-950 {
  border-right-color: var(--amber-950);
}

.border-b-amber-950 {
  border-bottom-color: var(--amber-950);
}

.border-l-amber-950 {
  border-left-color: var(--amber-950);
}

.border-x-amber-950 {
  border-left-color: var(--amber-950);
  border-right-color: var(--amber-950);
}

.border-y-amber-950 {
  border-top-color: var(--amber-950);
  border-bottom-color: var(--amber-950);
}

.border-t-yellow-50 {
  border-top-color: var(--yellow-50);
}

.border-r-yellow-50 {
  border-right-color: var(--yellow-50);
}

.border-b-yellow-50 {
  border-bottom-color: var(--yellow-50);
}

.border-l-yellow-50 {
  border-left-color: var(--yellow-50);
}

.border-x-yellow-50 {
  border-left-color: var(--yellow-50);
  border-right-color: var(--yellow-50);
}

.border-y-yellow-50 {
  border-top-color: var(--yellow-50);
  border-bottom-color: var(--yellow-50);
}

.border-t-yellow-100 {
  border-top-color: var(--yellow-100);
}

.border-r-yellow-100 {
  border-right-color: var(--yellow-100);
}

.border-b-yellow-100 {
  border-bottom-color: var(--yellow-100);
}

.border-l-yellow-100 {
  border-left-color: var(--yellow-100);
}

.border-x-yellow-100 {
  border-left-color: var(--yellow-100);
  border-right-color: var(--yellow-100);
}

.border-y-yellow-100 {
  border-top-color: var(--yellow-100);
  border-bottom-color: var(--yellow-100);
}

.border-t-yellow-200 {
  border-top-color: var(--yellow-200);
}

.border-r-yellow-200 {
  border-right-color: var(--yellow-200);
}

.border-b-yellow-200 {
  border-bottom-color: var(--yellow-200);
}

.border-l-yellow-200 {
  border-left-color: var(--yellow-200);
}

.border-x-yellow-200 {
  border-left-color: var(--yellow-200);
  border-right-color: var(--yellow-200);
}

.border-y-yellow-200 {
  border-top-color: var(--yellow-200);
  border-bottom-color: var(--yellow-200);
}

.border-t-yellow-300 {
  border-top-color: var(--yellow-300);
}

.border-r-yellow-300 {
  border-right-color: var(--yellow-300);
}

.border-b-yellow-300 {
  border-bottom-color: var(--yellow-300);
}

.border-l-yellow-300 {
  border-left-color: var(--yellow-300);
}

.border-x-yellow-300 {
  border-left-color: var(--yellow-300);
  border-right-color: var(--yellow-300);
}

.border-y-yellow-300 {
  border-top-color: var(--yellow-300);
  border-bottom-color: var(--yellow-300);
}

.border-t-yellow-400 {
  border-top-color: var(--yellow-400);
}

.border-r-yellow-400 {
  border-right-color: var(--yellow-400);
}

.border-b-yellow-400 {
  border-bottom-color: var(--yellow-400);
}

.border-l-yellow-400 {
  border-left-color: var(--yellow-400);
}

.border-x-yellow-400 {
  border-left-color: var(--yellow-400);
  border-right-color: var(--yellow-400);
}

.border-y-yellow-400 {
  border-top-color: var(--yellow-400);
  border-bottom-color: var(--yellow-400);
}

.border-t-yellow-500 {
  border-top-color: var(--yellow-500);
}

.border-r-yellow-500 {
  border-right-color: var(--yellow-500);
}

.border-b-yellow-500 {
  border-bottom-color: var(--yellow-500);
}

.border-l-yellow-500 {
  border-left-color: var(--yellow-500);
}

.border-x-yellow-500 {
  border-left-color: var(--yellow-500);
  border-right-color: var(--yellow-500);
}

.border-y-yellow-500 {
  border-top-color: var(--yellow-500);
  border-bottom-color: var(--yellow-500);
}

.border-t-yellow-600 {
  border-top-color: var(--yellow-600);
}

.border-r-yellow-600 {
  border-right-color: var(--yellow-600);
}

.border-b-yellow-600 {
  border-bottom-color: var(--yellow-600);
}

.border-l-yellow-600 {
  border-left-color: var(--yellow-600);
}

.border-x-yellow-600 {
  border-left-color: var(--yellow-600);
  border-right-color: var(--yellow-600);
}

.border-y-yellow-600 {
  border-top-color: var(--yellow-600);
  border-bottom-color: var(--yellow-600);
}

.border-t-yellow-700 {
  border-top-color: var(--yellow-700);
}

.border-r-yellow-700 {
  border-right-color: var(--yellow-700);
}

.border-b-yellow-700 {
  border-bottom-color: var(--yellow-700);
}

.border-l-yellow-700 {
  border-left-color: var(--yellow-700);
}

.border-x-yellow-700 {
  border-left-color: var(--yellow-700);
  border-right-color: var(--yellow-700);
}

.border-y-yellow-700 {
  border-top-color: var(--yellow-700);
  border-bottom-color: var(--yellow-700);
}

.border-t-yellow-800 {
  border-top-color: var(--yellow-800);
}

.border-r-yellow-800 {
  border-right-color: var(--yellow-800);
}

.border-b-yellow-800 {
  border-bottom-color: var(--yellow-800);
}

.border-l-yellow-800 {
  border-left-color: var(--yellow-800);
}

.border-x-yellow-800 {
  border-left-color: var(--yellow-800);
  border-right-color: var(--yellow-800);
}

.border-y-yellow-800 {
  border-top-color: var(--yellow-800);
  border-bottom-color: var(--yellow-800);
}

.border-t-yellow-900 {
  border-top-color: var(--yellow-900);
}

.border-r-yellow-900 {
  border-right-color: var(--yellow-900);
}

.border-b-yellow-900 {
  border-bottom-color: var(--yellow-900);
}

.border-l-yellow-900 {
  border-left-color: var(--yellow-900);
}

.border-x-yellow-900 {
  border-left-color: var(--yellow-900);
  border-right-color: var(--yellow-900);
}

.border-y-yellow-900 {
  border-top-color: var(--yellow-900);
  border-bottom-color: var(--yellow-900);
}

.border-t-yellow-950 {
  border-top-color: var(--yellow-950);
}

.border-r-yellow-950 {
  border-right-color: var(--yellow-950);
}

.border-b-yellow-950 {
  border-bottom-color: var(--yellow-950);
}

.border-l-yellow-950 {
  border-left-color: var(--yellow-950);
}

.border-x-yellow-950 {
  border-left-color: var(--yellow-950);
  border-right-color: var(--yellow-950);
}

.border-y-yellow-950 {
  border-top-color: var(--yellow-950);
  border-bottom-color: var(--yellow-950);
}

.border-t-lime-50 {
  border-top-color: var(--lime-50);
}

.border-r-lime-50 {
  border-right-color: var(--lime-50);
}

.border-b-lime-50 {
  border-bottom-color: var(--lime-50);
}

.border-l-lime-50 {
  border-left-color: var(--lime-50);
}

.border-x-lime-50 {
  border-left-color: var(--lime-50);
  border-right-color: var(--lime-50);
}

.border-y-lime-50 {
  border-top-color: var(--lime-50);
  border-bottom-color: var(--lime-50);
}

.border-t-lime-100 {
  border-top-color: var(--lime-100);
}

.border-r-lime-100 {
  border-right-color: var(--lime-100);
}

.border-b-lime-100 {
  border-bottom-color: var(--lime-100);
}

.border-l-lime-100 {
  border-left-color: var(--lime-100);
}

.border-x-lime-100 {
  border-left-color: var(--lime-100);
  border-right-color: var(--lime-100);
}

.border-y-lime-100 {
  border-top-color: var(--lime-100);
  border-bottom-color: var(--lime-100);
}

.border-t-lime-200 {
  border-top-color: var(--lime-200);
}

.border-r-lime-200 {
  border-right-color: var(--lime-200);
}

.border-b-lime-200 {
  border-bottom-color: var(--lime-200);
}

.border-l-lime-200 {
  border-left-color: var(--lime-200);
}

.border-x-lime-200 {
  border-left-color: var(--lime-200);
  border-right-color: var(--lime-200);
}

.border-y-lime-200 {
  border-top-color: var(--lime-200);
  border-bottom-color: var(--lime-200);
}

.border-t-lime-300 {
  border-top-color: var(--lime-300);
}

.border-r-lime-300 {
  border-right-color: var(--lime-300);
}

.border-b-lime-300 {
  border-bottom-color: var(--lime-300);
}

.border-l-lime-300 {
  border-left-color: var(--lime-300);
}

.border-x-lime-300 {
  border-left-color: var(--lime-300);
  border-right-color: var(--lime-300);
}

.border-y-lime-300 {
  border-top-color: var(--lime-300);
  border-bottom-color: var(--lime-300);
}

.border-t-lime-400 {
  border-top-color: var(--lime-400);
}

.border-r-lime-400 {
  border-right-color: var(--lime-400);
}

.border-b-lime-400 {
  border-bottom-color: var(--lime-400);
}

.border-l-lime-400 {
  border-left-color: var(--lime-400);
}

.border-x-lime-400 {
  border-left-color: var(--lime-400);
  border-right-color: var(--lime-400);
}

.border-y-lime-400 {
  border-top-color: var(--lime-400);
  border-bottom-color: var(--lime-400);
}

.border-t-lime-500 {
  border-top-color: var(--lime-500);
}

.border-r-lime-500 {
  border-right-color: var(--lime-500);
}

.border-b-lime-500 {
  border-bottom-color: var(--lime-500);
}

.border-l-lime-500 {
  border-left-color: var(--lime-500);
}

.border-x-lime-500 {
  border-left-color: var(--lime-500);
  border-right-color: var(--lime-500);
}

.border-y-lime-500 {
  border-top-color: var(--lime-500);
  border-bottom-color: var(--lime-500);
}

.border-t-lime-600 {
  border-top-color: var(--lime-600);
}

.border-r-lime-600 {
  border-right-color: var(--lime-600);
}

.border-b-lime-600 {
  border-bottom-color: var(--lime-600);
}

.border-l-lime-600 {
  border-left-color: var(--lime-600);
}

.border-x-lime-600 {
  border-left-color: var(--lime-600);
  border-right-color: var(--lime-600);
}

.border-y-lime-600 {
  border-top-color: var(--lime-600);
  border-bottom-color: var(--lime-600);
}

.border-t-lime-700 {
  border-top-color: var(--lime-700);
}

.border-r-lime-700 {
  border-right-color: var(--lime-700);
}

.border-b-lime-700 {
  border-bottom-color: var(--lime-700);
}

.border-l-lime-700 {
  border-left-color: var(--lime-700);
}

.border-x-lime-700 {
  border-left-color: var(--lime-700);
  border-right-color: var(--lime-700);
}

.border-y-lime-700 {
  border-top-color: var(--lime-700);
  border-bottom-color: var(--lime-700);
}

.border-t-lime-800 {
  border-top-color: var(--lime-800);
}

.border-r-lime-800 {
  border-right-color: var(--lime-800);
}

.border-b-lime-800 {
  border-bottom-color: var(--lime-800);
}

.border-l-lime-800 {
  border-left-color: var(--lime-800);
}

.border-x-lime-800 {
  border-left-color: var(--lime-800);
  border-right-color: var(--lime-800);
}

.border-y-lime-800 {
  border-top-color: var(--lime-800);
  border-bottom-color: var(--lime-800);
}

.border-t-lime-900 {
  border-top-color: var(--lime-900);
}

.border-r-lime-900 {
  border-right-color: var(--lime-900);
}

.border-b-lime-900 {
  border-bottom-color: var(--lime-900);
}

.border-l-lime-900 {
  border-left-color: var(--lime-900);
}

.border-x-lime-900 {
  border-left-color: var(--lime-900);
  border-right-color: var(--lime-900);
}

.border-y-lime-900 {
  border-top-color: var(--lime-900);
  border-bottom-color: var(--lime-900);
}

.border-t-lime-950 {
  border-top-color: var(--lime-950);
}

.border-r-lime-950 {
  border-right-color: var(--lime-950);
}

.border-b-lime-950 {
  border-bottom-color: var(--lime-950);
}

.border-l-lime-950 {
  border-left-color: var(--lime-950);
}

.border-x-lime-950 {
  border-left-color: var(--lime-950);
  border-right-color: var(--lime-950);
}

.border-y-lime-950 {
  border-top-color: var(--lime-950);
  border-bottom-color: var(--lime-950);
}

.border-t-green-50 {
  border-top-color: var(--green-50);
}

.border-r-green-50 {
  border-right-color: var(--green-50);
}

.border-b-green-50 {
  border-bottom-color: var(--green-50);
}

.border-l-green-50 {
  border-left-color: var(--green-50);
}

.border-x-green-50 {
  border-left-color: var(--green-50);
  border-right-color: var(--green-50);
}

.border-y-green-50 {
  border-top-color: var(--green-50);
  border-bottom-color: var(--green-50);
}

.border-t-green-100 {
  border-top-color: var(--green-100);
}

.border-r-green-100 {
  border-right-color: var(--green-100);
}

.border-b-green-100 {
  border-bottom-color: var(--green-100);
}

.border-l-green-100 {
  border-left-color: var(--green-100);
}

.border-x-green-100 {
  border-left-color: var(--green-100);
  border-right-color: var(--green-100);
}

.border-y-green-100 {
  border-top-color: var(--green-100);
  border-bottom-color: var(--green-100);
}

.border-t-green-200 {
  border-top-color: var(--green-200);
}

.border-r-green-200 {
  border-right-color: var(--green-200);
}

.border-b-green-200 {
  border-bottom-color: var(--green-200);
}

.border-l-green-200 {
  border-left-color: var(--green-200);
}

.border-x-green-200 {
  border-left-color: var(--green-200);
  border-right-color: var(--green-200);
}

.border-y-green-200 {
  border-top-color: var(--green-200);
  border-bottom-color: var(--green-200);
}

.border-t-green-300 {
  border-top-color: var(--green-300);
}

.border-r-green-300 {
  border-right-color: var(--green-300);
}

.border-b-green-300 {
  border-bottom-color: var(--green-300);
}

.border-l-green-300 {
  border-left-color: var(--green-300);
}

.border-x-green-300 {
  border-left-color: var(--green-300);
  border-right-color: var(--green-300);
}

.border-y-green-300 {
  border-top-color: var(--green-300);
  border-bottom-color: var(--green-300);
}

.border-t-green-400 {
  border-top-color: var(--green-400);
}

.border-r-green-400 {
  border-right-color: var(--green-400);
}

.border-b-green-400 {
  border-bottom-color: var(--green-400);
}

.border-l-green-400 {
  border-left-color: var(--green-400);
}

.border-x-green-400 {
  border-left-color: var(--green-400);
  border-right-color: var(--green-400);
}

.border-y-green-400 {
  border-top-color: var(--green-400);
  border-bottom-color: var(--green-400);
}

.border-t-green-500 {
  border-top-color: var(--green-500);
}

.border-r-green-500 {
  border-right-color: var(--green-500);
}

.border-b-green-500 {
  border-bottom-color: var(--green-500);
}

.border-l-green-500 {
  border-left-color: var(--green-500);
}

.border-x-green-500 {
  border-left-color: var(--green-500);
  border-right-color: var(--green-500);
}

.border-y-green-500 {
  border-top-color: var(--green-500);
  border-bottom-color: var(--green-500);
}

.border-t-green-600 {
  border-top-color: var(--green-600);
}

.border-r-green-600 {
  border-right-color: var(--green-600);
}

.border-b-green-600 {
  border-bottom-color: var(--green-600);
}

.border-l-green-600 {
  border-left-color: var(--green-600);
}

.border-x-green-600 {
  border-left-color: var(--green-600);
  border-right-color: var(--green-600);
}

.border-y-green-600 {
  border-top-color: var(--green-600);
  border-bottom-color: var(--green-600);
}

.border-t-green-700 {
  border-top-color: var(--green-700);
}

.border-r-green-700 {
  border-right-color: var(--green-700);
}

.border-b-green-700 {
  border-bottom-color: var(--green-700);
}

.border-l-green-700 {
  border-left-color: var(--green-700);
}

.border-x-green-700 {
  border-left-color: var(--green-700);
  border-right-color: var(--green-700);
}

.border-y-green-700 {
  border-top-color: var(--green-700);
  border-bottom-color: var(--green-700);
}

.border-t-green-800 {
  border-top-color: var(--green-800);
}

.border-r-green-800 {
  border-right-color: var(--green-800);
}

.border-b-green-800 {
  border-bottom-color: var(--green-800);
}

.border-l-green-800 {
  border-left-color: var(--green-800);
}

.border-x-green-800 {
  border-left-color: var(--green-800);
  border-right-color: var(--green-800);
}

.border-y-green-800 {
  border-top-color: var(--green-800);
  border-bottom-color: var(--green-800);
}

.border-t-green-900 {
  border-top-color: var(--green-900);
}

.border-r-green-900 {
  border-right-color: var(--green-900);
}

.border-b-green-900 {
  border-bottom-color: var(--green-900);
}

.border-l-green-900 {
  border-left-color: var(--green-900);
}

.border-x-green-900 {
  border-left-color: var(--green-900);
  border-right-color: var(--green-900);
}

.border-y-green-900 {
  border-top-color: var(--green-900);
  border-bottom-color: var(--green-900);
}

.border-t-green-950 {
  border-top-color: var(--green-950);
}

.border-r-green-950 {
  border-right-color: var(--green-950);
}

.border-b-green-950 {
  border-bottom-color: var(--green-950);
}

.border-l-green-950 {
  border-left-color: var(--green-950);
}

.border-x-green-950 {
  border-left-color: var(--green-950);
  border-right-color: var(--green-950);
}

.border-y-green-950 {
  border-top-color: var(--green-950);
  border-bottom-color: var(--green-950);
}

.border-t-emerald-50 {
  border-top-color: var(--emerald-50);
}

.border-r-emerald-50 {
  border-right-color: var(--emerald-50);
}

.border-b-emerald-50 {
  border-bottom-color: var(--emerald-50);
}

.border-l-emerald-50 {
  border-left-color: var(--emerald-50);
}

.border-x-emerald-50 {
  border-left-color: var(--emerald-50);
  border-right-color: var(--emerald-50);
}

.border-y-emerald-50 {
  border-top-color: var(--emerald-50);
  border-bottom-color: var(--emerald-50);
}

.border-t-emerald-100 {
  border-top-color: var(--emerald-100);
}

.border-r-emerald-100 {
  border-right-color: var(--emerald-100);
}

.border-b-emerald-100 {
  border-bottom-color: var(--emerald-100);
}

.border-l-emerald-100 {
  border-left-color: var(--emerald-100);
}

.border-x-emerald-100 {
  border-left-color: var(--emerald-100);
  border-right-color: var(--emerald-100);
}

.border-y-emerald-100 {
  border-top-color: var(--emerald-100);
  border-bottom-color: var(--emerald-100);
}

.border-t-emerald-200 {
  border-top-color: var(--emerald-200);
}

.border-r-emerald-200 {
  border-right-color: var(--emerald-200);
}

.border-b-emerald-200 {
  border-bottom-color: var(--emerald-200);
}

.border-l-emerald-200 {
  border-left-color: var(--emerald-200);
}

.border-x-emerald-200 {
  border-left-color: var(--emerald-200);
  border-right-color: var(--emerald-200);
}

.border-y-emerald-200 {
  border-top-color: var(--emerald-200);
  border-bottom-color: var(--emerald-200);
}

.border-t-emerald-300 {
  border-top-color: var(--emerald-300);
}

.border-r-emerald-300 {
  border-right-color: var(--emerald-300);
}

.border-b-emerald-300 {
  border-bottom-color: var(--emerald-300);
}

.border-l-emerald-300 {
  border-left-color: var(--emerald-300);
}

.border-x-emerald-300 {
  border-left-color: var(--emerald-300);
  border-right-color: var(--emerald-300);
}

.border-y-emerald-300 {
  border-top-color: var(--emerald-300);
  border-bottom-color: var(--emerald-300);
}

.border-t-emerald-400 {
  border-top-color: var(--emerald-400);
}

.border-r-emerald-400 {
  border-right-color: var(--emerald-400);
}

.border-b-emerald-400 {
  border-bottom-color: var(--emerald-400);
}

.border-l-emerald-400 {
  border-left-color: var(--emerald-400);
}

.border-x-emerald-400 {
  border-left-color: var(--emerald-400);
  border-right-color: var(--emerald-400);
}

.border-y-emerald-400 {
  border-top-color: var(--emerald-400);
  border-bottom-color: var(--emerald-400);
}

.border-t-emerald-500 {
  border-top-color: var(--emerald-500);
}

.border-r-emerald-500 {
  border-right-color: var(--emerald-500);
}

.border-b-emerald-500 {
  border-bottom-color: var(--emerald-500);
}

.border-l-emerald-500 {
  border-left-color: var(--emerald-500);
}

.border-x-emerald-500 {
  border-left-color: var(--emerald-500);
  border-right-color: var(--emerald-500);
}

.border-y-emerald-500 {
  border-top-color: var(--emerald-500);
  border-bottom-color: var(--emerald-500);
}

.border-t-emerald-600 {
  border-top-color: var(--emerald-600);
}

.border-r-emerald-600 {
  border-right-color: var(--emerald-600);
}

.border-b-emerald-600 {
  border-bottom-color: var(--emerald-600);
}

.border-l-emerald-600 {
  border-left-color: var(--emerald-600);
}

.border-x-emerald-600 {
  border-left-color: var(--emerald-600);
  border-right-color: var(--emerald-600);
}

.border-y-emerald-600 {
  border-top-color: var(--emerald-600);
  border-bottom-color: var(--emerald-600);
}

.border-t-emerald-700 {
  border-top-color: var(--emerald-700);
}

.border-r-emerald-700 {
  border-right-color: var(--emerald-700);
}

.border-b-emerald-700 {
  border-bottom-color: var(--emerald-700);
}

.border-l-emerald-700 {
  border-left-color: var(--emerald-700);
}

.border-x-emerald-700 {
  border-left-color: var(--emerald-700);
  border-right-color: var(--emerald-700);
}

.border-y-emerald-700 {
  border-top-color: var(--emerald-700);
  border-bottom-color: var(--emerald-700);
}

.border-t-emerald-800 {
  border-top-color: var(--emerald-800);
}

.border-r-emerald-800 {
  border-right-color: var(--emerald-800);
}

.border-b-emerald-800 {
  border-bottom-color: var(--emerald-800);
}

.border-l-emerald-800 {
  border-left-color: var(--emerald-800);
}

.border-x-emerald-800 {
  border-left-color: var(--emerald-800);
  border-right-color: var(--emerald-800);
}

.border-y-emerald-800 {
  border-top-color: var(--emerald-800);
  border-bottom-color: var(--emerald-800);
}

.border-t-emerald-900 {
  border-top-color: var(--emerald-900);
}

.border-r-emerald-900 {
  border-right-color: var(--emerald-900);
}

.border-b-emerald-900 {
  border-bottom-color: var(--emerald-900);
}

.border-l-emerald-900 {
  border-left-color: var(--emerald-900);
}

.border-x-emerald-900 {
  border-left-color: var(--emerald-900);
  border-right-color: var(--emerald-900);
}

.border-y-emerald-900 {
  border-top-color: var(--emerald-900);
  border-bottom-color: var(--emerald-900);
}

.border-t-emerald-950 {
  border-top-color: var(--emerald-950);
}

.border-r-emerald-950 {
  border-right-color: var(--emerald-950);
}

.border-b-emerald-950 {
  border-bottom-color: var(--emerald-950);
}

.border-l-emerald-950 {
  border-left-color: var(--emerald-950);
}

.border-x-emerald-950 {
  border-left-color: var(--emerald-950);
  border-right-color: var(--emerald-950);
}

.border-y-emerald-950 {
  border-top-color: var(--emerald-950);
  border-bottom-color: var(--emerald-950);
}

.border-t-teal-50 {
  border-top-color: var(--teal-50);
}

.border-r-teal-50 {
  border-right-color: var(--teal-50);
}

.border-b-teal-50 {
  border-bottom-color: var(--teal-50);
}

.border-l-teal-50 {
  border-left-color: var(--teal-50);
}

.border-x-teal-50 {
  border-left-color: var(--teal-50);
  border-right-color: var(--teal-50);
}

.border-y-teal-50 {
  border-top-color: var(--teal-50);
  border-bottom-color: var(--teal-50);
}

.border-t-teal-100 {
  border-top-color: var(--teal-100);
}

.border-r-teal-100 {
  border-right-color: var(--teal-100);
}

.border-b-teal-100 {
  border-bottom-color: var(--teal-100);
}

.border-l-teal-100 {
  border-left-color: var(--teal-100);
}

.border-x-teal-100 {
  border-left-color: var(--teal-100);
  border-right-color: var(--teal-100);
}

.border-y-teal-100 {
  border-top-color: var(--teal-100);
  border-bottom-color: var(--teal-100);
}

.border-t-teal-200 {
  border-top-color: var(--teal-200);
}

.border-r-teal-200 {
  border-right-color: var(--teal-200);
}

.border-b-teal-200 {
  border-bottom-color: var(--teal-200);
}

.border-l-teal-200 {
  border-left-color: var(--teal-200);
}

.border-x-teal-200 {
  border-left-color: var(--teal-200);
  border-right-color: var(--teal-200);
}

.border-y-teal-200 {
  border-top-color: var(--teal-200);
  border-bottom-color: var(--teal-200);
}

.border-t-teal-300 {
  border-top-color: var(--teal-300);
}

.border-r-teal-300 {
  border-right-color: var(--teal-300);
}

.border-b-teal-300 {
  border-bottom-color: var(--teal-300);
}

.border-l-teal-300 {
  border-left-color: var(--teal-300);
}

.border-x-teal-300 {
  border-left-color: var(--teal-300);
  border-right-color: var(--teal-300);
}

.border-y-teal-300 {
  border-top-color: var(--teal-300);
  border-bottom-color: var(--teal-300);
}

.border-t-teal-400 {
  border-top-color: var(--teal-400);
}

.border-r-teal-400 {
  border-right-color: var(--teal-400);
}

.border-b-teal-400 {
  border-bottom-color: var(--teal-400);
}

.border-l-teal-400 {
  border-left-color: var(--teal-400);
}

.border-x-teal-400 {
  border-left-color: var(--teal-400);
  border-right-color: var(--teal-400);
}

.border-y-teal-400 {
  border-top-color: var(--teal-400);
  border-bottom-color: var(--teal-400);
}

.border-t-teal-500 {
  border-top-color: var(--teal-500);
}

.border-r-teal-500 {
  border-right-color: var(--teal-500);
}

.border-b-teal-500 {
  border-bottom-color: var(--teal-500);
}

.border-l-teal-500 {
  border-left-color: var(--teal-500);
}

.border-x-teal-500 {
  border-left-color: var(--teal-500);
  border-right-color: var(--teal-500);
}

.border-y-teal-500 {
  border-top-color: var(--teal-500);
  border-bottom-color: var(--teal-500);
}

.border-t-teal-600 {
  border-top-color: var(--teal-600);
}

.border-r-teal-600 {
  border-right-color: var(--teal-600);
}

.border-b-teal-600 {
  border-bottom-color: var(--teal-600);
}

.border-l-teal-600 {
  border-left-color: var(--teal-600);
}

.border-x-teal-600 {
  border-left-color: var(--teal-600);
  border-right-color: var(--teal-600);
}

.border-y-teal-600 {
  border-top-color: var(--teal-600);
  border-bottom-color: var(--teal-600);
}

.border-t-teal-700 {
  border-top-color: var(--teal-700);
}

.border-r-teal-700 {
  border-right-color: var(--teal-700);
}

.border-b-teal-700 {
  border-bottom-color: var(--teal-700);
}

.border-l-teal-700 {
  border-left-color: var(--teal-700);
}

.border-x-teal-700 {
  border-left-color: var(--teal-700);
  border-right-color: var(--teal-700);
}

.border-y-teal-700 {
  border-top-color: var(--teal-700);
  border-bottom-color: var(--teal-700);
}

.border-t-teal-800 {
  border-top-color: var(--teal-800);
}

.border-r-teal-800 {
  border-right-color: var(--teal-800);
}

.border-b-teal-800 {
  border-bottom-color: var(--teal-800);
}

.border-l-teal-800 {
  border-left-color: var(--teal-800);
}

.border-x-teal-800 {
  border-left-color: var(--teal-800);
  border-right-color: var(--teal-800);
}

.border-y-teal-800 {
  border-top-color: var(--teal-800);
  border-bottom-color: var(--teal-800);
}

.border-t-teal-900 {
  border-top-color: var(--teal-900);
}

.border-r-teal-900 {
  border-right-color: var(--teal-900);
}

.border-b-teal-900 {
  border-bottom-color: var(--teal-900);
}

.border-l-teal-900 {
  border-left-color: var(--teal-900);
}

.border-x-teal-900 {
  border-left-color: var(--teal-900);
  border-right-color: var(--teal-900);
}

.border-y-teal-900 {
  border-top-color: var(--teal-900);
  border-bottom-color: var(--teal-900);
}

.border-t-teal-950 {
  border-top-color: var(--teal-950);
}

.border-r-teal-950 {
  border-right-color: var(--teal-950);
}

.border-b-teal-950 {
  border-bottom-color: var(--teal-950);
}

.border-l-teal-950 {
  border-left-color: var(--teal-950);
}

.border-x-teal-950 {
  border-left-color: var(--teal-950);
  border-right-color: var(--teal-950);
}

.border-y-teal-950 {
  border-top-color: var(--teal-950);
  border-bottom-color: var(--teal-950);
}

.border-t-cyan-50 {
  border-top-color: var(--cyan-50);
}

.border-r-cyan-50 {
  border-right-color: var(--cyan-50);
}

.border-b-cyan-50 {
  border-bottom-color: var(--cyan-50);
}

.border-l-cyan-50 {
  border-left-color: var(--cyan-50);
}

.border-x-cyan-50 {
  border-left-color: var(--cyan-50);
  border-right-color: var(--cyan-50);
}

.border-y-cyan-50 {
  border-top-color: var(--cyan-50);
  border-bottom-color: var(--cyan-50);
}

.border-t-cyan-100 {
  border-top-color: var(--cyan-100);
}

.border-r-cyan-100 {
  border-right-color: var(--cyan-100);
}

.border-b-cyan-100 {
  border-bottom-color: var(--cyan-100);
}

.border-l-cyan-100 {
  border-left-color: var(--cyan-100);
}

.border-x-cyan-100 {
  border-left-color: var(--cyan-100);
  border-right-color: var(--cyan-100);
}

.border-y-cyan-100 {
  border-top-color: var(--cyan-100);
  border-bottom-color: var(--cyan-100);
}

.border-t-cyan-200 {
  border-top-color: var(--cyan-200);
}

.border-r-cyan-200 {
  border-right-color: var(--cyan-200);
}

.border-b-cyan-200 {
  border-bottom-color: var(--cyan-200);
}

.border-l-cyan-200 {
  border-left-color: var(--cyan-200);
}

.border-x-cyan-200 {
  border-left-color: var(--cyan-200);
  border-right-color: var(--cyan-200);
}

.border-y-cyan-200 {
  border-top-color: var(--cyan-200);
  border-bottom-color: var(--cyan-200);
}

.border-t-cyan-300 {
  border-top-color: var(--cyan-300);
}

.border-r-cyan-300 {
  border-right-color: var(--cyan-300);
}

.border-b-cyan-300 {
  border-bottom-color: var(--cyan-300);
}

.border-l-cyan-300 {
  border-left-color: var(--cyan-300);
}

.border-x-cyan-300 {
  border-left-color: var(--cyan-300);
  border-right-color: var(--cyan-300);
}

.border-y-cyan-300 {
  border-top-color: var(--cyan-300);
  border-bottom-color: var(--cyan-300);
}

.border-t-cyan-400 {
  border-top-color: var(--cyan-400);
}

.border-r-cyan-400 {
  border-right-color: var(--cyan-400);
}

.border-b-cyan-400 {
  border-bottom-color: var(--cyan-400);
}

.border-l-cyan-400 {
  border-left-color: var(--cyan-400);
}

.border-x-cyan-400 {
  border-left-color: var(--cyan-400);
  border-right-color: var(--cyan-400);
}

.border-y-cyan-400 {
  border-top-color: var(--cyan-400);
  border-bottom-color: var(--cyan-400);
}

.border-t-cyan-500 {
  border-top-color: var(--cyan-500);
}

.border-r-cyan-500 {
  border-right-color: var(--cyan-500);
}

.border-b-cyan-500 {
  border-bottom-color: var(--cyan-500);
}

.border-l-cyan-500 {
  border-left-color: var(--cyan-500);
}

.border-x-cyan-500 {
  border-left-color: var(--cyan-500);
  border-right-color: var(--cyan-500);
}

.border-y-cyan-500 {
  border-top-color: var(--cyan-500);
  border-bottom-color: var(--cyan-500);
}

.border-t-cyan-600 {
  border-top-color: var(--cyan-600);
}

.border-r-cyan-600 {
  border-right-color: var(--cyan-600);
}

.border-b-cyan-600 {
  border-bottom-color: var(--cyan-600);
}

.border-l-cyan-600 {
  border-left-color: var(--cyan-600);
}

.border-x-cyan-600 {
  border-left-color: var(--cyan-600);
  border-right-color: var(--cyan-600);
}

.border-y-cyan-600 {
  border-top-color: var(--cyan-600);
  border-bottom-color: var(--cyan-600);
}

.border-t-cyan-700 {
  border-top-color: var(--cyan-700);
}

.border-r-cyan-700 {
  border-right-color: var(--cyan-700);
}

.border-b-cyan-700 {
  border-bottom-color: var(--cyan-700);
}

.border-l-cyan-700 {
  border-left-color: var(--cyan-700);
}

.border-x-cyan-700 {
  border-left-color: var(--cyan-700);
  border-right-color: var(--cyan-700);
}

.border-y-cyan-700 {
  border-top-color: var(--cyan-700);
  border-bottom-color: var(--cyan-700);
}

.border-t-cyan-800 {
  border-top-color: var(--cyan-800);
}

.border-r-cyan-800 {
  border-right-color: var(--cyan-800);
}

.border-b-cyan-800 {
  border-bottom-color: var(--cyan-800);
}

.border-l-cyan-800 {
  border-left-color: var(--cyan-800);
}

.border-x-cyan-800 {
  border-left-color: var(--cyan-800);
  border-right-color: var(--cyan-800);
}

.border-y-cyan-800 {
  border-top-color: var(--cyan-800);
  border-bottom-color: var(--cyan-800);
}

.border-t-cyan-900 {
  border-top-color: var(--cyan-900);
}

.border-r-cyan-900 {
  border-right-color: var(--cyan-900);
}

.border-b-cyan-900 {
  border-bottom-color: var(--cyan-900);
}

.border-l-cyan-900 {
  border-left-color: var(--cyan-900);
}

.border-x-cyan-900 {
  border-left-color: var(--cyan-900);
  border-right-color: var(--cyan-900);
}

.border-y-cyan-900 {
  border-top-color: var(--cyan-900);
  border-bottom-color: var(--cyan-900);
}

.border-t-cyan-950 {
  border-top-color: var(--cyan-950);
}

.border-r-cyan-950 {
  border-right-color: var(--cyan-950);
}

.border-b-cyan-950 {
  border-bottom-color: var(--cyan-950);
}

.border-l-cyan-950 {
  border-left-color: var(--cyan-950);
}

.border-x-cyan-950 {
  border-left-color: var(--cyan-950);
  border-right-color: var(--cyan-950);
}

.border-y-cyan-950 {
  border-top-color: var(--cyan-950);
  border-bottom-color: var(--cyan-950);
}

.border-t-sky-50 {
  border-top-color: var(--sky-50);
}

.border-r-sky-50 {
  border-right-color: var(--sky-50);
}

.border-b-sky-50 {
  border-bottom-color: var(--sky-50);
}

.border-l-sky-50 {
  border-left-color: var(--sky-50);
}

.border-x-sky-50 {
  border-left-color: var(--sky-50);
  border-right-color: var(--sky-50);
}

.border-y-sky-50 {
  border-top-color: var(--sky-50);
  border-bottom-color: var(--sky-50);
}

.border-t-sky-100 {
  border-top-color: var(--sky-100);
}

.border-r-sky-100 {
  border-right-color: var(--sky-100);
}

.border-b-sky-100 {
  border-bottom-color: var(--sky-100);
}

.border-l-sky-100 {
  border-left-color: var(--sky-100);
}

.border-x-sky-100 {
  border-left-color: var(--sky-100);
  border-right-color: var(--sky-100);
}

.border-y-sky-100 {
  border-top-color: var(--sky-100);
  border-bottom-color: var(--sky-100);
}

.border-t-sky-200 {
  border-top-color: var(--sky-200);
}

.border-r-sky-200 {
  border-right-color: var(--sky-200);
}

.border-b-sky-200 {
  border-bottom-color: var(--sky-200);
}

.border-l-sky-200 {
  border-left-color: var(--sky-200);
}

.border-x-sky-200 {
  border-left-color: var(--sky-200);
  border-right-color: var(--sky-200);
}

.border-y-sky-200 {
  border-top-color: var(--sky-200);
  border-bottom-color: var(--sky-200);
}

.border-t-sky-300 {
  border-top-color: var(--sky-300);
}

.border-r-sky-300 {
  border-right-color: var(--sky-300);
}

.border-b-sky-300 {
  border-bottom-color: var(--sky-300);
}

.border-l-sky-300 {
  border-left-color: var(--sky-300);
}

.border-x-sky-300 {
  border-left-color: var(--sky-300);
  border-right-color: var(--sky-300);
}

.border-y-sky-300 {
  border-top-color: var(--sky-300);
  border-bottom-color: var(--sky-300);
}

.border-t-sky-400 {
  border-top-color: var(--sky-400);
}

.border-r-sky-400 {
  border-right-color: var(--sky-400);
}

.border-b-sky-400 {
  border-bottom-color: var(--sky-400);
}

.border-l-sky-400 {
  border-left-color: var(--sky-400);
}

.border-x-sky-400 {
  border-left-color: var(--sky-400);
  border-right-color: var(--sky-400);
}

.border-y-sky-400 {
  border-top-color: var(--sky-400);
  border-bottom-color: var(--sky-400);
}

.border-t-sky-500 {
  border-top-color: var(--sky-500);
}

.border-r-sky-500 {
  border-right-color: var(--sky-500);
}

.border-b-sky-500 {
  border-bottom-color: var(--sky-500);
}

.border-l-sky-500 {
  border-left-color: var(--sky-500);
}

.border-x-sky-500 {
  border-left-color: var(--sky-500);
  border-right-color: var(--sky-500);
}

.border-y-sky-500 {
  border-top-color: var(--sky-500);
  border-bottom-color: var(--sky-500);
}

.border-t-sky-600 {
  border-top-color: var(--sky-600);
}

.border-r-sky-600 {
  border-right-color: var(--sky-600);
}

.border-b-sky-600 {
  border-bottom-color: var(--sky-600);
}

.border-l-sky-600 {
  border-left-color: var(--sky-600);
}

.border-x-sky-600 {
  border-left-color: var(--sky-600);
  border-right-color: var(--sky-600);
}

.border-y-sky-600 {
  border-top-color: var(--sky-600);
  border-bottom-color: var(--sky-600);
}

.border-t-sky-700 {
  border-top-color: var(--sky-700);
}

.border-r-sky-700 {
  border-right-color: var(--sky-700);
}

.border-b-sky-700 {
  border-bottom-color: var(--sky-700);
}

.border-l-sky-700 {
  border-left-color: var(--sky-700);
}

.border-x-sky-700 {
  border-left-color: var(--sky-700);
  border-right-color: var(--sky-700);
}

.border-y-sky-700 {
  border-top-color: var(--sky-700);
  border-bottom-color: var(--sky-700);
}

.border-t-sky-800 {
  border-top-color: var(--sky-800);
}

.border-r-sky-800 {
  border-right-color: var(--sky-800);
}

.border-b-sky-800 {
  border-bottom-color: var(--sky-800);
}

.border-l-sky-800 {
  border-left-color: var(--sky-800);
}

.border-x-sky-800 {
  border-left-color: var(--sky-800);
  border-right-color: var(--sky-800);
}

.border-y-sky-800 {
  border-top-color: var(--sky-800);
  border-bottom-color: var(--sky-800);
}

.border-t-sky-900 {
  border-top-color: var(--sky-900);
}

.border-r-sky-900 {
  border-right-color: var(--sky-900);
}

.border-b-sky-900 {
  border-bottom-color: var(--sky-900);
}

.border-l-sky-900 {
  border-left-color: var(--sky-900);
}

.border-x-sky-900 {
  border-left-color: var(--sky-900);
  border-right-color: var(--sky-900);
}

.border-y-sky-900 {
  border-top-color: var(--sky-900);
  border-bottom-color: var(--sky-900);
}

.border-t-sky-950 {
  border-top-color: var(--sky-950);
}

.border-r-sky-950 {
  border-right-color: var(--sky-950);
}

.border-b-sky-950 {
  border-bottom-color: var(--sky-950);
}

.border-l-sky-950 {
  border-left-color: var(--sky-950);
}

.border-x-sky-950 {
  border-left-color: var(--sky-950);
  border-right-color: var(--sky-950);
}

.border-y-sky-950 {
  border-top-color: var(--sky-950);
  border-bottom-color: var(--sky-950);
}

.border-t-blue-50 {
  border-top-color: var(--blue-50);
}

.border-r-blue-50 {
  border-right-color: var(--blue-50);
}

.border-b-blue-50 {
  border-bottom-color: var(--blue-50);
}

.border-l-blue-50 {
  border-left-color: var(--blue-50);
}

.border-x-blue-50 {
  border-left-color: var(--blue-50);
  border-right-color: var(--blue-50);
}

.border-y-blue-50 {
  border-top-color: var(--blue-50);
  border-bottom-color: var(--blue-50);
}

.border-t-blue-100 {
  border-top-color: var(--blue-100);
}

.border-r-blue-100 {
  border-right-color: var(--blue-100);
}

.border-b-blue-100 {
  border-bottom-color: var(--blue-100);
}

.border-l-blue-100 {
  border-left-color: var(--blue-100);
}

.border-x-blue-100 {
  border-left-color: var(--blue-100);
  border-right-color: var(--blue-100);
}

.border-y-blue-100 {
  border-top-color: var(--blue-100);
  border-bottom-color: var(--blue-100);
}

.border-t-blue-200 {
  border-top-color: var(--blue-200);
}

.border-r-blue-200 {
  border-right-color: var(--blue-200);
}

.border-b-blue-200 {
  border-bottom-color: var(--blue-200);
}

.border-l-blue-200 {
  border-left-color: var(--blue-200);
}

.border-x-blue-200 {
  border-left-color: var(--blue-200);
  border-right-color: var(--blue-200);
}

.border-y-blue-200 {
  border-top-color: var(--blue-200);
  border-bottom-color: var(--blue-200);
}

.border-t-blue-300 {
  border-top-color: var(--blue-300);
}

.border-r-blue-300 {
  border-right-color: var(--blue-300);
}

.border-b-blue-300 {
  border-bottom-color: var(--blue-300);
}

.border-l-blue-300 {
  border-left-color: var(--blue-300);
}

.border-x-blue-300 {
  border-left-color: var(--blue-300);
  border-right-color: var(--blue-300);
}

.border-y-blue-300 {
  border-top-color: var(--blue-300);
  border-bottom-color: var(--blue-300);
}

.border-t-blue-400 {
  border-top-color: var(--blue-400);
}

.border-r-blue-400 {
  border-right-color: var(--blue-400);
}

.border-b-blue-400 {
  border-bottom-color: var(--blue-400);
}

.border-l-blue-400 {
  border-left-color: var(--blue-400);
}

.border-x-blue-400 {
  border-left-color: var(--blue-400);
  border-right-color: var(--blue-400);
}

.border-y-blue-400 {
  border-top-color: var(--blue-400);
  border-bottom-color: var(--blue-400);
}

.border-t-blue-500 {
  border-top-color: var(--blue-500);
}

.border-r-blue-500 {
  border-right-color: var(--blue-500);
}

.border-b-blue-500 {
  border-bottom-color: var(--blue-500);
}

.border-l-blue-500 {
  border-left-color: var(--blue-500);
}

.border-x-blue-500 {
  border-left-color: var(--blue-500);
  border-right-color: var(--blue-500);
}

.border-y-blue-500 {
  border-top-color: var(--blue-500);
  border-bottom-color: var(--blue-500);
}

.border-t-blue-600 {
  border-top-color: var(--blue-600);
}

.border-r-blue-600 {
  border-right-color: var(--blue-600);
}

.border-b-blue-600 {
  border-bottom-color: var(--blue-600);
}

.border-l-blue-600 {
  border-left-color: var(--blue-600);
}

.border-x-blue-600 {
  border-left-color: var(--blue-600);
  border-right-color: var(--blue-600);
}

.border-y-blue-600 {
  border-top-color: var(--blue-600);
  border-bottom-color: var(--blue-600);
}

.border-t-blue-700 {
  border-top-color: var(--blue-700);
}

.border-r-blue-700 {
  border-right-color: var(--blue-700);
}

.border-b-blue-700 {
  border-bottom-color: var(--blue-700);
}

.border-l-blue-700 {
  border-left-color: var(--blue-700);
}

.border-x-blue-700 {
  border-left-color: var(--blue-700);
  border-right-color: var(--blue-700);
}

.border-y-blue-700 {
  border-top-color: var(--blue-700);
  border-bottom-color: var(--blue-700);
}

.border-t-blue-800 {
  border-top-color: var(--blue-800);
}

.border-r-blue-800 {
  border-right-color: var(--blue-800);
}

.border-b-blue-800 {
  border-bottom-color: var(--blue-800);
}

.border-l-blue-800 {
  border-left-color: var(--blue-800);
}

.border-x-blue-800 {
  border-left-color: var(--blue-800);
  border-right-color: var(--blue-800);
}

.border-y-blue-800 {
  border-top-color: var(--blue-800);
  border-bottom-color: var(--blue-800);
}

.border-t-blue-900 {
  border-top-color: var(--blue-900);
}

.border-r-blue-900 {
  border-right-color: var(--blue-900);
}

.border-b-blue-900 {
  border-bottom-color: var(--blue-900);
}

.border-l-blue-900 {
  border-left-color: var(--blue-900);
}

.border-x-blue-900 {
  border-left-color: var(--blue-900);
  border-right-color: var(--blue-900);
}

.border-y-blue-900 {
  border-top-color: var(--blue-900);
  border-bottom-color: var(--blue-900);
}

.border-t-blue-950 {
  border-top-color: var(--blue-950);
}

.border-r-blue-950 {
  border-right-color: var(--blue-950);
}

.border-b-blue-950 {
  border-bottom-color: var(--blue-950);
}

.border-l-blue-950 {
  border-left-color: var(--blue-950);
}

.border-x-blue-950 {
  border-left-color: var(--blue-950);
  border-right-color: var(--blue-950);
}

.border-y-blue-950 {
  border-top-color: var(--blue-950);
  border-bottom-color: var(--blue-950);
}

.border-t-indigo-50 {
  border-top-color: var(--indigo-50);
}

.border-r-indigo-50 {
  border-right-color: var(--indigo-50);
}

.border-b-indigo-50 {
  border-bottom-color: var(--indigo-50);
}

.border-l-indigo-50 {
  border-left-color: var(--indigo-50);
}

.border-x-indigo-50 {
  border-left-color: var(--indigo-50);
  border-right-color: var(--indigo-50);
}

.border-y-indigo-50 {
  border-top-color: var(--indigo-50);
  border-bottom-color: var(--indigo-50);
}

.border-t-indigo-100 {
  border-top-color: var(--indigo-100);
}

.border-r-indigo-100 {
  border-right-color: var(--indigo-100);
}

.border-b-indigo-100 {
  border-bottom-color: var(--indigo-100);
}

.border-l-indigo-100 {
  border-left-color: var(--indigo-100);
}

.border-x-indigo-100 {
  border-left-color: var(--indigo-100);
  border-right-color: var(--indigo-100);
}

.border-y-indigo-100 {
  border-top-color: var(--indigo-100);
  border-bottom-color: var(--indigo-100);
}

.border-t-indigo-200 {
  border-top-color: var(--indigo-200);
}

.border-r-indigo-200 {
  border-right-color: var(--indigo-200);
}

.border-b-indigo-200 {
  border-bottom-color: var(--indigo-200);
}

.border-l-indigo-200 {
  border-left-color: var(--indigo-200);
}

.border-x-indigo-200 {
  border-left-color: var(--indigo-200);
  border-right-color: var(--indigo-200);
}

.border-y-indigo-200 {
  border-top-color: var(--indigo-200);
  border-bottom-color: var(--indigo-200);
}

.border-t-indigo-300 {
  border-top-color: var(--indigo-300);
}

.border-r-indigo-300 {
  border-right-color: var(--indigo-300);
}

.border-b-indigo-300 {
  border-bottom-color: var(--indigo-300);
}

.border-l-indigo-300 {
  border-left-color: var(--indigo-300);
}

.border-x-indigo-300 {
  border-left-color: var(--indigo-300);
  border-right-color: var(--indigo-300);
}

.border-y-indigo-300 {
  border-top-color: var(--indigo-300);
  border-bottom-color: var(--indigo-300);
}

.border-t-indigo-400 {
  border-top-color: var(--indigo-400);
}

.border-r-indigo-400 {
  border-right-color: var(--indigo-400);
}

.border-b-indigo-400 {
  border-bottom-color: var(--indigo-400);
}

.border-l-indigo-400 {
  border-left-color: var(--indigo-400);
}

.border-x-indigo-400 {
  border-left-color: var(--indigo-400);
  border-right-color: var(--indigo-400);
}

.border-y-indigo-400 {
  border-top-color: var(--indigo-400);
  border-bottom-color: var(--indigo-400);
}

.border-t-indigo-500 {
  border-top-color: var(--indigo-500);
}

.border-r-indigo-500 {
  border-right-color: var(--indigo-500);
}

.border-b-indigo-500 {
  border-bottom-color: var(--indigo-500);
}

.border-l-indigo-500 {
  border-left-color: var(--indigo-500);
}

.border-x-indigo-500 {
  border-left-color: var(--indigo-500);
  border-right-color: var(--indigo-500);
}

.border-y-indigo-500 {
  border-top-color: var(--indigo-500);
  border-bottom-color: var(--indigo-500);
}

.border-t-indigo-600 {
  border-top-color: var(--indigo-600);
}

.border-r-indigo-600 {
  border-right-color: var(--indigo-600);
}

.border-b-indigo-600 {
  border-bottom-color: var(--indigo-600);
}

.border-l-indigo-600 {
  border-left-color: var(--indigo-600);
}

.border-x-indigo-600 {
  border-left-color: var(--indigo-600);
  border-right-color: var(--indigo-600);
}

.border-y-indigo-600 {
  border-top-color: var(--indigo-600);
  border-bottom-color: var(--indigo-600);
}

.border-t-indigo-700 {
  border-top-color: var(--indigo-700);
}

.border-r-indigo-700 {
  border-right-color: var(--indigo-700);
}

.border-b-indigo-700 {
  border-bottom-color: var(--indigo-700);
}

.border-l-indigo-700 {
  border-left-color: var(--indigo-700);
}

.border-x-indigo-700 {
  border-left-color: var(--indigo-700);
  border-right-color: var(--indigo-700);
}

.border-y-indigo-700 {
  border-top-color: var(--indigo-700);
  border-bottom-color: var(--indigo-700);
}

.border-t-indigo-800 {
  border-top-color: var(--indigo-800);
}

.border-r-indigo-800 {
  border-right-color: var(--indigo-800);
}

.border-b-indigo-800 {
  border-bottom-color: var(--indigo-800);
}

.border-l-indigo-800 {
  border-left-color: var(--indigo-800);
}

.border-x-indigo-800 {
  border-left-color: var(--indigo-800);
  border-right-color: var(--indigo-800);
}

.border-y-indigo-800 {
  border-top-color: var(--indigo-800);
  border-bottom-color: var(--indigo-800);
}

.border-t-indigo-900 {
  border-top-color: var(--indigo-900);
}

.border-r-indigo-900 {
  border-right-color: var(--indigo-900);
}

.border-b-indigo-900 {
  border-bottom-color: var(--indigo-900);
}

.border-l-indigo-900 {
  border-left-color: var(--indigo-900);
}

.border-x-indigo-900 {
  border-left-color: var(--indigo-900);
  border-right-color: var(--indigo-900);
}

.border-y-indigo-900 {
  border-top-color: var(--indigo-900);
  border-bottom-color: var(--indigo-900);
}

.border-t-indigo-950 {
  border-top-color: var(--indigo-950);
}

.border-r-indigo-950 {
  border-right-color: var(--indigo-950);
}

.border-b-indigo-950 {
  border-bottom-color: var(--indigo-950);
}

.border-l-indigo-950 {
  border-left-color: var(--indigo-950);
}

.border-x-indigo-950 {
  border-left-color: var(--indigo-950);
  border-right-color: var(--indigo-950);
}

.border-y-indigo-950 {
  border-top-color: var(--indigo-950);
  border-bottom-color: var(--indigo-950);
}

.border-t-violet-50 {
  border-top-color: var(--violet-50);
}

.border-r-violet-50 {
  border-right-color: var(--violet-50);
}

.border-b-violet-50 {
  border-bottom-color: var(--violet-50);
}

.border-l-violet-50 {
  border-left-color: var(--violet-50);
}

.border-x-violet-50 {
  border-left-color: var(--violet-50);
  border-right-color: var(--violet-50);
}

.border-y-violet-50 {
  border-top-color: var(--violet-50);
  border-bottom-color: var(--violet-50);
}

.border-t-violet-100 {
  border-top-color: var(--violet-100);
}

.border-r-violet-100 {
  border-right-color: var(--violet-100);
}

.border-b-violet-100 {
  border-bottom-color: var(--violet-100);
}

.border-l-violet-100 {
  border-left-color: var(--violet-100);
}

.border-x-violet-100 {
  border-left-color: var(--violet-100);
  border-right-color: var(--violet-100);
}

.border-y-violet-100 {
  border-top-color: var(--violet-100);
  border-bottom-color: var(--violet-100);
}

.border-t-violet-200 {
  border-top-color: var(--violet-200);
}

.border-r-violet-200 {
  border-right-color: var(--violet-200);
}

.border-b-violet-200 {
  border-bottom-color: var(--violet-200);
}

.border-l-violet-200 {
  border-left-color: var(--violet-200);
}

.border-x-violet-200 {
  border-left-color: var(--violet-200);
  border-right-color: var(--violet-200);
}

.border-y-violet-200 {
  border-top-color: var(--violet-200);
  border-bottom-color: var(--violet-200);
}

.border-t-violet-300 {
  border-top-color: var(--violet-300);
}

.border-r-violet-300 {
  border-right-color: var(--violet-300);
}

.border-b-violet-300 {
  border-bottom-color: var(--violet-300);
}

.border-l-violet-300 {
  border-left-color: var(--violet-300);
}

.border-x-violet-300 {
  border-left-color: var(--violet-300);
  border-right-color: var(--violet-300);
}

.border-y-violet-300 {
  border-top-color: var(--violet-300);
  border-bottom-color: var(--violet-300);
}

.border-t-violet-400 {
  border-top-color: var(--violet-400);
}

.border-r-violet-400 {
  border-right-color: var(--violet-400);
}

.border-b-violet-400 {
  border-bottom-color: var(--violet-400);
}

.border-l-violet-400 {
  border-left-color: var(--violet-400);
}

.border-x-violet-400 {
  border-left-color: var(--violet-400);
  border-right-color: var(--violet-400);
}

.border-y-violet-400 {
  border-top-color: var(--violet-400);
  border-bottom-color: var(--violet-400);
}

.border-t-violet-500 {
  border-top-color: var(--violet-500);
}

.border-r-violet-500 {
  border-right-color: var(--violet-500);
}

.border-b-violet-500 {
  border-bottom-color: var(--violet-500);
}

.border-l-violet-500 {
  border-left-color: var(--violet-500);
}

.border-x-violet-500 {
  border-left-color: var(--violet-500);
  border-right-color: var(--violet-500);
}

.border-y-violet-500 {
  border-top-color: var(--violet-500);
  border-bottom-color: var(--violet-500);
}

.border-t-violet-600 {
  border-top-color: var(--violet-600);
}

.border-r-violet-600 {
  border-right-color: var(--violet-600);
}

.border-b-violet-600 {
  border-bottom-color: var(--violet-600);
}

.border-l-violet-600 {
  border-left-color: var(--violet-600);
}

.border-x-violet-600 {
  border-left-color: var(--violet-600);
  border-right-color: var(--violet-600);
}

.border-y-violet-600 {
  border-top-color: var(--violet-600);
  border-bottom-color: var(--violet-600);
}

.border-t-violet-700 {
  border-top-color: var(--violet-700);
}

.border-r-violet-700 {
  border-right-color: var(--violet-700);
}

.border-b-violet-700 {
  border-bottom-color: var(--violet-700);
}

.border-l-violet-700 {
  border-left-color: var(--violet-700);
}

.border-x-violet-700 {
  border-left-color: var(--violet-700);
  border-right-color: var(--violet-700);
}

.border-y-violet-700 {
  border-top-color: var(--violet-700);
  border-bottom-color: var(--violet-700);
}

.border-t-violet-800 {
  border-top-color: var(--violet-800);
}

.border-r-violet-800 {
  border-right-color: var(--violet-800);
}

.border-b-violet-800 {
  border-bottom-color: var(--violet-800);
}

.border-l-violet-800 {
  border-left-color: var(--violet-800);
}

.border-x-violet-800 {
  border-left-color: var(--violet-800);
  border-right-color: var(--violet-800);
}

.border-y-violet-800 {
  border-top-color: var(--violet-800);
  border-bottom-color: var(--violet-800);
}

.border-t-violet-900 {
  border-top-color: var(--violet-900);
}

.border-r-violet-900 {
  border-right-color: var(--violet-900);
}

.border-b-violet-900 {
  border-bottom-color: var(--violet-900);
}

.border-l-violet-900 {
  border-left-color: var(--violet-900);
}

.border-x-violet-900 {
  border-left-color: var(--violet-900);
  border-right-color: var(--violet-900);
}

.border-y-violet-900 {
  border-top-color: var(--violet-900);
  border-bottom-color: var(--violet-900);
}

.border-t-violet-950 {
  border-top-color: var(--violet-950);
}

.border-r-violet-950 {
  border-right-color: var(--violet-950);
}

.border-b-violet-950 {
  border-bottom-color: var(--violet-950);
}

.border-l-violet-950 {
  border-left-color: var(--violet-950);
}

.border-x-violet-950 {
  border-left-color: var(--violet-950);
  border-right-color: var(--violet-950);
}

.border-y-violet-950 {
  border-top-color: var(--violet-950);
  border-bottom-color: var(--violet-950);
}

.border-t-purple-50 {
  border-top-color: var(--purple-50);
}

.border-r-purple-50 {
  border-right-color: var(--purple-50);
}

.border-b-purple-50 {
  border-bottom-color: var(--purple-50);
}

.border-l-purple-50 {
  border-left-color: var(--purple-50);
}

.border-x-purple-50 {
  border-left-color: var(--purple-50);
  border-right-color: var(--purple-50);
}

.border-y-purple-50 {
  border-top-color: var(--purple-50);
  border-bottom-color: var(--purple-50);
}

.border-t-purple-100 {
  border-top-color: var(--purple-100);
}

.border-r-purple-100 {
  border-right-color: var(--purple-100);
}

.border-b-purple-100 {
  border-bottom-color: var(--purple-100);
}

.border-l-purple-100 {
  border-left-color: var(--purple-100);
}

.border-x-purple-100 {
  border-left-color: var(--purple-100);
  border-right-color: var(--purple-100);
}

.border-y-purple-100 {
  border-top-color: var(--purple-100);
  border-bottom-color: var(--purple-100);
}

.border-t-purple-200 {
  border-top-color: var(--purple-200);
}

.border-r-purple-200 {
  border-right-color: var(--purple-200);
}

.border-b-purple-200 {
  border-bottom-color: var(--purple-200);
}

.border-l-purple-200 {
  border-left-color: var(--purple-200);
}

.border-x-purple-200 {
  border-left-color: var(--purple-200);
  border-right-color: var(--purple-200);
}

.border-y-purple-200 {
  border-top-color: var(--purple-200);
  border-bottom-color: var(--purple-200);
}

.border-t-purple-300 {
  border-top-color: var(--purple-300);
}

.border-r-purple-300 {
  border-right-color: var(--purple-300);
}

.border-b-purple-300 {
  border-bottom-color: var(--purple-300);
}

.border-l-purple-300 {
  border-left-color: var(--purple-300);
}

.border-x-purple-300 {
  border-left-color: var(--purple-300);
  border-right-color: var(--purple-300);
}

.border-y-purple-300 {
  border-top-color: var(--purple-300);
  border-bottom-color: var(--purple-300);
}

.border-t-purple-400 {
  border-top-color: var(--purple-400);
}

.border-r-purple-400 {
  border-right-color: var(--purple-400);
}

.border-b-purple-400 {
  border-bottom-color: var(--purple-400);
}

.border-l-purple-400 {
  border-left-color: var(--purple-400);
}

.border-x-purple-400 {
  border-left-color: var(--purple-400);
  border-right-color: var(--purple-400);
}

.border-y-purple-400 {
  border-top-color: var(--purple-400);
  border-bottom-color: var(--purple-400);
}

.border-t-purple-500 {
  border-top-color: var(--purple-500);
}

.border-r-purple-500 {
  border-right-color: var(--purple-500);
}

.border-b-purple-500 {
  border-bottom-color: var(--purple-500);
}

.border-l-purple-500 {
  border-left-color: var(--purple-500);
}

.border-x-purple-500 {
  border-left-color: var(--purple-500);
  border-right-color: var(--purple-500);
}

.border-y-purple-500 {
  border-top-color: var(--purple-500);
  border-bottom-color: var(--purple-500);
}

.border-t-purple-600 {
  border-top-color: var(--purple-600);
}

.border-r-purple-600 {
  border-right-color: var(--purple-600);
}

.border-b-purple-600 {
  border-bottom-color: var(--purple-600);
}

.border-l-purple-600 {
  border-left-color: var(--purple-600);
}

.border-x-purple-600 {
  border-left-color: var(--purple-600);
  border-right-color: var(--purple-600);
}

.border-y-purple-600 {
  border-top-color: var(--purple-600);
  border-bottom-color: var(--purple-600);
}

.border-t-purple-700 {
  border-top-color: var(--purple-700);
}

.border-r-purple-700 {
  border-right-color: var(--purple-700);
}

.border-b-purple-700 {
  border-bottom-color: var(--purple-700);
}

.border-l-purple-700 {
  border-left-color: var(--purple-700);
}

.border-x-purple-700 {
  border-left-color: var(--purple-700);
  border-right-color: var(--purple-700);
}

.border-y-purple-700 {
  border-top-color: var(--purple-700);
  border-bottom-color: var(--purple-700);
}

.border-t-purple-800 {
  border-top-color: var(--purple-800);
}

.border-r-purple-800 {
  border-right-color: var(--purple-800);
}

.border-b-purple-800 {
  border-bottom-color: var(--purple-800);
}

.border-l-purple-800 {
  border-left-color: var(--purple-800);
}

.border-x-purple-800 {
  border-left-color: var(--purple-800);
  border-right-color: var(--purple-800);
}

.border-y-purple-800 {
  border-top-color: var(--purple-800);
  border-bottom-color: var(--purple-800);
}

.border-t-purple-900 {
  border-top-color: var(--purple-900);
}

.border-r-purple-900 {
  border-right-color: var(--purple-900);
}

.border-b-purple-900 {
  border-bottom-color: var(--purple-900);
}

.border-l-purple-900 {
  border-left-color: var(--purple-900);
}

.border-x-purple-900 {
  border-left-color: var(--purple-900);
  border-right-color: var(--purple-900);
}

.border-y-purple-900 {
  border-top-color: var(--purple-900);
  border-bottom-color: var(--purple-900);
}

.border-t-purple-950 {
  border-top-color: var(--purple-950);
}

.border-r-purple-950 {
  border-right-color: var(--purple-950);
}

.border-b-purple-950 {
  border-bottom-color: var(--purple-950);
}

.border-l-purple-950 {
  border-left-color: var(--purple-950);
}

.border-x-purple-950 {
  border-left-color: var(--purple-950);
  border-right-color: var(--purple-950);
}

.border-y-purple-950 {
  border-top-color: var(--purple-950);
  border-bottom-color: var(--purple-950);
}

.border-t-fuchsia-50 {
  border-top-color: var(--fuchsia-50);
}

.border-r-fuchsia-50 {
  border-right-color: var(--fuchsia-50);
}

.border-b-fuchsia-50 {
  border-bottom-color: var(--fuchsia-50);
}

.border-l-fuchsia-50 {
  border-left-color: var(--fuchsia-50);
}

.border-x-fuchsia-50 {
  border-left-color: var(--fuchsia-50);
  border-right-color: var(--fuchsia-50);
}

.border-y-fuchsia-50 {
  border-top-color: var(--fuchsia-50);
  border-bottom-color: var(--fuchsia-50);
}

.border-t-fuchsia-100 {
  border-top-color: var(--fuchsia-100);
}

.border-r-fuchsia-100 {
  border-right-color: var(--fuchsia-100);
}

.border-b-fuchsia-100 {
  border-bottom-color: var(--fuchsia-100);
}

.border-l-fuchsia-100 {
  border-left-color: var(--fuchsia-100);
}

.border-x-fuchsia-100 {
  border-left-color: var(--fuchsia-100);
  border-right-color: var(--fuchsia-100);
}

.border-y-fuchsia-100 {
  border-top-color: var(--fuchsia-100);
  border-bottom-color: var(--fuchsia-100);
}

.border-t-fuchsia-200 {
  border-top-color: var(--fuchsia-200);
}

.border-r-fuchsia-200 {
  border-right-color: var(--fuchsia-200);
}

.border-b-fuchsia-200 {
  border-bottom-color: var(--fuchsia-200);
}

.border-l-fuchsia-200 {
  border-left-color: var(--fuchsia-200);
}

.border-x-fuchsia-200 {
  border-left-color: var(--fuchsia-200);
  border-right-color: var(--fuchsia-200);
}

.border-y-fuchsia-200 {
  border-top-color: var(--fuchsia-200);
  border-bottom-color: var(--fuchsia-200);
}

.border-t-fuchsia-300 {
  border-top-color: var(--fuchsia-300);
}

.border-r-fuchsia-300 {
  border-right-color: var(--fuchsia-300);
}

.border-b-fuchsia-300 {
  border-bottom-color: var(--fuchsia-300);
}

.border-l-fuchsia-300 {
  border-left-color: var(--fuchsia-300);
}

.border-x-fuchsia-300 {
  border-left-color: var(--fuchsia-300);
  border-right-color: var(--fuchsia-300);
}

.border-y-fuchsia-300 {
  border-top-color: var(--fuchsia-300);
  border-bottom-color: var(--fuchsia-300);
}

.border-t-fuchsia-400 {
  border-top-color: var(--fuchsia-400);
}

.border-r-fuchsia-400 {
  border-right-color: var(--fuchsia-400);
}

.border-b-fuchsia-400 {
  border-bottom-color: var(--fuchsia-400);
}

.border-l-fuchsia-400 {
  border-left-color: var(--fuchsia-400);
}

.border-x-fuchsia-400 {
  border-left-color: var(--fuchsia-400);
  border-right-color: var(--fuchsia-400);
}

.border-y-fuchsia-400 {
  border-top-color: var(--fuchsia-400);
  border-bottom-color: var(--fuchsia-400);
}

.border-t-fuchsia-500 {
  border-top-color: var(--fuchsia-500);
}

.border-r-fuchsia-500 {
  border-right-color: var(--fuchsia-500);
}

.border-b-fuchsia-500 {
  border-bottom-color: var(--fuchsia-500);
}

.border-l-fuchsia-500 {
  border-left-color: var(--fuchsia-500);
}

.border-x-fuchsia-500 {
  border-left-color: var(--fuchsia-500);
  border-right-color: var(--fuchsia-500);
}

.border-y-fuchsia-500 {
  border-top-color: var(--fuchsia-500);
  border-bottom-color: var(--fuchsia-500);
}

.border-t-fuchsia-600 {
  border-top-color: var(--fuchsia-600);
}

.border-r-fuchsia-600 {
  border-right-color: var(--fuchsia-600);
}

.border-b-fuchsia-600 {
  border-bottom-color: var(--fuchsia-600);
}

.border-l-fuchsia-600 {
  border-left-color: var(--fuchsia-600);
}

.border-x-fuchsia-600 {
  border-left-color: var(--fuchsia-600);
  border-right-color: var(--fuchsia-600);
}

.border-y-fuchsia-600 {
  border-top-color: var(--fuchsia-600);
  border-bottom-color: var(--fuchsia-600);
}

.border-t-fuchsia-700 {
  border-top-color: var(--fuchsia-700);
}

.border-r-fuchsia-700 {
  border-right-color: var(--fuchsia-700);
}

.border-b-fuchsia-700 {
  border-bottom-color: var(--fuchsia-700);
}

.border-l-fuchsia-700 {
  border-left-color: var(--fuchsia-700);
}

.border-x-fuchsia-700 {
  border-left-color: var(--fuchsia-700);
  border-right-color: var(--fuchsia-700);
}

.border-y-fuchsia-700 {
  border-top-color: var(--fuchsia-700);
  border-bottom-color: var(--fuchsia-700);
}

.border-t-fuchsia-800 {
  border-top-color: var(--fuchsia-800);
}

.border-r-fuchsia-800 {
  border-right-color: var(--fuchsia-800);
}

.border-b-fuchsia-800 {
  border-bottom-color: var(--fuchsia-800);
}

.border-l-fuchsia-800 {
  border-left-color: var(--fuchsia-800);
}

.border-x-fuchsia-800 {
  border-left-color: var(--fuchsia-800);
  border-right-color: var(--fuchsia-800);
}

.border-y-fuchsia-800 {
  border-top-color: var(--fuchsia-800);
  border-bottom-color: var(--fuchsia-800);
}

.border-t-fuchsia-900 {
  border-top-color: var(--fuchsia-900);
}

.border-r-fuchsia-900 {
  border-right-color: var(--fuchsia-900);
}

.border-b-fuchsia-900 {
  border-bottom-color: var(--fuchsia-900);
}

.border-l-fuchsia-900 {
  border-left-color: var(--fuchsia-900);
}

.border-x-fuchsia-900 {
  border-left-color: var(--fuchsia-900);
  border-right-color: var(--fuchsia-900);
}

.border-y-fuchsia-900 {
  border-top-color: var(--fuchsia-900);
  border-bottom-color: var(--fuchsia-900);
}

.border-t-fuchsia-950 {
  border-top-color: var(--fuchsia-950);
}

.border-r-fuchsia-950 {
  border-right-color: var(--fuchsia-950);
}

.border-b-fuchsia-950 {
  border-bottom-color: var(--fuchsia-950);
}

.border-l-fuchsia-950 {
  border-left-color: var(--fuchsia-950);
}

.border-x-fuchsia-950 {
  border-left-color: var(--fuchsia-950);
  border-right-color: var(--fuchsia-950);
}

.border-y-fuchsia-950 {
  border-top-color: var(--fuchsia-950);
  border-bottom-color: var(--fuchsia-950);
}

.border-t-pink-50 {
  border-top-color: var(--pink-50);
}

.border-r-pink-50 {
  border-right-color: var(--pink-50);
}

.border-b-pink-50 {
  border-bottom-color: var(--pink-50);
}

.border-l-pink-50 {
  border-left-color: var(--pink-50);
}

.border-x-pink-50 {
  border-left-color: var(--pink-50);
  border-right-color: var(--pink-50);
}

.border-y-pink-50 {
  border-top-color: var(--pink-50);
  border-bottom-color: var(--pink-50);
}

.border-t-pink-100 {
  border-top-color: var(--pink-100);
}

.border-r-pink-100 {
  border-right-color: var(--pink-100);
}

.border-b-pink-100 {
  border-bottom-color: var(--pink-100);
}

.border-l-pink-100 {
  border-left-color: var(--pink-100);
}

.border-x-pink-100 {
  border-left-color: var(--pink-100);
  border-right-color: var(--pink-100);
}

.border-y-pink-100 {
  border-top-color: var(--pink-100);
  border-bottom-color: var(--pink-100);
}

.border-t-pink-200 {
  border-top-color: var(--pink-200);
}

.border-r-pink-200 {
  border-right-color: var(--pink-200);
}

.border-b-pink-200 {
  border-bottom-color: var(--pink-200);
}

.border-l-pink-200 {
  border-left-color: var(--pink-200);
}

.border-x-pink-200 {
  border-left-color: var(--pink-200);
  border-right-color: var(--pink-200);
}

.border-y-pink-200 {
  border-top-color: var(--pink-200);
  border-bottom-color: var(--pink-200);
}

.border-t-pink-300 {
  border-top-color: var(--pink-300);
}

.border-r-pink-300 {
  border-right-color: var(--pink-300);
}

.border-b-pink-300 {
  border-bottom-color: var(--pink-300);
}

.border-l-pink-300 {
  border-left-color: var(--pink-300);
}

.border-x-pink-300 {
  border-left-color: var(--pink-300);
  border-right-color: var(--pink-300);
}

.border-y-pink-300 {
  border-top-color: var(--pink-300);
  border-bottom-color: var(--pink-300);
}

.border-t-pink-400 {
  border-top-color: var(--pink-400);
}

.border-r-pink-400 {
  border-right-color: var(--pink-400);
}

.border-b-pink-400 {
  border-bottom-color: var(--pink-400);
}

.border-l-pink-400 {
  border-left-color: var(--pink-400);
}

.border-x-pink-400 {
  border-left-color: var(--pink-400);
  border-right-color: var(--pink-400);
}

.border-y-pink-400 {
  border-top-color: var(--pink-400);
  border-bottom-color: var(--pink-400);
}

.border-t-pink-500 {
  border-top-color: var(--pink-500);
}

.border-r-pink-500 {
  border-right-color: var(--pink-500);
}

.border-b-pink-500 {
  border-bottom-color: var(--pink-500);
}

.border-l-pink-500 {
  border-left-color: var(--pink-500);
}

.border-x-pink-500 {
  border-left-color: var(--pink-500);
  border-right-color: var(--pink-500);
}

.border-y-pink-500 {
  border-top-color: var(--pink-500);
  border-bottom-color: var(--pink-500);
}

.border-t-pink-600 {
  border-top-color: var(--pink-600);
}

.border-r-pink-600 {
  border-right-color: var(--pink-600);
}

.border-b-pink-600 {
  border-bottom-color: var(--pink-600);
}

.border-l-pink-600 {
  border-left-color: var(--pink-600);
}

.border-x-pink-600 {
  border-left-color: var(--pink-600);
  border-right-color: var(--pink-600);
}

.border-y-pink-600 {
  border-top-color: var(--pink-600);
  border-bottom-color: var(--pink-600);
}

.border-t-pink-700 {
  border-top-color: var(--pink-700);
}

.border-r-pink-700 {
  border-right-color: var(--pink-700);
}

.border-b-pink-700 {
  border-bottom-color: var(--pink-700);
}

.border-l-pink-700 {
  border-left-color: var(--pink-700);
}

.border-x-pink-700 {
  border-left-color: var(--pink-700);
  border-right-color: var(--pink-700);
}

.border-y-pink-700 {
  border-top-color: var(--pink-700);
  border-bottom-color: var(--pink-700);
}

.border-t-pink-800 {
  border-top-color: var(--pink-800);
}

.border-r-pink-800 {
  border-right-color: var(--pink-800);
}

.border-b-pink-800 {
  border-bottom-color: var(--pink-800);
}

.border-l-pink-800 {
  border-left-color: var(--pink-800);
}

.border-x-pink-800 {
  border-left-color: var(--pink-800);
  border-right-color: var(--pink-800);
}

.border-y-pink-800 {
  border-top-color: var(--pink-800);
  border-bottom-color: var(--pink-800);
}

.border-t-pink-900 {
  border-top-color: var(--pink-900);
}

.border-r-pink-900 {
  border-right-color: var(--pink-900);
}

.border-b-pink-900 {
  border-bottom-color: var(--pink-900);
}

.border-l-pink-900 {
  border-left-color: var(--pink-900);
}

.border-x-pink-900 {
  border-left-color: var(--pink-900);
  border-right-color: var(--pink-900);
}

.border-y-pink-900 {
  border-top-color: var(--pink-900);
  border-bottom-color: var(--pink-900);
}

.border-t-pink-950 {
  border-top-color: var(--pink-950);
}

.border-r-pink-950 {
  border-right-color: var(--pink-950);
}

.border-b-pink-950 {
  border-bottom-color: var(--pink-950);
}

.border-l-pink-950 {
  border-left-color: var(--pink-950);
}

.border-x-pink-950 {
  border-left-color: var(--pink-950);
  border-right-color: var(--pink-950);
}

.border-y-pink-950 {
  border-top-color: var(--pink-950);
  border-bottom-color: var(--pink-950);
}

.border-t-rose-50 {
  border-top-color: var(--rose-50);
}

.border-r-rose-50 {
  border-right-color: var(--rose-50);
}

.border-b-rose-50 {
  border-bottom-color: var(--rose-50);
}

.border-l-rose-50 {
  border-left-color: var(--rose-50);
}

.border-x-rose-50 {
  border-left-color: var(--rose-50);
  border-right-color: var(--rose-50);
}

.border-y-rose-50 {
  border-top-color: var(--rose-50);
  border-bottom-color: var(--rose-50);
}

.border-t-rose-100 {
  border-top-color: var(--rose-100);
}

.border-r-rose-100 {
  border-right-color: var(--rose-100);
}

.border-b-rose-100 {
  border-bottom-color: var(--rose-100);
}

.border-l-rose-100 {
  border-left-color: var(--rose-100);
}

.border-x-rose-100 {
  border-left-color: var(--rose-100);
  border-right-color: var(--rose-100);
}

.border-y-rose-100 {
  border-top-color: var(--rose-100);
  border-bottom-color: var(--rose-100);
}

.border-t-rose-200 {
  border-top-color: var(--rose-200);
}

.border-r-rose-200 {
  border-right-color: var(--rose-200);
}

.border-b-rose-200 {
  border-bottom-color: var(--rose-200);
}

.border-l-rose-200 {
  border-left-color: var(--rose-200);
}

.border-x-rose-200 {
  border-left-color: var(--rose-200);
  border-right-color: var(--rose-200);
}

.border-y-rose-200 {
  border-top-color: var(--rose-200);
  border-bottom-color: var(--rose-200);
}

.border-t-rose-300 {
  border-top-color: var(--rose-300);
}

.border-r-rose-300 {
  border-right-color: var(--rose-300);
}

.border-b-rose-300 {
  border-bottom-color: var(--rose-300);
}

.border-l-rose-300 {
  border-left-color: var(--rose-300);
}

.border-x-rose-300 {
  border-left-color: var(--rose-300);
  border-right-color: var(--rose-300);
}

.border-y-rose-300 {
  border-top-color: var(--rose-300);
  border-bottom-color: var(--rose-300);
}

.border-t-rose-400 {
  border-top-color: var(--rose-400);
}

.border-r-rose-400 {
  border-right-color: var(--rose-400);
}

.border-b-rose-400 {
  border-bottom-color: var(--rose-400);
}

.border-l-rose-400 {
  border-left-color: var(--rose-400);
}

.border-x-rose-400 {
  border-left-color: var(--rose-400);
  border-right-color: var(--rose-400);
}

.border-y-rose-400 {
  border-top-color: var(--rose-400);
  border-bottom-color: var(--rose-400);
}

.border-t-rose-500 {
  border-top-color: var(--rose-500);
}

.border-r-rose-500 {
  border-right-color: var(--rose-500);
}

.border-b-rose-500 {
  border-bottom-color: var(--rose-500);
}

.border-l-rose-500 {
  border-left-color: var(--rose-500);
}

.border-x-rose-500 {
  border-left-color: var(--rose-500);
  border-right-color: var(--rose-500);
}

.border-y-rose-500 {
  border-top-color: var(--rose-500);
  border-bottom-color: var(--rose-500);
}

.border-t-rose-600 {
  border-top-color: var(--rose-600);
}

.border-r-rose-600 {
  border-right-color: var(--rose-600);
}

.border-b-rose-600 {
  border-bottom-color: var(--rose-600);
}

.border-l-rose-600 {
  border-left-color: var(--rose-600);
}

.border-x-rose-600 {
  border-left-color: var(--rose-600);
  border-right-color: var(--rose-600);
}

.border-y-rose-600 {
  border-top-color: var(--rose-600);
  border-bottom-color: var(--rose-600);
}

.border-t-rose-700 {
  border-top-color: var(--rose-700);
}

.border-r-rose-700 {
  border-right-color: var(--rose-700);
}

.border-b-rose-700 {
  border-bottom-color: var(--rose-700);
}

.border-l-rose-700 {
  border-left-color: var(--rose-700);
}

.border-x-rose-700 {
  border-left-color: var(--rose-700);
  border-right-color: var(--rose-700);
}

.border-y-rose-700 {
  border-top-color: var(--rose-700);
  border-bottom-color: var(--rose-700);
}

.border-t-rose-800 {
  border-top-color: var(--rose-800);
}

.border-r-rose-800 {
  border-right-color: var(--rose-800);
}

.border-b-rose-800 {
  border-bottom-color: var(--rose-800);
}

.border-l-rose-800 {
  border-left-color: var(--rose-800);
}

.border-x-rose-800 {
  border-left-color: var(--rose-800);
  border-right-color: var(--rose-800);
}

.border-y-rose-800 {
  border-top-color: var(--rose-800);
  border-bottom-color: var(--rose-800);
}

.border-t-rose-900 {
  border-top-color: var(--rose-900);
}

.border-r-rose-900 {
  border-right-color: var(--rose-900);
}

.border-b-rose-900 {
  border-bottom-color: var(--rose-900);
}

.border-l-rose-900 {
  border-left-color: var(--rose-900);
}

.border-x-rose-900 {
  border-left-color: var(--rose-900);
  border-right-color: var(--rose-900);
}

.border-y-rose-900 {
  border-top-color: var(--rose-900);
  border-bottom-color: var(--rose-900);
}

.border-t-rose-950 {
  border-top-color: var(--rose-950);
}

.border-r-rose-950 {
  border-right-color: var(--rose-950);
}

.border-b-rose-950 {
  border-bottom-color: var(--rose-950);
}

.border-l-rose-950 {
  border-left-color: var(--rose-950);
}

.border-x-rose-950 {
  border-left-color: var(--rose-950);
  border-right-color: var(--rose-950);
}

.border-y-rose-950 {
  border-top-color: var(--rose-950);
  border-bottom-color: var(--rose-950);
}

.border-white {
  border-color: #ffffff;
}

.border-t-white {
  border-top-color: #ffffff;
}

.border-r-white {
  border-right-color: #ffffff;
}

.border-b-white {
  border-bottom-color: #ffffff;
}

.border-l-white {
  border-left-color: #ffffff;
}

.border-x-white {
  border-left-color: #ffffff;
  border-right-color: #ffffff;
}

.border-y-white {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff;
}

.border-black {
  border-color: #000000;
}

.border-t-black {
  border-top-color: #000000;
}

.border-r-black {
  border-right-color: #000000;
}

.border-b-black {
  border-bottom-color: #000000;
}

.border-l-black {
  border-left-color: #000000;
}

.border-x-black {
  border-left-color: #000000;
  border-right-color: #000000;
}

.border-y-black {
  border-top-color: #000000;
  border-bottom-color: #000000;
}