<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <!-- 引入 Utility.uss 样式库 -->
    <Style src="project://database/Assets/Resources/TailwindUss/index.uss" />
    <!-- 引入补充样式 -->
    <Style src="project://database/Assets/Resources/UI Toolkit/DatabaseConnectionWindow-Utility.uss" />

    <ui:VisualElement name="database-connection-content" class="bg-neutral-900 p-6">
        <!-- Header Section -->
        <ui:VisualElement name="header-container" class="mb-6 pb-4 border-b-neutral-700">
            <ui:Label name="title-label" text="数据库连接配置" class="text-neutral-100 text-lg font-bold mb-1" />
            <ui:Label name="subtitle-label" text="配置并测试数据库连接参数" class="text-neutral-400 text-sm" />
        </ui:VisualElement>

        <!-- Form Section -->
        <ui:VisualElement name="form-container" class="flex-grow mb-4">
            <!-- Connection Info Group -->
            <ui:VisualElement name="connection-info-group" class="bg-neutral-800 border-neutral-600 rounded p-4 mb-5">
                <ui:Label name="connection-info-title" text="连接信息" class="text-neutral-200 text-sm font-bold mb-3" />

                <!-- Row 1: Server -->
                <ui:VisualElement name="server-container" class="mb-4">
                    <ui:Label name="server-label" text="服务器地址 *" class="text-neutral-300 text-xs mb-1" />
                    <ui:TextField name="server-field" class="field-input-enhanced-neutral" value="localhost" />
                </ui:VisualElement>

                <!-- Row 2: Database -->
                <ui:VisualElement name="database-container" class="mb-4">
                    <ui:Label name="database-label" text="数据库名称 *" class="text-neutral-300 text-xs mb-1" />
                    <ui:TextField name="database-field" class="field-input-enhanced-neutral" value="DTCKC_DATA" />
                </ui:VisualElement>

                <!-- Row 3: Username -->
                <ui:VisualElement name="username-container" class="mb-4">
                    <ui:Label name="username-label" text="用户名 *" class="text-neutral-300 text-xs mb-1" />
                    <ui:TextField name="username-field" class="field-input-enhanced-neutral" value="sa" />
                </ui:VisualElement>

                <!-- Row 4: Password -->
                <ui:VisualElement name="password-container" class="mb-4">
                    <ui:Label name="password-label" text="密码 *" class="text-neutral-300 text-xs mb-1" />
                    <ui:TextField name="password-field" class="field-input-enhanced-neutral" password="true" value="123456" />
                </ui:VisualElement>
            </ui:VisualElement>

            <!-- Advanced Settings Group -->
            <ui:VisualElement name="advanced-settings-group" class="bg-neutral-700 border-neutral-600 rounded p-4 mb-5">
                <ui:Label name="advanced-settings-title" text="高级设置" class="text-neutral-200 text-sm font-bold mb-3" />

                <ui:VisualElement name="advanced-grid" class="flex-col">
                    <ui:VisualElement name="encrypt-container" class="flex-row items-center justify-between py-2 mb-2">
                        <ui:Label name="encrypt-label" text="启用连接加密" class="text-neutral-300 text-xs flex-grow" />
                        <ui:Toggle name="encrypt-toggle" class="ml-2" value="false" />
                    </ui:VisualElement>

                    <ui:VisualElement name="trust-certificate-container" class="flex-row items-center justify-between py-2">
                        <ui:Label name="trust-certificate-label" text="信任服务器证书" class="text-neutral-300 text-xs flex-grow" />
                        <ui:Toggle name="trust-certificate-toggle" class="ml-2" value="true" />
                    </ui:VisualElement>
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>

        <!-- Status Section -->
        <ui:VisualElement name="status-container" class="flex-row items-center bg-neutral-800 border-neutral-600 rounded p-3 mb-4">
            <ui:VisualElement name="status-icon" class="w-4 h-4 bg-neutral-500 rounded-full mr-2" />
            <ui:Label name="status-label" text="请配置数据库连接参数后点击测试连接" class="text-neutral-300 text-xs flex-grow" />
            <ui:VisualElement name="loading-indicator" class="w-4 h-4 bg-blue-400 rounded-full ml-2 hidden" />
        </ui:VisualElement>

        <!-- Button Section -->
        <ui:VisualElement name="button-container" class="flex-row justify-between items-center pt-4 border-t-neutral-700">
            <ui:VisualElement name="button-left-group" class="flex-row items-center" />

            <ui:VisualElement name="button-right-group" class="flex-row items-center">
                <ui:Button name="test-button" text="测试连接" class="bg-blue-600 text-white border-blue-600 rounded px-5 py-2 text-sm font-medium mr-2" />
                <ui:Button name="cancel-button" text="取消" class="bg-neutral-600 text-neutral-200 border-neutral-500 rounded px-5 py-2 text-sm font-medium" />
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>