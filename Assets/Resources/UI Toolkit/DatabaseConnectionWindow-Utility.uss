/* DatabaseConnectionWindow 使用 Utility.uss 的补充样式 */

/* 最小宽高设置 */
.min-w-540 {
    min-width: 540px;
}

.min-h-480 {
    min-height: 480px;
}

/* 边距和内边距补充 */
.p-6 {
    padding: 24px;
}

.p-4 {
    padding: 16px;
}

.p-3 {
    padding: 12px;
}

.p-2 {
    padding: 8px;
}

.px-5 {
    padding-left: 20px;
    padding-right: 20px;
}

.py-2 {
    padding-top: 8px;
    padding-bottom: 8px;
}

.mb-6 {
    margin-bottom: 24px;
}

.mb-5 {
    margin-bottom: 20px;
}

.mb-4 {
    margin-bottom: 16px;
}

.mb-3 {
    margin-bottom: 12px;
}

.mb-2 {
    margin-bottom: 8px;
}

.mb-1 {
    margin-bottom: 4px;
}

.mr-2 {
    margin-right: 8px;
}

.ml-2 {
    margin-left: 8px;
}

.pb-4 {
    padding-bottom: 16px;
}

.pt-4 {
    padding-top: 16px;
}

/* 布局补充 */
.flex-row {
    flex-direction: row;
}

.flex-col {
    flex-direction: column;
}

.flex-grow {
    flex-grow: 1;
}

.justify-between {
    justify-content: space-between;
}

/* 尺寸补充 */
.w-4 {
    width: 16px;
}

.h-4 {
    height: 16px;
}

/* 字体大小补充 */
.text-lg {
    font-size: 16px;
}

.text-sm {
    font-size: 12px;
}

.text-xs {
    font-size: 11px;
}

/* 字体粗细 */
.font-bold {
    -unity-font-style: normal;
}

.font-medium {
    -unity-font-style: normal;
    /* Unity USS 不支持 font-weight，使用 font-style */
}

/* 圆角补充 */
.rounded {
    border-radius: 4px;
}

.rounded-full {
    border-radius: 50%;
}

/* 边框补充 */
.border-slate-200 {
    border-width: 1px;
    border-color: var(--slate-200);
}

.border-slate-300 {
    border-width: 1px;
    border-color: var(--slate-300);
}

.border-slate-500 {
    border-width: 1px;
    border-color: var(--slate-500);
}

.border-slate-600 {
    border-width: 1px;
    border-color: var(--slate-600);
}

.border-blue-200 {
    border-width: 1px;
    border-color: var(--blue-200);
}

.border-blue-500 {
    border-width: 1px;
    border-color: var(--blue-500);
}

.border-blue-600 {
    border-width: 1px;
    border-color: var(--blue-600);
}

.border-t-slate-200 {
    border-top-width: 1px;
    border-top-color: var(--slate-200);
}

.border-t-slate-700 {
    border-top-width: 1px;
    border-top-color: var(--slate-700);
}

.border-b-slate-200 {
    border-bottom-width: 1px;
    border-bottom-color: var(--slate-200);
}

.border-b-slate-700 {
    border-bottom-width: 1px;
    border-bottom-color: var(--slate-700);
}

/* Neutral 色系边框颜色 */
.border-neutral-500 {
    border-width: 1px;
    border-color: var(--neutral-500);
}

.border-neutral-600 {
    border-width: 1px;
    border-color: var(--neutral-600);
}

.border-neutral-700 {
    border-width: 1px;
    border-color: var(--neutral-700);
}

.border-t-neutral-700 {
    border-top-width: 1px;
    border-top-color: var(--neutral-700);
}

.border-b-neutral-700 {
    border-bottom-width: 1px;
    border-bottom-color: var(--neutral-700);
}

/* 文本颜色补充 */
.text-slate-100 {
    color: var(--slate-100);
}

.text-slate-200 {
    color: var(--slate-200);
}

.text-slate-300 {
    color: var(--slate-300);
}

.text-slate-400 {
    color: var(--slate-400);
}

.text-slate-500 {
    color: var(--slate-500);
}

.text-slate-600 {
    color: var(--slate-600);
}

.text-slate-700 {
    color: var(--slate-700);
}

.text-slate-800 {
    color: var(--slate-800);
}

.text-slate-900 {
    color: var(--slate-900);
}

/* Neutral 色系文本颜色 */
.text-neutral-100 {
    color: var(--neutral-100);
}

.text-neutral-200 {
    color: var(--neutral-200);
}

.text-neutral-300 {
    color: var(--neutral-300);
}

.text-neutral-400 {
    color: var(--neutral-400);
}

.text-neutral-500 {
    color: var(--neutral-500);
}

.text-neutral-600 {
    color: var(--neutral-600);
}

.text-neutral-700 {
    color: var(--neutral-700);
}

.text-neutral-800 {
    color: var(--neutral-800);
}

.text-neutral-900 {
    color: var(--neutral-900);
}

.text-white {
    color: #ffffff;
}

/* 背景颜色补充 */
.bg-blue-50 {
    background-color: var(--blue-50);
}

.bg-blue-400 {
    background-color: var(--blue-400);
}

.bg-blue-500 {
    background-color: var(--blue-500);
}

.bg-blue-600 {
    background-color: var(--blue-600);
}

.bg-slate-50 {
    background-color: var(--slate-50);
}

.bg-slate-100 {
    background-color: var(--slate-100);
}

.bg-slate-200 {
    background-color: var(--slate-200);
}

.bg-slate-400 {
    background-color: var(--slate-400);
}

.bg-slate-500 {
    background-color: var(--slate-500);
}

.bg-slate-600 {
    background-color: var(--slate-600);
}

.bg-slate-700 {
    background-color: var(--slate-700);
}

.bg-slate-800 {
    background-color: var(--slate-800);
}

.bg-slate-900 {
    background-color: var(--slate-900);
}

/* Neutral 色系背景颜色 */
.bg-neutral-500 {
    background-color: var(--neutral-500);
}

.bg-neutral-600 {
    background-color: var(--neutral-600);
}

.bg-neutral-700 {
    background-color: var(--neutral-700);
}

.bg-neutral-800 {
    background-color: var(--neutral-800);
}

.bg-neutral-900 {
    background-color: var(--neutral-900);
}

.bg-white {
    background-color: #ffffff;
}

/* 显示/隐藏 */
.hidden {
    display: none;
}

/* 输入框样式增强 */
.field-input-enhanced {
    min-height: 32px;
    height: 32px;
    padding-left: 12px;
    padding-right: 12px;
    padding-top: 6px;
    padding-bottom: 6px;
    border-width: 1px;
    border-color: var(--slate-300);
    border-radius: 6px;
    background-color: #ffffff;
    color: var(--slate-900);
    font-size: 13px;
    -unity-text-align: middle-left;
}

.field-input-enhanced:focus {
    border-color: var(--blue-500);
    background-color: #ffffff;
}

.field-input-enhanced:hover {
    border-color: var(--slate-400);
}

.field-input-enhanced:disabled {
    background-color: var(--slate-100);
    color: var(--slate-500);
    border-color: var(--slate-200);
}

/* 输入框内部文本样式 */
.field-input-enhanced > #unity-text-input {
    background-color: transparent;
    border-width: 0;
    padding: 0;
    margin: 0;
    color: var(--slate-900);
    font-size: 13px;
}

.field-input-enhanced:focus > #unity-text-input {
    color: var(--slate-900);
}

/* 密码输入框特殊样式 */
.field-input-enhanced[password="true"] > #unity-text-input {
    -unity-font-style: normal;
}

/* 深色主题输入框样式 */
.field-input-enhanced-dark {
    min-height: 32px;
    height: 32px;
    padding-left: 12px;
    padding-right: 12px;
    padding-top: 6px;
    padding-bottom: 6px;
    border-width: 1px;
    border-color: var(--slate-600);
    border-radius: 6px;
    background-color: var(--slate-700);
    color: var(--slate-100);
    font-size: 13px;
    -unity-text-align: middle-left;
}

.field-input-enhanced-dark:focus {
    border-color: var(--blue-400);
    background-color: var(--slate-700);
}

.field-input-enhanced-dark:hover {
    border-color: var(--slate-500);
}

.field-input-enhanced-dark:disabled {
    background-color: var(--slate-800);
    color: var(--slate-600);
    border-color: var(--slate-700);
}

/* 深色主题输入框内部文本样式 */
.field-input-enhanced-dark > #unity-text-input {
    background-color: transparent;
    border-width: 0;
    padding: 0;
    margin: 0;
    color: var(--slate-100);
    font-size: 13px;
}

.field-input-enhanced-dark:focus > #unity-text-input {
    color: var(--slate-100);
}

/* 深色主题密码输入框特殊样式 */
.field-input-enhanced-dark[password="true"] > #unity-text-input {
    -unity-font-style: normal;
}

/* Neutral 主题输入框样式 */
.field-input-enhanced-neutral {
    min-height: 32px;
    height: 32px;
    padding-left: 12px;
    padding-right: 12px;
    padding-top: 6px;
    padding-bottom: 6px;
    border-width: 1px;
    border-color: var(--neutral-600);
    border-radius: 6px;
    background-color: var(--neutral-700);
    color: var(--neutral-100);
    font-size: 13px;
    -unity-text-align: middle-left;
}

.field-input-enhanced-neutral:focus {
    border-color: var(--blue-400);
    background-color: var(--neutral-700);
}

.field-input-enhanced-neutral:hover {
    border-color: var(--neutral-500);
}

.field-input-enhanced-neutral:disabled {
    background-color: var(--neutral-800);
    color: var(--neutral-600);
    border-color: var(--neutral-700);
}

/* Neutral 主题输入框内部文本样式 */
.field-input-enhanced-neutral > #unity-text-input {
    background-color: transparent;
    border-width: 0;
    padding: 0;
    margin: 0;
    color: var(--neutral-100);
    font-size: 13px;
}

.field-input-enhanced-neutral:focus > #unity-text-input {
    color: var(--neutral-100);
}

/* Neutral 主题密码输入框特殊样式 */
.field-input-enhanced-neutral[password="true"] > #unity-text-input {
    -unity-font-style: normal;
}

/* 按钮样式增强 */
Button:hover.bg-blue-500 {
    background-color: var(--blue-600);
    border-color: var(--blue-600);
}

Button:active.bg-blue-500 {
    background-color: var(--blue-700);
    border-color: var(--blue-700);
}

Button:hover.bg-blue-600 {
    background-color: var(--blue-500);
    border-color: var(--blue-500);
}

Button:active.bg-blue-600 {
    background-color: var(--blue-700);
    border-color: var(--blue-700);
}

Button:hover.bg-slate-200 {
    background-color: var(--slate-300);
    border-color: var(--slate-400);
}

Button:active.bg-slate-200 {
    background-color: var(--slate-400);
    border-color: var(--slate-500);
}

Button:hover.bg-slate-600 {
    background-color: var(--slate-500);
    border-color: var(--slate-400);
}

Button:active.bg-slate-600 {
    background-color: var(--slate-700);
    border-color: var(--slate-600);
}

/* Neutral 主题按钮交互样式 */
Button:hover.bg-neutral-600 {
    background-color: var(--neutral-500);
    border-color: var(--neutral-400);
}

Button:active.bg-neutral-600 {
    background-color: var(--neutral-700);
    border-color: var(--neutral-600);
}

/* 状态指示器样式 */
.status-icon.status-success {
    background-color: var(--green-500);
}

.status-icon.status-error {
    background-color: var(--red-500);
}

.status-icon.status-warning {
    background-color: var(--yellow-500);
}

.status-icon.status-info {
    background-color: var(--blue-500);
}

/* 状态容器样式变体 */
.status-container.status-success {
    background-color: var(--green-50);
    border-color: var(--green-200);
}

.status-container.status-error {
    background-color: var(--red-50);
    border-color: var(--red-200);
}

.status-container.status-warning {
    background-color: var(--yellow-50);
    border-color: var(--yellow-200);
}

/* 表单验证样式 */
.field-error {
    border-color: var(--red-500);
}

.field-error:focus {
    border-color: var(--red-600);
}

.error-message {
    color: var(--red-600);
    font-size: 10px;
    margin-top: 2px;
}

/* 响应式设计辅助类 */
.responsive-row {
    flex-direction: row;
}

/* 当需要垂直布局时，可以通过代码动态添加这个类 */
.responsive-row.vertical {
    flex-direction: column;
}

.responsive-row.vertical .flex-grow {
    margin-right: 0;
    margin-bottom: 8px;
}

/* Unity UI Toolkit 不支持 :last-child 伪类选择器 */
.responsive-row.vertical .flex-grow.last-item {
    margin-bottom: 0;
}
