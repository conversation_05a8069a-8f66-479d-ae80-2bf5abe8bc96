/* Database Connection Window Styles */

/* Main Container */
.database-connection-content {
    flex-grow: 1;
    padding: 20px;
    background-color: var(--unity-colors-window-background);
    min-width: 540px;
    min-height: 480px;
}

/* Header Section */
.database-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom-width: 1px;
    border-bottom-color: var(--unity-colors-helpbox-border);
}

.database-title {
    font-size: 16px;
    -unity-font-style: normal;
    color: var(--unity-colors-label-text);
    margin-bottom: 4px;
    -unity-text-align: upper-left;
}

.database-subtitle {
    font-size: 12px;
    color: var(--unity-colors-label-text-disabled);
    -unity-text-align: upper-left;
}

/* Form Container */
.form-container {
    flex-grow: 1;
    margin-bottom: 16px;
}

/* Form Groups */
.form-group {
    margin-bottom: 20px;
    padding: 16px;
    background-color: var(--unity-colors-helpbox-background);
    border-radius: 4px;
    border-width: 1px;
    border-color: var(--unity-colors-helpbox-border);
}

.form-group-title {
    font-size: 14px;
    -unity-font-style: normal;
    color: var(--unity-colors-label-text);
    margin-bottom: 12px;
    -unity-text-align: upper-left;
}

/* Field Rows */
.field-row {
    flex-direction: row;
    margin-bottom: 12px;
}

/* Unity UI Toolkit 不支持 :last-child 伪类选择器 */
/* 需要在 C# 代码中通过添加特定类名来控制最后一个元素的样式 */
.field-row.last-item {
    margin-bottom: 0;
}

/* Field Container */
.field-container {
    flex-grow: 1;
    margin-right: 8px;
}

/* Unity UI Toolkit 不支持 :last-child 伪类选择器 */
.field-container.last-item {
    margin-right: 0;
}

.field-container.half-width {
    flex-basis: 50%;
    max-width: 50%;
}

/* Field Labels */
.field-label {
    font-size: 12px;
    color: var(--unity-colors-label-text);
    margin-bottom: 4px;
    -unity-text-align: upper-left;
}



/* Field Inputs */
.field-input {
    min-height: 20px;
    padding: 4px 8px;
    border-radius: 3px;
    border-width: 1px;
    border-color: var(--unity-colors-input-text-border);
    background-color: var(--unity-colors-input-text-background);
    color: var(--unity-colors-input-text);
}

.field-input:focus {
    border-color: var(--unity-colors-input-text-border-focus);
}

.field-input:hover {
    border-color: var(--unity-colors-input-text-border-hover);
}

/* Advanced Settings */
.advanced-settings {
    background-color: var(--unity-colors-inspector-background);
}

.advanced-title {
    color: var(--unity-colors-label-text);
}

.advanced-grid {
    flex-direction: column;
}

/* Toggle Container */
.toggle-container {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 8px 0;
}

/* Unity UI Toolkit 不支持 :last-child 伪类选择器 */
.toggle-container.last-item {
    margin-bottom: 0;
}

.toggle-label {
    font-size: 12px;
    color: var(--unity-colors-label-text);
    flex-grow: 1;
    -unity-text-align: middle-left;
}

.toggle-input {
    margin-left: 8px;
}

/* Status Section */
.status-container {
    flex-direction: row;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background-color: var(--unity-colors-helpbox-background);
    border-radius: 4px;
    border-width: 1px;
    border-color: var(--unity-colors-helpbox-border);
}

.status-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border-radius: 50%;
    background-color: var(--unity-colors-label-text-disabled);
}

.status-label {
    font-size: 12px;
    color: var(--unity-colors-label-text);
    flex-grow: 1;
    -unity-text-align: middle-left;
}

.status-label.status-success {
    color: var(--unity-colors-highlight-text);
}

.status-label.status-error {
    color: var(--unity-colors-error-text);
}

.status-label.status-info {
    color: var(--unity-colors-label-text);
}

.loading-indicator {
    width: 16px;
    height: 16px;
    margin-left: 8px;
    background-color: var(--unity-colors-label-text-disabled);
    border-radius: 50%;
    opacity: 0.7;
}

.loading-indicator.hidden {
    display: none;
}

/* Button Section */
.button-container {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding-top: 16px;
    border-top-width: 1px;
    border-top-color: var(--unity-colors-helpbox-border);
}

.button-left-group {
    flex-direction: row;
    align-items: center;
}

.button-right-group {
    flex-direction: row;
    align-items: center;
}

/* Button Styles */
.db-button {
    min-width: 80px;
    height: 24px;
    padding: 4px 12px;
    margin-left: 8px;
    border-radius: 3px;
    border-width: 1px;
    font-size: 12px;
    -unity-text-align: middle-center;
}

/* Unity UI Toolkit 不支持 :first-child 伪类选择器 */
.db-button.first-item {
    margin-left: 0;
}

.cancel-button {
    background-color: var(--unity-colors-button-background);
    border-color: var(--unity-colors-button-border);
    color: var(--unity-colors-button-text);
}

.cancel-button:hover {
    background-color: var(--unity-colors-button-background-hover);
    border-color: var(--unity-colors-button-border-hover);
}

.cancel-button:active {
    background-color: var(--unity-colors-button-background-pressed);
    border-color: var(--unity-colors-button-border-pressed);
}

.save-button {
    background-color: var(--unity-colors-button-background);
    border-color: var(--unity-colors-button-border);
    color: var(--unity-colors-button-text);
}

.save-button:hover {
    background-color: var(--unity-colors-button-background-hover);
    border-color: var(--unity-colors-button-border-hover);
}

.save-button:active {
    background-color: var(--unity-colors-button-background-pressed);
    border-color: var(--unity-colors-button-border-pressed);
}

.test-button {
    background-color: var(--unity-colors-highlight-background);
    border-color: var(--unity-colors-highlight-background);
    color: var(--unity-colors-highlight-text);
}

.test-button:hover {
    background-color: var(--unity-colors-highlight-background-hover);
    border-color: var(--unity-colors-highlight-background-hover);
}

.test-button:active {
    background-color: var(--unity-colors-highlight-background-pressed);
    border-color: var(--unity-colors-highlight-background-pressed);
}

.test-button:disabled {
    background-color: var(--unity-colors-button-background-disabled);
    border-color: var(--unity-colors-button-border-disabled);
    color: var(--unity-colors-button-text-disabled);
}

/* Responsive Design */
@media (max-width: 600px) {
    .field-row {
        flex-direction: column;
    }
    
    .field-container.half-width {
        flex-basis: 100%;
        max-width: 100%;
        margin-right: 0;
        margin-bottom: 8px;
    }
    
    .button-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .button-right-group {
        flex-direction: column;
        align-items: stretch;
        margin-top: 8px;
    }
    
    .db-button {
        margin-left: 0;
        margin-bottom: 4px;
    }
    
    /* Unity UI Toolkit 不支持 :last-child 伪类选择器 */
    .db-button.last-item {
        margin-bottom: 0;
    }
}

/* Animation for loading indicator */
@keyframes spin {
    0% { rotate: 0deg; }
    100% { rotate: 360deg; }
}

.loading-indicator:not(.hidden) {
}