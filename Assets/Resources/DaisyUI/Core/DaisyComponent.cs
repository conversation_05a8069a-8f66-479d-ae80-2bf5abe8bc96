using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.DaisyUI.Core
{
    /// <summary>
    /// DaisyUI组件基类 - 所有DaisyUI组件的基础类
    /// 提供语义化的类名和链式调用支持
    /// </summary>
    public abstract class DaisyComponent : VisualElement
    {
        #region 属性和字段

        /// <summary>
        /// 组件类型名称
        /// </summary>
        public string ComponentType { get; protected set; }

        /// <summary>
        /// 修饰符类名集合
        /// </summary>
        public string[] ModifierClasses { get; protected set; }

        /// <summary>
        /// 当前主题
        /// </summary>
        public DaisyTheme Theme { get; protected set; }

        /// <summary>
        /// 是否禁用
        /// </summary>
        public bool IsDisabled { get; set; }

        /// <summary>
        /// 组件尺寸
        /// </summary>
        public string Size { get; set; } = "md";

        /// <summary>
        /// 组件变体
        /// </summary>
        public string Variant { get; set; } = "default";

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized { get; private set; }

        /// <summary>
        /// 组件模板路径（相对于Resources文件夹）
        /// </summary>
        protected abstract string TemplatePath { get; }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="componentType">组件类型名称</param>
        protected DaisyComponent(string componentType)
        {
            ComponentType = componentType;
            ModifierClasses = new string[0];

            // 添加基础类名
            AddToClassList($"daisy-{componentType}");

            // 获取当前主题
            Theme = DaisyTheme.Current;

            // 初始化组件
            Initialize();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void Initialize()
        {
            try
            {
                // 加载模板
                LoadTemplate();

                // 应用默认样式
                ApplyDefaultStyles();

                // 设置事件处理器
                SetupEventHandlers();

                // 执行自定义初始化
                OnInitialize();

                IsInitialized = true;

                if (DaisyUtilities.IsDebugMode)
                {
                    Logging.LogInfo("DaisyComponent", $"组件 {ComponentType} 初始化完成");
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("DaisyComponent", $"组件 {ComponentType} 初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用默认样式
        /// </summary>
        protected virtual void ApplyDefaultStyles()
        {
            // 添加基础类名 - 使用TailwindUss样式
            AddToClassList($"daisy-{ComponentType}");
            AddToClassList($"daisy-{ComponentType}-{Size}");
            AddToClassList($"daisy-{ComponentType}-{Variant}");

            // 应用默认可见性类
            AddToClassList("daisy-visible");

            // 应用主题
            if (Theme != null)
            {
                AddToClassList($"theme-{Theme.themeName}");
            }
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        protected virtual void SetupEventHandlers()
        {
            // 子类可以重写此方法来设置特定的事件处理器
        }

        /// <summary>
        /// 加载UXML模板
        /// </summary>
        protected virtual void LoadTemplate()
        {
            if (string.IsNullOrEmpty(TemplatePath))
            {
                if (DaisyUtilities.IsDebugMode)
                {
                    Logging.LogInfo("DaisyComponent", $"组件 {ComponentType} 未指定模板路径，跳过模板加载");
                }
                return;
            }

            try
            {
                // 从Resources文件夹加载UXML模板
                var template = Resources.Load<VisualTreeAsset>(TemplatePath);
                if (template != null)
                {
                    // 克隆模板内容
                    var templateContent = template.CloneTree();
                    
                    // 将模板内容添加到当前组件
                    Add(templateContent);

                    if (DaisyUtilities.IsDebugMode)
                    {
                        Logging.LogInfo("DaisyComponent", $"组件 {ComponentType} 模板加载成功: {TemplatePath}");
                    }
                }
                else
                {
                    if (DaisyUtilities.IsDebugMode)
                    {
                        Logging.LogWarning("DaisyComponent", $"组件 {ComponentType} 模板未找到: {TemplatePath}");
                    }
                }

                // 加载对应的样式文件
                LoadStyleSheet();
            }
            catch (System.Exception ex)
            {
                Logging.LogError("DaisyComponent", $"组件 {ComponentType} 模板加载失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载样式文件
        /// </summary>
        protected virtual void LoadStyleSheet()
        {
            if (string.IsNullOrEmpty(TemplatePath))
            {
                return;
            }

            try
            {
                // 从Resources文件夹加载样式文件
                var styleSheet = Resources.Load<StyleSheet>(TemplatePath);
                if (styleSheet != null)
                {
                    // 添加样式文件到当前组件
                    styleSheets.Add(styleSheet);

                    if (DaisyUtilities.IsDebugMode)
                    {
                        Logging.LogInfo("DaisyComponent", $"组件 {ComponentType} 样式文件加载成功: {TemplatePath}");
                    }
                }
                else
                {
                    if (DaisyUtilities.IsDebugMode)
                    {
                        Logging.LogInfo("DaisyComponent", $"组件 {ComponentType} 样式文件未找到: {TemplatePath}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("DaisyComponent", $"组件 {ComponentType} 样式文件加载失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 自定义初始化逻辑
        /// </summary>
        protected virtual void OnInitialize()
        {
            // 子类可以重写此方法来执行特定的初始化逻辑
        }

        #endregion

        #region 样式管理方法

        /// <summary>
        /// 设置修饰符
        /// </summary>
        /// <param name="modifier">修饰符名称</param>
        /// <param name="enabled">是否启用</param>
        public virtual DaisyComponent SetModifier(string modifier, bool enabled = true)
        {
            string className = $"daisy-{ComponentType}-{modifier}";

            if (enabled)
            {
                AddToClassList(className);
            }
            else
            {
                RemoveFromClassList(className);
            }

            return this;
        }

        /// <summary>
        /// 设置尺寸
        /// </summary>
        /// <param name="size">尺寸名称</param>
        public virtual DaisyComponent WithSize(string size)
        {
            // 移除当前尺寸类名
            RemoveFromClassList($"daisy-{ComponentType}-{Size}");

            // 更新尺寸
            Size = size;

            // 添加新的尺寸类名
            AddToClassList($"daisy-{ComponentType}-{Size}");

            return this;
        }

        /// <summary>
        /// 设置变体
        /// </summary>
        /// <param name="variant">变体名称</param>
        public virtual DaisyComponent WithVariant(string variant)
        {
            // 移除当前变体类名
            RemoveFromClassList($"daisy-{ComponentType}-{Variant}");

            // 更新变体
            Variant = variant;

            // 添加新的变体类名
            AddToClassList($"daisy-{ComponentType}-{Variant}");

            return this;
        }

        /// <summary>
        /// 应用主题
        /// </summary>
        /// <param name="theme">主题</param>
        public virtual DaisyComponent WithTheme(DaisyTheme theme)
        {
            // 移除当前主题类名
            if (Theme != null)
            {
                RemoveFromClassList($"theme-{Theme.themeName}");
            }

            // 更新主题
            Theme = theme;

            // 添加新的主题类名
            if (Theme != null)
            {
                AddToClassList($"theme-{Theme.themeName}");
                Theme.Apply(this);
            }

            return this;
        }

        /// <summary>
        /// 添加自定义类名
        /// </summary>
        /// <param name="className">类名</param>
        public virtual DaisyComponent AddClass(string className)
        {
            AddToClassList(className);
            return this;
        }

        /// <summary>
        /// 移除自定义类名
        /// </summary>
        /// <param name="className">类名</param>
        public virtual DaisyComponent RemoveClass(string className)
        {
            RemoveFromClassList(className);
            return this;
        }

        /// <summary>
        /// 切换类名
        /// </summary>
        /// <param name="className">类名</param>
        /// <param name="enabled">是否启用</param>
        public virtual DaisyComponent ToggleClass(string className, bool? enabled = null)
        {
            if (enabled.HasValue)
            {
                if (enabled.Value)
                    AddToClassList(className);
                else
                    RemoveFromClassList(className);
            }
            else
            {
                if (ClassListContains(className))
                    RemoveFromClassList(className);
                else
                    AddToClassList(className);
            }

            return this;
        }

        #endregion

        #region 状态管理方法

        /// <summary>
        /// 设置禁用状态
        /// </summary>
        /// <param name="disabled">是否禁用</param>
        public virtual DaisyComponent SetDisabled(bool disabled = true)
        {
            IsDisabled = disabled;
            SetModifier("disabled", disabled);

            // 更新交互状态
            pickingMode = disabled ? PickingMode.Ignore : PickingMode.Position;

            return this;
        }

        /// <summary>
        /// 设置可见性
        /// </summary>
        /// <param name="visible">是否可见</param>
        public virtual DaisyComponent SetVisible(bool visible = true)
        {
            if (visible)
            {
                style.display = DisplayStyle.Flex;
                RemoveFromClassList("daisy-hidden");
                AddToClassList("daisy-visible");
            }
            else
            {
                style.display = DisplayStyle.None;
                RemoveFromClassList("daisy-visible");
                AddToClassList("daisy-hidden");
            }
            return this;
        }

        /// <summary>
        /// 切换可见性
        /// </summary>
        public virtual DaisyComponent ToggleVisible()
        {
            bool isVisible = style.display != DisplayStyle.None;
            return SetVisible(!isVisible);
        }

        #endregion

        #region 工具方法

        /// <summary>
        /// 重置组件状态（用于对象池）
        /// </summary>
        public virtual void Reset()
        {
            // 清除所有自定义类名
            ClearClassList();

            // 重置基础属性
            Size = "md";
            Variant = "default";
            IsDisabled = false;

            // 重新应用默认样式
            ApplyDefaultStyles();

            // 重置样式 - 使用CSS类而不是内联样式
            AddToClassList("daisy-visible");
            pickingMode = PickingMode.Position;
        }

        /// <summary>
        /// 获取组件调试信息
        /// </summary>
        public virtual string GetDebugInfo()
        {
            var classes = string.Join(", ", GetClasses());
            return $"DaisyComponent[{ComponentType}] - Size: {Size}, Variant: {Variant}, Classes: [{classes}]";
        }

        /// <summary>
        /// 验证组件状态
        /// </summary>
        public virtual bool ValidateComponent()
        {
            if (string.IsNullOrEmpty(ComponentType))
            {
                Logging.LogError("DaisyComponent", "ComponentType 不能为空");
                return false;
            }

            if (!ClassListContains($"daisy-{ComponentType}"))
            {
                Logging.LogError("DaisyComponent", $"缺少基础类名: daisy-{ComponentType}");
                return false;
            }

            return true;
        }

        #endregion

        #region 链式调用支持

        /// <summary>
        /// 执行自定义操作
        /// </summary>
        /// <param name="action">操作</param>
        public virtual DaisyComponent With(System.Action<DaisyComponent> action)
        {
            action?.Invoke(this);
            return this;
        }

        /// <summary>
        /// 条件执行操作
        /// </summary>
        /// <param name="condition">条件</param>
        /// <param name="action">操作</param>
        public virtual DaisyComponent When(bool condition, System.Action<DaisyComponent> action)
        {
            if (condition)
            {
                action?.Invoke(this);
            }
            return this;
        }

        /// <summary>
        /// 条件执行操作（带else分支）
        /// </summary>
        /// <param name="condition">条件</param>
        /// <param name="trueAction">条件为真时的操作</param>
        /// <param name="falseAction">条件为假时的操作</param>
        public virtual DaisyComponent When(bool condition,
            System.Action<DaisyComponent> trueAction,
            System.Action<DaisyComponent> falseAction)
        {
            if (condition)
            {
                trueAction?.Invoke(this);
            }
            else
            {
                falseAction?.Invoke(this);
            }
            return this;
        }

        #endregion
    }
}