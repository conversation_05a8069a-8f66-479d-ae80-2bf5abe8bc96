/* DaisyUI Component Library - Main Stylesheet */
/* This file imports all component styles, themes, and utilities */

/* ========== Core Theme System ========== */
@import url("project://database/Assets/Resources/DaisyUI/Themes/themes.uss");

/* ========== Utility Classes ========== */
@import url("project://database/Assets/Resources/DaisyUI/Utilities/utilities.uss");

/* ========== Component Styles ========== */

/* Actions */
@import url("project://database/Assets/Resources/DaisyUI/Components/Actions/Button/DaisyButton.uss");

/* Data Display */
@import url("project://database/Assets/Resources/DaisyUI/Components/DataDisplay/Card/DaisyCard.uss");

/* Data Input */
@import url("project://database/Assets/Resources/DaisyUI/Components/DataInput/Input/DaisyInput.uss");
@import url("project://database/Assets/Resources/DaisyUI/Components/DataInput/Select/DaisySelect.uss");

/* Navigation */
@import url("project://database/Assets/Resources/DaisyUI/Components/Navigation/Dropdown/DaisyDropdown.uss");
@import url("project://database/Assets/Resources/DaisyUI/Components/Navigation/Modal/DaisyModal.uss");
@import url("project://database/Assets/Resources/DaisyUI/Components/Navigation/Tree/DaisyTree.uss");
@import url("project://database/Assets/Resources/DaisyUI/Components/Navigation/Tree/DaisyTreeItem.uss");

/* ========== Global Overrides ========== */
/* Any global styles or component overrides can be added here */

/* Unity UI Toolkit optimizations */
:root {
    --animation-duration: 0.2s;
    --ease-in-out: ease-in-out;
}


/* Component library version */
/* @version 1.0.0 */
/* @description DaisyUI components adapted for Unity UI Toolkit */