using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;
using BlastingDesign.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace BlastingDesign.UI.DaisyUI.Components.DataInput
{
    /// <summary>
    /// DaisyUI选择器组件
    /// 基于Unity DropdownField实现，提供DaisyUI样式和链式调用API
    /// </summary>
    public class DaisySelect : DaisyComponent
    {
        #region 私有字段

        private const string TEMPLATE_PATH = "DaisyUI/Components/DataInput/Select/DaisySelect";
        
        private DropdownField _dropdownField;
        private Label _label;
        private Label _helperText;
        private VisualElement _selectContainer;
        private List<string> _options;
        private bool _allowMultiple = false;
        private List<string> _selectedValues;

        #endregion

        #region 属性

        /// <summary>
        /// 选择器的值
        /// </summary>
        public string Value
        {
            get => _dropdownField?.value ?? string.Empty;
            set
            {
                if (_dropdownField != null && _options.Contains(value))
                {
                    _dropdownField.value = value;
                }
            }
        }

        /// <summary>
        /// 选择器标签
        /// </summary>
        public string Label
        {
            get => _label?.text ?? string.Empty;
            set
            {
                if (_label != null)
                {
                    _label.text = value ?? string.Empty;
                    _label.style.display = string.IsNullOrEmpty(value) ? DisplayStyle.None : DisplayStyle.Flex;
                }
            }
        }

        /// <summary>
        /// 选项列表
        /// </summary>
        public List<string> Options
        {
            get => _options ?? new List<string>();
            set
            {
                _options = value ?? new List<string>();
                UpdateOptions();
            }
        }

        /// <summary>
        /// 帮助文本
        /// </summary>
        public string HelperText
        {
            get => _helperText?.text ?? string.Empty;
            set
            {
                if (_helperText != null)
                {
                    _helperText.text = value ?? string.Empty;
                    _helperText.style.display = string.IsNullOrEmpty(value) ? DisplayStyle.None : DisplayStyle.Flex;
                }
            }
        }

        /// <summary>
        /// 是否允许多选
        /// </summary>
        public bool AllowMultiple
        {
            get => _allowMultiple;
            set
            {
                _allowMultiple = value;
                if (value && _selectedValues == null)
                {
                    _selectedValues = new List<string>();
                }
            }
        }

        /// <summary>
        /// 多选时的选中值列表
        /// </summary>
        public List<string> SelectedValues
        {
            get => _selectedValues ?? new List<string>();
            set => _selectedValues = value ?? new List<string>();
        }

        /// <summary>
        /// 选中的索引
        /// </summary>
        public int SelectedIndex
        {
            get => _dropdownField?.index ?? -1;
            set
            {
                if (_dropdownField != null && value >= 0 && value < _options.Count)
                {
                    _dropdownField.index = value;
                }
            }
        }

        /// <summary>
        /// 模板路径
        /// </summary>
        protected override string TemplatePath => TEMPLATE_PATH;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="options">选项列表</param>
        public DaisySelect(List<string> options = null) : base("select")
        {
            _options = options ?? new List<string>();
            _selectedValues = new List<string>();
            CreateSelectStructure();
        }

        #endregion

        #region 静态工厂方法

        /// <summary>
        /// 创建选择器组件
        /// </summary>
        /// <param name="options">选项列表</param>
        /// <returns>DaisySelect实例</returns>
        public static DaisySelect Create(List<string> options = null)
        {
            return new DaisySelect(options);
        }

        /// <summary>
        /// 创建选择器组件
        /// </summary>
        /// <param name="options">选项数组</param>
        /// <returns>DaisySelect实例</returns>
        public static DaisySelect Create(params string[] options)
        {
            return new DaisySelect(options?.ToList());
        }

        /// <summary>
        /// 创建带标签的选择器
        /// </summary>
        /// <param name="label">标签文本</param>
        /// <param name="options">选项列表</param>
        /// <returns>DaisySelect实例</returns>
        public static DaisySelect Create(string label, List<string> options)
        {
            var select = new DaisySelect(options);
            select.SetLabel(label);
            return select;
        }

        /// <summary>
        /// 创建带标签的选择器
        /// </summary>
        /// <param name="label">标签文本</param>
        /// <param name="options">选项数组</param>
        /// <returns>DaisySelect实例</returns>
        public static DaisySelect Create(string label, params string[] options)
        {
            var select = new DaisySelect(options?.ToList());
            select.SetLabel(label);
            return select;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 创建选择器结构
        /// </summary>
        private void CreateSelectStructure()
        {
            // 创建标签
            _label = new Label();
            _label.AddToClassList("daisy-select-label");
            _label.style.display = DisplayStyle.None; // 默认隐藏
            Add(_label);

            // 创建选择器容器
            _selectContainer = new VisualElement();
            _selectContainer.AddToClassList("daisy-select-container");
            Add(_selectContainer);

            // 创建下拉框
            _dropdownField = new DropdownField();
            _dropdownField.AddToClassList("daisy-select-field");
            _selectContainer.Add(_dropdownField);

            // 创建帮助文本
            _helperText = new Label();
            _helperText.AddToClassList("daisy-select-helper");
            _helperText.style.display = DisplayStyle.None; // 默认隐藏
            Add(_helperText);

            // 更新选项
            UpdateOptions();

            // 设置事件处理
            SetupEventHandlers();
        }

        /// <summary>
        /// 更新选项列表
        /// </summary>
        private void UpdateOptions()
        {
            if (_dropdownField != null && _options != null)
            {
                _dropdownField.choices = _options;

                // 如果有选项且当前没有选中值，选中第一个
                if (_options.Count > 0 && string.IsNullOrEmpty(_dropdownField.value))
                {
                    _dropdownField.index = 0;
                }
            }
        }

        /// <summary>
        /// 设置事件处理
        /// </summary>
        private void SetupEventHandlers()
        {
            if (_dropdownField != null)
            {
                _dropdownField.RegisterValueChangedCallback(OnValueChanged);
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 值改变事件
        /// </summary>
        private void OnValueChanged(ChangeEvent<string> evt)
        {
            if (_allowMultiple)
            {
                // 多选逻辑
                if (_selectedValues.Contains(evt.newValue))
                {
                    _selectedValues.Remove(evt.newValue);
                }
                else
                {
                    _selectedValues.Add(evt.newValue);
                }

                // 更新显示文本
                UpdateMultiSelectDisplay();
            }
        }

        /// <summary>
        /// 更新多选显示
        /// </summary>
        private void UpdateMultiSelectDisplay()
        {
            if (_allowMultiple && _selectedValues.Count > 0)
            {
                // 这里需要自定义显示逻辑，因为Unity的DropdownField不直接支持多选
                Logging.LogInfo("DaisySelect", $"多选值: {string.Join(", ", _selectedValues)}");
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置标签
        /// </summary>
        /// <param name="label">标签文本</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect SetLabel(string label)
        {
            Label = label;
            return this;
        }

        /// <summary>
        /// 设置选项
        /// </summary>
        /// <param name="options">选项列表</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect SetOptions(List<string> options)
        {
            Options = options;
            return this;
        }

        /// <summary>
        /// 设置选项
        /// </summary>
        /// <param name="options">选项数组</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect SetOptions(params string[] options)
        {
            Options = options?.ToList();
            return this;
        }

        /// <summary>
        /// 添加选项
        /// </summary>
        /// <param name="option">选项</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect AddOption(string option)
        {
            if (!string.IsNullOrEmpty(option) && !_options.Contains(option))
            {
                _options.Add(option);
                UpdateOptions();
            }
            return this;
        }

        /// <summary>
        /// 移除选项
        /// </summary>
        /// <param name="option">选项</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect RemoveOption(string option)
        {
            if (_options.Contains(option))
            {
                _options.Remove(option);
                UpdateOptions();
            }
            return this;
        }

        /// <summary>
        /// 设置帮助文本
        /// </summary>
        /// <param name="text">帮助文本</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect SetHelperText(string text)
        {
            HelperText = text;
            return this;
        }

        /// <summary>
        /// 设置选中值
        /// </summary>
        /// <param name="value">选中值</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect SetValue(string value)
        {
            Value = value;
            return this;
        }

        /// <summary>
        /// 设置选中索引
        /// </summary>
        /// <param name="index">选中索引</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect SetSelectedIndex(int index)
        {
            SelectedIndex = index;
            return this;
        }

        /// <summary>
        /// 注册值改变回调
        /// </summary>
        /// <param name="callback">回调函数</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect OnValueChanged(Action<string> callback)
        {
            if (_dropdownField != null && callback != null)
            {
                _dropdownField.RegisterValueChangedCallback(evt => callback(evt.newValue));
            }
            return this;
        }

        #endregion

        #region 修饰符方法

        /// <summary>
        /// 设置边框样式
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect SetBordered()
        {
            return (DaisySelect)SetModifier("bordered");
        }

        /// <summary>
        /// 设置幽灵样式
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect SetGhost()
        {
            return (DaisySelect)SetModifier("ghost");
        }

        /// <summary>
        /// 设置错误状态
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect SetError()
        {
            return (DaisySelect)SetModifier("error");
        }

        /// <summary>
        /// 设置成功状态
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect SetSuccess()
        {
            return (DaisySelect)SetModifier("success");
        }

        /// <summary>
        /// 设置警告状态
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect SetWarning()
        {
            return (DaisySelect)SetModifier("warning");
        }

        /// <summary>
        /// 设置信息状态
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect SetInfo()
        {
            return (DaisySelect)SetModifier("info");
        }

        /// <summary>
        /// 设置禁用状态
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect SetDisabled()
        {
            if (_dropdownField != null)
            {
                _dropdownField.SetEnabled(false);
            }
            return (DaisySelect)SetModifier("disabled");
        }

        /// <summary>
        /// 设置多选模式
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisySelect SetMultiple()
        {
            AllowMultiple = true;
            return (DaisySelect)SetModifier("multiple");
        }

        #endregion

        #region 重写方法

        /// <summary>
        /// 组件特定的初始化
        /// </summary>
        protected override void OnInitialize()
        {
            Logging.LogInfo("DaisySelect", "选择器组件初始化完成");
        }

        /// <summary>
        /// 设置尺寸（重写基类方法以返回正确类型）
        /// </summary>
        /// <param name="size">尺寸名称</param>
        /// <returns>当前实例，支持链式调用</returns>
        public new DaisySelect WithSize(string size)
        {
            base.WithSize(size);
            return this;
        }

        #endregion
    }
}
