namespace BlastingDesign.UI.DaisyUI.Components.DataInput
{
    /// <summary>
    /// DaisyUI输入框组件 - 尺寸相关部分
    /// 包含所有尺寸变体的链式调用方法
    /// </summary>
    public partial class DaisyInput
    {
        #region 尺寸方法

        /// <summary>
        /// 设置超小尺寸
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput ExtraSmall()
        {
            return WithSize("xs");
        }

        /// <summary>
        /// 设置小尺寸
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput Small()
        {
            return WithSize("sm");
        }

        /// <summary>
        /// 设置中等尺寸
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput Medium()
        {
            return WithSize("md");
        }

        /// <summary>
        /// 设置大尺寸
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput Large()
        {
            return WithSize("lg");
        }

        /// <summary>
        /// 设置超大尺寸
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput ExtraLarge()
        {
            return WithSize("xl");
        }


        /// <summary>
        /// 设置尺寸（重写基类方法以返回正确类型）
        /// </summary>
        /// <param name="size">尺寸名称</param>
        /// <returns>当前实例，支持链式调用</returns>
        public new DaisyInput WithSize(string size)
        {
            _currentSize = size;
            base.WithSize(size);
            return this;
        }

        #endregion
    }
}