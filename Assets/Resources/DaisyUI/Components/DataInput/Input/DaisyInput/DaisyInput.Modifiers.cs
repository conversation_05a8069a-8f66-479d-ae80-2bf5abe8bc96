namespace BlastingDesign.UI.DaisyUI.Components.DataInput
{
    /// <summary>
    /// DaisyUI输入框组件 - 修饰符和状态部分
    /// 包含各种修饰符、颜色变体和状态管理方法
    /// </summary>
    public partial class DaisyInput
    {
        #region 修饰符方法

        /// <summary>
        /// 设置边框样式
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput SetBordered()
        {
            return (DaisyInput)SetModifier("bordered");
        }

        /// <summary>
        /// 设置幽灵样式
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput SetGhost()
        {
            return (DaisyInput)SetModifier("ghost");
        }

        /// <summary>
        /// 设置为连接项（用于输入组）
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput JoinItem()
        {
            return (DaisyInput)SetModifier("join-item");
        }

        /// <summary>
        /// 设置为可增长输入框
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput Grow()
        {
            return (DaisyInput)SetModifier("grow");
        }

        #endregion

        #region 颜色变体方法

        /// <summary>
        /// 设置中性色彩
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput Neutral()
        {
            return (DaisyInput)WithVariant("neutral");
        }

        /// <summary>
        /// 设置主要色彩
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput Primary()
        {
            return (DaisyInput)WithVariant("primary");
        }

        /// <summary>
        /// 设置次要色彩
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput Secondary()
        {
            return (DaisyInput)WithVariant("secondary");
        }

        /// <summary>
        /// 设置强调色彩
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput Accent()
        {
            return (DaisyInput)WithVariant("accent");
        }

        #endregion

        #region 状态方法

        /// <summary>
        /// 设置错误状态
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput Error()
        {
            _currentState = "error";
            UpdateState();
            return this;
        }

        /// <summary>
        /// 设置成功状态
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput Success()
        {
            _currentState = "success";
            UpdateState();
            return this;
        }

        /// <summary>
        /// 设置警告状态
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput Warning()
        {
            _currentState = "warning";
            UpdateState();
            return this;
        }

        /// <summary>
        /// 设置信息状态
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput Info()
        {
            _currentState = "info";
            UpdateState();
            return this;
        }

        /// <summary>
        /// 设置禁用状态
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput Disabled()
        {
            IsDisabled = true;
            return this;
        }

        /// <summary>
        /// 设置只读状态
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput ReadOnly()
        {
            IsReadOnly = true;
            return this;
        }

        /// <summary>
        /// 清除所有状态
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput ClearState()
        {
            _currentState = "normal";
            UpdateState();
            return this;
        }

        #endregion

        #region 状态别名方法

        /// <summary>
        /// 设置错误状态（别名）
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput SetError()
        {
            return Error();
        }

        /// <summary>
        /// 设置成功状态（别名）
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput SetSuccess()
        {
            return Success();
        }

        /// <summary>
        /// 设置警告状态（别名）
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput SetWarning()
        {
            return Warning();
        }

        /// <summary>
        /// 设置信息状态（别名）
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput SetInfo()
        {
            return Info();
        }

        /// <summary>
        /// 设置禁用状态（别名）
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput SetDisabled()
        {
            return Disabled();
        }

        /// <summary>
        /// 设置只读状态（别名）
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput SetReadOnly()
        {
            return ReadOnly();
        }

        #endregion
    }
}