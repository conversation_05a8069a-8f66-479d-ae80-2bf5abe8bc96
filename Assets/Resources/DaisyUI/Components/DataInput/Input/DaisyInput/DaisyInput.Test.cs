using UnityEngine;
using UnityEngine.UIElements;

namespace BlastingDesign.UI.DaisyUI.Components.DataInput
{
    /// <summary>
    /// DaisyInput组件的简单测试验证
    /// 仅用于验证新的占位符实现是否正常工作
    /// </summary>
    public static class DaisyInputTest
    {
        /// <summary>
        /// 验证新的占位符实现
        /// </summary>
        public static void TestPlaceholderImplementation()
        {
            // 测试默认构造函数
            var input1 = new DaisyInput();
            Debug.Log($"默认构造函数: Placeholder = '{input1.Placeholder}'");
            
            // 测试带占位符的构造函数
            var input2 = new DaisyInput("请输入内容");
            Debug.Log($"带占位符构造函数: Placeholder = '{input2.Placeholder}'");
            
            // 测试属性设置
            input1.Placeholder = "新的占位符";
            Debug.Log($"属性设置后: Placeholder = '{input1.Placeholder}'");
            
            // 测试链式调用
            var input3 = new DaisyInput()
                .SetPlaceholder("链式调用占位符")
                .SetPlaceholderFocusBehavior(true);
            Debug.Log($"链式调用: Placeholder = '{input3.Placeholder}', HidePlaceholderOnFocus = {input3.HidePlaceholderOnFocus}");
            
            Debug.Log("DaisyInput 占位符重构验证完成！");
        }
    }
}