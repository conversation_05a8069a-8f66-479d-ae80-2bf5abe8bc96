using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;

namespace BlastingDesign.UI.DaisyUI.Components.DataInput
{
    /// <summary>
    /// DaisyUI输入框组件
    /// 基于Unity TextField实现，提供DaisyUI样式和链式调用API
    /// 支持多种输入类型、状态管理和样式变体
    /// </summary>
    [UxmlElement("DaisyInput")]
    public partial class DaisyInput : DaisyComponent
    {
        // 此文件作为部分类的主要定义文件
        // 具体实现分散在以下部分类文件中：
        // - DaisyInput.Core.cs: 核心功能和属性
        // - DaisyInput.Initialization.cs: 初始化和模板加载
        // - DaisyInput.Sizes.cs: 尺寸相关方法
        // - DaisyInput.Factory.cs: 静态工厂方法
        // - DaisyInput.Modifiers.cs: 修饰符和状态方法
        // - DaisyInput.Methods.cs: 公共方法和事件处理
    }
}