using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;
using BlastingDesign.Utils;
using System;

namespace BlastingDesign.UI.DaisyUI.Components.Navigation
{
    /// <summary>
    /// DaisyUI模态框组件
    /// 提供模态对话框功能，支持标题、内容、操作按钮等
    /// </summary>
    public class DaisyModal : DaisyComponent
    {
        #region 私有字段

        private const string TEMPLATE_PATH = "DaisyUI/Components/Navigation/Modal/DaisyModal";
        
        private VisualElement _modalBackdrop;
        private VisualElement _modalBox;
        private VisualElement _modalHeader;
        private VisualElement _modalBody;
        private VisualElement _modalActions;
        private Label _modalTitle;
        private Button _closeButton;
        private VisualElement _modalContent;
        private bool _isOpen = false;
        private bool _closeOnBackdropClick = true;
        private bool _closeOnEscape = true;

        #endregion

        #region 属性

        /// <summary>
        /// 模态框是否开启
        /// </summary>
        public bool IsOpen
        {
            get => _isOpen;
            set
            {
                _isOpen = value;
                UpdateVisibility();
            }
        }

        /// <summary>
        /// 模态框标题
        /// </summary>
        public string Title
        {
            get => _modalTitle?.text ?? string.Empty;
            set
            {
                if (_modalTitle != null)
                {
                    _modalTitle.text = value ?? string.Empty;
                    if (string.IsNullOrEmpty(value))
                    {
                        _modalHeader.AddToClassList("daisy-hidden");
                        _modalHeader.RemoveFromClassList("daisy-visible");
                    }
                    else
                    {
                        _modalHeader.RemoveFromClassList("daisy-hidden");
                        _modalHeader.AddToClassList("daisy-visible");
                    }
                }
            }
        }

        /// <summary>
        /// 是否点击背景关闭
        /// </summary>
        public bool CloseOnBackdropClick
        {
            get => _closeOnBackdropClick;
            set => _closeOnBackdropClick = value;
        }

        /// <summary>
        /// 是否按ESC键关闭
        /// </summary>
        public bool CloseOnEscape
        {
            get => _closeOnEscape;
            set => _closeOnEscape = value;
        }

        /// <summary>
        /// 模态框内容区域
        /// </summary>
        public VisualElement ModalContent => _modalContent;

        /// <summary>
        /// 模态框操作区域
        /// </summary>
        public VisualElement ModalActions => _modalActions;

        /// <summary>
        /// 模板路径
        /// </summary>
        protected override string TemplatePath => TEMPLATE_PATH;

        #endregion

        #region 事件

        /// <summary>
        /// 模态框打开时触发
        /// </summary>
        public event Action OnOpen;

        /// <summary>
        /// 模态框关闭时触发
        /// </summary>
        public event Action OnClose;

        /// <summary>
        /// 模态框确认时触发
        /// </summary>
        public event Action OnConfirm;

        /// <summary>
        /// 模态框取消时触发
        /// </summary>
        public event Action OnCancel;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public DaisyModal() : base("modal")
        {
            CreateModalStructure();
            SetupEventHandlers();
        }

        #endregion

        #region 静态工厂方法

        /// <summary>
        /// 创建模态框组件
        /// </summary>
        /// <returns>DaisyModal实例</returns>
        public static DaisyModal Create()
        {
            return new DaisyModal();
        }

        /// <summary>
        /// 创建带标题的模态框
        /// </summary>
        /// <param name="title">模态框标题</param>
        /// <returns>DaisyModal实例</returns>
        public static DaisyModal Create(string title)
        {
            var modal = new DaisyModal();
            modal.SetTitle(title);
            return modal;
        }

        /// <summary>
        /// 创建确认对话框
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="message">消息内容</param>
        /// <param name="onConfirm">确认回调</param>
        /// <param name="onCancel">取消回调</param>
        /// <returns>DaisyModal实例</returns>
        public static DaisyModal CreateConfirmDialog(string title, string message, Action onConfirm = null, Action onCancel = null)
        {
            var modal = new DaisyModal();
            modal.SetTitle(title);
            modal.SetContent(message);
            
            // 添加确认和取消按钮
            var confirmButton = new Button(() =>
            {
                onConfirm?.Invoke();
                modal.Close();
            })
            {
                text = "确认"
            };
            confirmButton.AddToClassList("daisy-btn");
            confirmButton.AddToClassList("daisy-btn-primary");
            
            var cancelButton = new Button(() =>
            {
                onCancel?.Invoke();
                modal.Close();
            })
            {
                text = "取消"
            };
            cancelButton.AddToClassList("daisy-btn");
            cancelButton.AddToClassList("daisy-btn-ghost");
            
            modal.AddActions(cancelButton, confirmButton);
            return modal;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 创建模态框结构
        /// </summary>
        private void CreateModalStructure()
        {
            // 创建背景遮罩
            _modalBackdrop = new VisualElement();
            _modalBackdrop.AddToClassList("daisy-modal-backdrop");
            Add(_modalBackdrop);

            // 创建模态框容器
            _modalBox = new VisualElement();
            _modalBox.AddToClassList("daisy-modal-box");
            _modalBackdrop.Add(_modalBox);

            // 创建头部区域
            _modalHeader = new VisualElement();
            _modalHeader.AddToClassList("daisy-modal-header");
            _modalHeader.AddToClassList("daisy-hidden"); // 默认隐藏
            _modalBox.Add(_modalHeader);

            // 创建标题
            _modalTitle = new Label();
            _modalTitle.AddToClassList("daisy-modal-title");
            _modalHeader.Add(_modalTitle);

            // 创建关闭按钮
            _closeButton = new Button(() => Close())
            {
                text = "✕"
            };
            _closeButton.AddToClassList("daisy-modal-close");
            _closeButton.AddToClassList("daisy-btn");
            _closeButton.AddToClassList("daisy-btn-sm");
            _closeButton.AddToClassList("daisy-btn-circle");
            _closeButton.AddToClassList("daisy-btn-ghost");
            _modalHeader.Add(_closeButton);

            // 创建内容区域
            _modalBody = new VisualElement();
            _modalBody.AddToClassList("daisy-modal-body");
            _modalBox.Add(_modalBody);

            // 创建内容容器
            _modalContent = new VisualElement();
            _modalContent.AddToClassList("daisy-modal-content");
            _modalBody.Add(_modalContent);

            // 创建操作区域
            _modalActions = new VisualElement();
            _modalActions.AddToClassList("daisy-modal-actions");
            _modalActions.AddToClassList("daisy-hidden"); // 默认隐藏
            _modalBox.Add(_modalActions);

            // 默认不显示
            AddToClassList("daisy-hidden");
        }

        /// <summary>
        /// 设置事件处理
        /// </summary>
        private new void SetupEventHandlers()
        {
            // 背景点击事件
            if (_modalBackdrop != null)
            {
                _modalBackdrop.RegisterCallback<ClickEvent>(OnBackdropClick);
            }

            // 阻止模态框点击事件冒泡
            if (_modalBox != null)
            {
                _modalBox.RegisterCallback<ClickEvent>(evt => evt.StopPropagation());
            }

            // 键盘事件
            RegisterCallback<KeyDownEvent>(OnKeyDown);
        }

        /// <summary>
        /// 更新可见性
        /// </summary>
        private void UpdateVisibility()
        {
            if (_isOpen)
            {
                RemoveFromClassList("daisy-hidden");
                AddToClassList("daisy-visible");
                AddToClassList("daisy-modal-open");
                Focus();
                OnOpen?.Invoke();
            }
            else
            {
                AddToClassList("daisy-hidden");
                RemoveFromClassList("daisy-visible");
                RemoveFromClassList("daisy-modal-open");
                OnClose?.Invoke();
            }
        }

        /// <summary>
        /// 确保操作区域存在
        /// </summary>
        private void EnsureActionsContainer()
        {
            if (_modalActions != null)
            {
                _modalActions.RemoveFromClassList("daisy-hidden");
                _modalActions.AddToClassList("daisy-visible");
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 背景点击事件
        /// </summary>
        private void OnBackdropClick(ClickEvent evt)
        {
            if (_closeOnBackdropClick && _isOpen)
            {
                Close();
            }
        }

        /// <summary>
        /// 键盘事件
        /// </summary>
        private void OnKeyDown(KeyDownEvent evt)
        {
            if (_closeOnEscape && _isOpen && evt.keyCode == KeyCode.Escape)
            {
                Close();
                evt.StopPropagation();
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 打开模态框
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal Open()
        {
            IsOpen = true;
            return this;
        }

        /// <summary>
        /// 关闭模态框
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal Close()
        {
            IsOpen = false;
            return this;
        }

        /// <summary>
        /// 切换模态框开启状态
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal Toggle()
        {
            IsOpen = !IsOpen;
            return this;
        }

        /// <summary>
        /// 设置标题
        /// </summary>
        /// <param name="title">标题文本</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal SetTitle(string title)
        {
            Title = title;
            return this;
        }

        /// <summary>
        /// 设置内容
        /// </summary>
        /// <param name="content">内容文本</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal SetContent(string content)
        {
            var label = new Label(content);
            label.AddToClassList("daisy-modal-text");
            return AddContent(label);
        }

        /// <summary>
        /// 添加内容元素
        /// </summary>
        /// <param name="content">内容元素</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal AddContent(VisualElement content)
        {
            if (content != null)
            {
                _modalContent.Add(content);
            }
            return this;
        }

        /// <summary>
        /// 清空内容
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal ClearContent()
        {
            _modalContent.Clear();
            return this;
        }

        /// <summary>
        /// 添加操作按钮
        /// </summary>
        /// <param name="actions">操作按钮数组</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal AddActions(params VisualElement[] actions)
        {
            EnsureActionsContainer();

            foreach (var action in actions)
            {
                if (action != null)
                {
                    _modalActions.Add(action);
                }
            }
            return this;
        }

        /// <summary>
        /// 清空操作按钮
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal ClearActions()
        {
            _modalActions.Clear();
            _modalActions.AddToClassList("daisy-hidden");
            _modalActions.RemoveFromClassList("daisy-visible");
            return this;
        }

        /// <summary>
        /// 设置关闭行为
        /// </summary>
        /// <param name="closeOnBackdrop">点击背景关闭</param>
        /// <param name="closeOnEscape">ESC键关闭</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal SetCloseOptions(bool closeOnBackdrop = true, bool closeOnEscape = true)
        {
            CloseOnBackdropClick = closeOnBackdrop;
            CloseOnEscape = closeOnEscape;
            return this;
        }

        /// <summary>
        /// 注册打开事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal OnOpenEvent(Action callback)
        {
            OnOpen += callback;
            return this;
        }

        /// <summary>
        /// 注册关闭事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal OnCloseEvent(Action callback)
        {
            OnClose += callback;
            return this;
        }

        /// <summary>
        /// 注册确认事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal OnConfirmEvent(Action callback)
        {
            OnConfirm += callback;
            return this;
        }

        /// <summary>
        /// 注册取消事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal OnCancelEvent(Action callback)
        {
            OnCancel += callback;
            return this;
        }

        #endregion

        #region 修饰符方法

        /// <summary>
        /// 设置小尺寸
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal SetSmall()
        {
            return (DaisyModal)SetModifier("sm");
        }

        /// <summary>
        /// 设置大尺寸
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal SetLarge()
        {
            return (DaisyModal)SetModifier("lg");
        }

        /// <summary>
        /// 设置全屏
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal SetFullscreen()
        {
            return (DaisyModal)SetModifier("fullscreen");
        }

        /// <summary>
        /// 设置居中
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal SetCentered()
        {
            return (DaisyModal)SetModifier("centered");
        }

        /// <summary>
        /// 设置底部显示
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyModal SetBottom()
        {
            return (DaisyModal)SetModifier("bottom");
        }

        #endregion

        #region 重写方法

        /// <summary>
        /// 组件特定的初始化
        /// </summary>
        protected override void OnInitialize()
        {
            Logging.LogInfo("DaisyModal", "模态框组件初始化完成");
        }

        /// <summary>
        /// 清理事件
        /// </summary>
        public void Dispose()
        {
            OnOpen = null;
            OnClose = null;
            OnConfirm = null;
            OnCancel = null;
        }

        #endregion
    }
}