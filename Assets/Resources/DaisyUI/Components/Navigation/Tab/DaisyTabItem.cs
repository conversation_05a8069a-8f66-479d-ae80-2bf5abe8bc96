using System;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;

namespace BlastingDesign.UI.DaisyUI.Components.Navigation
{
    [UxmlElement("DaisyTabItem")]
    public partial class DaisyTabItem : DaisyComponent
    {

        #region Properties
        private string _id = "";
        private string _label = "Tab";
        private bool _active = false;
        private bool _disabled = false;

        [UxmlAttribute("id")]
        public string Id
        {
            get => _id;
            set
            {
                _id = value;
                userData = value;
            }
        }

        [UxmlAttribute("label")]
        public string Label
        {
            get => _label;
            set
            {
                if (_label != value)
                {
                    _label = value;
                    UpdateLabel();
                }
            }
        }

        [UxmlAttribute("active")]
        public bool Active
        {
            get => _active;
            set
            {
                if (_active != value)
                {
                    _active = value;
                    UpdateActiveState();
                }
            }
        }

        [UxmlAttribute("disabled")]
        public bool Disabled
        {
            get => _disabled;
            set
            {
                if (_disabled != value)
                {
                    _disabled = value;
                    UpdateDisabledState();
                }
            }
        }
        #endregion

        #region Elements
        private Label _labelElement;
        #endregion

        #region Events
        public event Action OnClicked;
        #endregion

        #region Constructors
        public DaisyTabItem() : this("", "Tab")
        {
        }

        public DaisyTabItem(string id, string label) : base("tab-item")
        {
            _id = id;
            _label = label;
        }
        #endregion

        #region Initialization
        protected override string TemplatePath => "";

        protected override void OnInitialize()
        {
            CreateElements();
            RegisterCallbacks();
        }

        private void CreateElements()
        {
            _labelElement = new Label(_label);
            _labelElement.AddToClassList("daisy-tab-item-label");
            Add(_labelElement);
            
            userData = _id;
        }

        private void RegisterCallbacks()
        {
            RegisterCallback<ClickEvent>(OnClick);
            RegisterCallback<MouseEnterEvent>(OnMouseEnter);
            RegisterCallback<MouseLeaveEvent>(OnMouseLeave);
        }
        #endregion

        #region Public Methods
        public DaisyTabItem SetActive(bool active)
        {
            Active = active;
            return this;
        }

        public new DaisyTabItem SetDisabled(bool disabled)
        {
            Disabled = disabled;
            return this;
        }

        public DaisyTabItem SetLabel(string label)
        {
            Label = label;
            return this;
        }

        public DaisyTabItem OnClick(Action callback)
        {
            OnClicked += callback;
            return this;
        }
        #endregion

        #region Event Handlers
        private void OnClick(ClickEvent evt)
        {
            if (_disabled) return;
            
            OnClicked?.Invoke();
            evt.StopPropagation();
        }

        private void OnMouseEnter(MouseEnterEvent evt)
        {
            if (_disabled) return;
            AddToClassList("daisy-tab-item-hover");
        }

        private void OnMouseLeave(MouseLeaveEvent evt)
        {
            RemoveFromClassList("daisy-tab-item-hover");
        }
        #endregion

        #region Private Methods
        private void UpdateLabel()
        {
            if (_labelElement != null)
            {
                _labelElement.text = _label;
            }
        }

        private void UpdateActiveState()
        {
            if (_active)
            {
                AddToClassList("daisy-tab-item-active");
            }
            else
            {
                RemoveFromClassList("daisy-tab-item-active");
            }
        }

        private void UpdateDisabledState()
        {
            if (_disabled)
            {
                AddToClassList("daisy-tab-item-disabled");
            }
            else
            {
                RemoveFromClassList("daisy-tab-item-disabled");
            }
            
            SetEnabled(!_disabled);
        }
        #endregion
    }
}