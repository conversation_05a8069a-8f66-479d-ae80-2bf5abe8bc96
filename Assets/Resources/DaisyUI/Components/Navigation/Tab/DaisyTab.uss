/* DaisyUI Tab Component Styles */

/* Base Tab Container */
.daisy-tabs {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    width: 100%;
}

.daisy-tab-container {
    display: flex;
    position: relative;
}

.daisy-tab-content-container {
    display: flex;
    flex: 1;
    min-height: 200px;
    position: relative;
}

/* Direction Variants */
.daisy-tabs-row {
    flex-direction: column;
}

.daisy-tabs-row .daisy-tab-container {
    flex-direction: row;
    align-items: flex-end;
}

.daisy-tabs-column {
    flex-direction: row;
}

.daisy-tabs-column .daisy-tab-container {
    flex-direction: column;
    align-items: stretch;
    width: auto;
    min-width: 120px;
}

/* Tab Item Base Styles */
.daisy-tab-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    margin: 0;
    background-color: rgba(96, 96, 96, 1);
    border-width: 1px;
    border-radius: 3px 3px 0 0;
    position: relative;
    min-height: 32px;
    white-space: nowrap;
    border-color: rgba(128, 128, 128, 0.3);
    border-bottom-width: 0;
    transition-duration: 0.1s;
    transition-property: background-color, border-color;
}

.daisy-tab-item-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    -unity-font-style: normal;
    margin: 0;
    padding: 0;
    -unity-text-align: middle-center;
}

/* Tab Item States */
.daisy-tab-item:hover {
    background-color: rgba(112, 112, 112, 1);
}

.daisy-tab-item:hover .daisy-tab-item-label {
    color: rgba(255, 255, 255, 1);
}

.daisy-tab-item-active {
    background-color: rgba(64, 64, 64, 1);
    border-color: rgba(128, 128, 128, 0.5);
    border-bottom-width: 1px;
    border-bottom-color: rgba(64, 64, 64, 1);
}

.daisy-tab-item-active .daisy-tab-item-label {
    color: rgba(255, 255, 255, 1);
    -unity-font-style: normal;
}

.daisy-tab-item-disabled {
    opacity: 0.5;
}

.daisy-tab-item-disabled .daisy-tab-item-label {
    color: var(--base-content, #1f2937);
}

/* Tab Panel */
.daisy-tab-panel {
    flex: 1;
    padding: 16px;
    background-color: rgba(64, 64, 64, 1);
    border-width: 1px;
    border-radius: 0 3px 3px 3px;
    min-height: 200px;
    border-color: rgba(128, 128, 128, 0.5);
}

/* Size Variants */
.daisy-tabs-extrasmall .daisy-tab-item {
    padding: 3px 6px;
    min-height: 20px;
}

.daisy-tabs-extrasmall .daisy-tab-item-label {
    font-size: 10px;
}

.daisy-tabs-small .daisy-tab-item {
    padding: 5px 10px;
    min-height: 24px;
}

.daisy-tabs-small .daisy-tab-item-label {
    font-size: 11px;
}

.daisy-tabs-medium .daisy-tab-item {
    padding: 8px 16px;
    min-height: 32px;
}

.daisy-tabs-medium .daisy-tab-item-label {
    font-size: 12px;
}

.daisy-tabs-large .daisy-tab-item {
    padding: 10px 18px;
    min-height: 36px;
}

.daisy-tabs-large .daisy-tab-item-label {
    font-size: 13px;
}

.daisy-tabs-extralarge .daisy-tab-item {
    padding: 12px 20px;
    min-height: 40px;
}

.daisy-tabs-extralarge .daisy-tab-item-label {
    font-size: 14px;
}

/* Bordered Variant */
.daisy-tabs-bordered .daisy-tab-container {
    border-bottom-width: 1px;
    border-bottom-color: rgba(128, 128, 128, 0.3);
}

.daisy-tabs-bordered .daisy-tab-item {
    border-radius: 0;
    margin-bottom: -1px;
    background-color: transparent;
    border-width: 0;
    border-bottom-width: 2px;
    border-bottom-color: transparent;
}

.daisy-tabs-bordered .daisy-tab-item-active {
    background-color: transparent;
    border-bottom-color: rgba(110, 170, 255, 1);
}

.daisy-tabs-bordered .daisy-tab-panel {
    border-radius: 0;
    border-width: 0;
    background-color: transparent;
    padding-top: 16px;
}

/* Lifted Variant */
.daisy-tabs-lifted .daisy-tab-item {
    border-width: 1px;
    background-color: rgba(96, 96, 96, 1);
    margin-right: -1px;
    border-radius: 3px 3px 0 0;
}

.daisy-tabs-lifted .daisy-tab-item-active {
    background-color: rgba(64, 64, 64, 1);
    border-color: rgba(128, 128, 128, 0.5);
    border-bottom-color: rgba(64, 64, 64, 1);
}

.daisy-tabs-lifted .daisy-tab-panel {
    border-width: 1px;
    border-radius: 0 3px 3px 3px;
    margin-top: -1px;
    border-color: rgba(128, 128, 128, 0.5);
}

/* Boxed Variant */
.daisy-tabs-boxed .daisy-tab-item {
    background-color: rgba(72, 72, 72, 1);
    border-width: 1px;
    border-radius: 3px;
    margin-right: 4px;
    margin-bottom: 8px;
    border-color: rgba(88, 88, 88, 1);
}

.daisy-tabs-boxed .daisy-tab-item .daisy-tab-item-label {
    color: rgba(255, 255, 255, 0.75);
}

.daisy-tabs-boxed .daisy-tab-item:hover {
    background-color: rgba(100, 100, 100, 1);
    border-color: rgba(120, 120, 120, 1);
}

.daisy-tabs-boxed .daisy-tab-item:hover .daisy-tab-item-label {
    color: rgba(255, 255, 255, 0.9);
}

.daisy-tabs-boxed .daisy-tab-item-active {
    background-color: rgba(58, 123, 213, 1);
    border-color: rgba(58, 123, 213, 1);
    border-width: 2px;
}

.daisy-tabs-boxed .daisy-tab-item-active:hover {
    background-color: rgba(68, 133, 223, 1);
    border-color: rgba(68, 133, 223, 1);
}

.daisy-tabs-boxed .daisy-tab-item-active .daisy-tab-item-label {
    color: rgba(255, 255, 255, 1);
    -unity-font-style: bold;
}

.daisy-tabs-boxed .daisy-tab-panel {
    border-width: 1px;
    border-radius: 3px;
    background-color: rgba(64, 64, 64, 1);
    border-color: rgba(128, 128, 128, 0.5);
}

/* Column Direction Specific Styles */
.daisy-tabs-column .daisy-tab-item {
    border-radius: 3px 0 0 3px;
    justify-content: flex-start;
    width: 100%;
}

.daisy-tabs-column .daisy-tab-panel {
    border-radius: 0 3px 3px 0;
}

.daisy-tabs-column.daisy-tabs-bordered .daisy-tab-container {
}

.daisy-tabs-column.daisy-tabs-bordered .daisy-tab-item {
    margin-bottom: 0;
    margin-right: -1px;
    border-right-width: 2px;
    border-right-color: transparent;
    border-bottom-width: 0;
}

.daisy-tabs-column.daisy-tabs-bordered .daisy-tab-item-active {
    border-right-color: rgba(110, 170, 255, 1);
}

/* Dark Theme Support - Already using Unity-style dark colors as default */
.theme-dark .daisy-tab-item-label {
    color: rgba(255, 255, 255, 0.8);
}

.theme-dark .daisy-tab-item-active {
    background-color: rgba(64, 64, 64, 1);
}

.theme-dark .daisy-tab-panel {
    background-color: rgba(64, 64, 64, 1);
}

.theme-dark .daisy-tabs-lifted .daisy-tab-item {
    background-color: rgba(96, 96, 96, 1);
    border-color: rgba(128, 128, 128, 0.5);
}

.theme-dark .daisy-tabs-lifted .daisy-tab-item-active {
    background-color: rgba(64, 64, 64, 1);
}

.theme-dark .daisy-tabs-boxed .daisy-tab-item {
    background-color: rgba(96, 96, 96, 1);
    border-color: rgba(128, 128, 128, 0.3);
}