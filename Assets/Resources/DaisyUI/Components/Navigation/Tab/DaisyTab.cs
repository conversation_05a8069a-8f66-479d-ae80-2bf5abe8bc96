using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;

namespace BlastingDesign.UI.DaisyUI.Components.Navigation
{
    [UxmlElement("DaisyTab")]
    public partial class DaisyTab : DaisyComponent
    {

        #region Properties
        private string _activeTabId = "";
        private TabDirection _direction = TabDirection.Row;
        private TabVariant _variant = TabVariant.None;
        private TabSize _size = TabSize.Medium;

        [UxmlAttribute("active-tab-id")]
        public string ActiveTabId
        {
            get => _activeTabId;
            set
            {
                if (_activeTabId != value)
                {
                    _activeTabId = value;
                    UpdateActiveTab();
                }
            }
        }

        [UxmlAttribute("direction")]
        public TabDirection Direction
        {
            get => _direction;
            set
            {
                if (_direction != value)
                {
                    _direction = value;
                    UpdateDirection();
                }
            }
        }

        [UxmlAttribute("variant")]
        public new TabVariant Variant
        {
            get => _variant;
            set
            {
                if (_variant != value)
                {
                    var oldVariant = _variant;
                    _variant = value;
                    UpdateVariant(oldVariant);
                }
            }
        }

        public new TabSize Size
        {
            get => _size;
            set
            {
                if (_size != value)
                {
                    var oldSize = _size;
                    _size = value;
                    UpdateSize(oldSize);
                }
            }
        }
        #endregion

        #region Elements
        private VisualElement _tabContainer;
        private VisualElement _contentContainer;
        private readonly List<DaisyTabItem> _tabItems = new();
        private readonly List<VisualElement> _tabPanels = new();
        #endregion

        #region Events
        public event Action<string> TabChanged;
        #endregion

        #region Constructors
        public DaisyTab() : this("")
        {
        }

        public DaisyTab(string activeTabId) : base("tabs")
        {
            _activeTabId = activeTabId;
        }
        #endregion

        #region Static Factory Methods
        public static DaisyTab Create(string activeTabId = "")
        {
            return new DaisyTab(activeTabId);
        }

        public static DaisyTab Boxed(string activeTabId = "")
        {
            return new DaisyTab(activeTabId).SetVariant(TabVariant.Boxed);
        }

        public static DaisyTab Bordered(string activeTabId = "")
        {
            return new DaisyTab(activeTabId).SetVariant(TabVariant.Bordered);
        }

        public static DaisyTab Lifted(string activeTabId = "")
        {
            return new DaisyTab(activeTabId).SetVariant(TabVariant.Lifted);
        }
        #endregion

        #region Initialization
        protected override string TemplatePath => "DaisyUI/Components/Navigation/Tab/DaisyTab";

        protected override void OnInitialize()
        {
            BindElements();
            ApplyInitialStyles();
        }


        private void BindElements()
        {
            _tabContainer = this.Q<VisualElement>("tab-container");
            _contentContainer = this.Q<VisualElement>("content-container");
            
            if (_tabContainer == null || _contentContainer == null)
            {
                Debug.LogError("Tab template elements not found. Ensure template has 'tab-container' and 'content-container' elements.");
            }
        }

        private void ApplyInitialStyles()
        {
            UpdateDirection();
            UpdateVariant(TabVariant.None);
            UpdateSize(TabSize.Medium);
            
            // 确保已有的tabs有正确的激活状态
            if (_tabItems.Count > 0)
            {
                UpdateActiveTab();
            }
        }
        #endregion

        #region Public Methods
        public DaisyTab AddTab(string id, string label, VisualElement content = null)
        {
            if (string.IsNullOrEmpty(id))
            {
                Debug.LogError("Tab ID cannot be null or empty");
                return this;
            }

            if (_tabItems.Exists(t => t.Id == id))
            {
                Debug.LogWarning($"Tab with ID '{id}' already exists");
                return this;
            }

            var tabItem = new DaisyTabItem(id, label);
            tabItem.OnClicked += () => SetActiveTab(id);
            
            _tabItems.Add(tabItem);
            _tabContainer?.Add(tabItem);

            if (content != null)
            {
                AddTabPanel(id, content);
            }

            // 处理激活状态逻辑
            if (_activeTabId == id)
            {
                // 如果这个tab的ID匹配预设的activeTabId，激活它
                SetActiveTab(id);
            }
            else if (_tabItems.Count == 1)
            {
                // 如果这是第一个tab且当前没有激活的tab，激活第一个tab
                if (string.IsNullOrEmpty(_activeTabId))
                {
                    SetActiveTab(id);
                }
            }

            return this;
        }

        public DaisyTab AddTabPanel(string tabId, VisualElement content)
        {
            if (content == null) return this;

            content.AddToClassList("daisy-tab-panel");
            content.userData = tabId; // 设置userData以便后续识别
            content.SetEnabled(tabId == _activeTabId);
            content.style.display = tabId == _activeTabId ? DisplayStyle.Flex : DisplayStyle.None;
            
            _tabPanels.Add(content);
            _contentContainer?.Add(content);

            return this;
        }

        public DaisyTab SetActiveTab(string tabId)
        {
            var wasAlreadyActive = _activeTabId == tabId;
            
            _activeTabId = tabId;
            UpdateActiveTab();
            
            // 只有在真正发生切换时才触发事件
            if (!wasAlreadyActive)
            {
                TabChanged?.Invoke(tabId);
            }

            return this;
        }

        public DaisyTab RemoveTab(string tabId)
        {
            var tabItem = _tabItems.Find(t => t.Id == tabId);
            if (tabItem != null)
            {
                _tabItems.Remove(tabItem);
                _tabContainer?.Remove(tabItem);
            }

            var panel = _tabPanels.Find(p => p.userData?.ToString() == tabId);
            if (panel != null)
            {
                _tabPanels.Remove(panel);
                _contentContainer?.Remove(panel);
            }

            if (_activeTabId == tabId && _tabItems.Count > 0)
            {
                SetActiveTab(_tabItems[0].Id);
            }

            return this;
        }
        #endregion

        #region Fluent Interface
        public DaisyTab SetVariant(TabVariant variant)
        {
            Variant = variant;
            return this;
        }

        public DaisyTab SetDirection(TabDirection direction)
        {
            Direction = direction;
            return this;
        }

        public DaisyTab SetSize(TabSize size)
        {
            Size = size;
            return this;
        }

        public DaisyTab OnTabChange(Action<string> callback)
        {
            TabChanged += callback;
            return this;
        }
        #endregion

        #region Size Methods
        public DaisyTab SetExtraSmall() => SetSize(TabSize.ExtraSmall);
        public DaisyTab SetSmall() => SetSize(TabSize.Small);
        public DaisyTab SetMedium() => SetSize(TabSize.Medium);
        public DaisyTab SetLarge() => SetSize(TabSize.Large);
        public DaisyTab SetExtraLarge() => SetSize(TabSize.ExtraLarge);
        #endregion

        #region Private Methods
        private void UpdateActiveTab()
        {
            foreach (var tabItem in _tabItems)
            {
                tabItem.SetActive(tabItem.Id == _activeTabId);
            }

            foreach (var panel in _tabPanels)
            {
                var isActive = panel.userData?.ToString() == _activeTabId;
                panel.SetEnabled(isActive);
                panel.style.display = isActive ? DisplayStyle.Flex : DisplayStyle.None;
            }
        }

        private void UpdateDirection()
        {
            RemoveFromClassList("daisy-tabs-row");
            RemoveFromClassList("daisy-tabs-column");
            
            AddToClassList(_direction == TabDirection.Row ? "daisy-tabs-row" : "daisy-tabs-column");
        }

        private void UpdateVariant(TabVariant oldVariant)
        {
            if (oldVariant != TabVariant.None)
            {
                RemoveFromClassList($"daisy-tabs-{oldVariant.ToString().ToLower()}");
            }
            
            if (_variant != TabVariant.None)
            {
                AddToClassList($"daisy-tabs-{_variant.ToString().ToLower()}");
            }
        }

        private void UpdateSize(TabSize oldSize)
        {
            RemoveFromClassList($"daisy-tabs-{oldSize.ToString().ToLower()}");
            AddToClassList($"daisy-tabs-{_size.ToString().ToLower()}");
        }
        #endregion
    }

    #region Enums
    public enum TabDirection
    {
        Row,
        Column
    }

    public enum TabVariant
    {
        None,
        Boxed,
        Bordered,
        Lifted
    }

    public enum TabSize
    {
        ExtraSmall,
        Small,
        Medium,
        Large,
        ExtraLarge
    }
    #endregion
}