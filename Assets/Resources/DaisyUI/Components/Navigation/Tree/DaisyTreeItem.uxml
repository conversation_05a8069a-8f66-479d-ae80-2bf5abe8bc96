<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements">
    <ui:VisualElement name="container" class="daisy-tree-item-container">
        <ui:VisualElement name="indent-container" class="daisy-tree-item-indent-container">
            <!-- 缩进元素将在这里动态添加 -->
        </ui:VisualElement>
        
        <ui:VisualElement name="content" class="daisy-tree-item-content">
            <ui:Button name="main-button" class="daisy-tree-item-main-button">
                <ui:Label name="icon" text="" class="daisy-tree-item-icon daisy-icon" />
                <ui:Label name="text" text="Item" class="daisy-tree-item-text" />
            </ui:Button>
            
            <ui:VisualElement name="actions-container" class="daisy-tree-item-actions-container">
                <!-- 操作按钮将在这里动态添加 -->
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>