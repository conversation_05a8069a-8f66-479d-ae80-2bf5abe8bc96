# DaisyTree 主题变量化迁移文档

## 概述

本次更新将 DaisyTree 组件中的硬编码颜色值替换为 CSS 变量，实现了更好的主题一致性和可维护性。

## 变更内容

### 1. 硬编码颜色值替换

**之前的硬编码颜色值：**
```css
.daisy-tree.dark {
    background-color: #2a2a2a;  /* 硬编码深色背景 */
    border-color: #404040;      /* 硬编码边框颜色 */
}

.daisy-tree.dark .daisy-tree-item-text {
    color: #ffffff;             /* 硬编码白色文字 */
}
```

**替换后的变量化样式：**
```css
.daisy-tree.dark {
    background-color: var(--daisy-dark-base-200);
    border-color: var(--daisy-dark-base-300);
}

.daisy-tree.dark .daisy-tree-item-text {
    color: var(--daisy-dark-content);
}
```

### 2. 新增主题变量

在 `Assets/Resources/DaisyUI/Themes/themes.uss` 中新增了以下变量：

**浅色主题变量（:root）：**
```css
--daisy-dark-base-100: var(--base-100);        /* 主背景色 */
--daisy-dark-base-200: var(--base-200);        /* 次级背景色 */
--daisy-dark-base-300: var(--base-300);        /* 边框颜色 */
--daisy-dark-base-400: #D1D5DB;                /* 更深的边框/按钮颜色 */
--daisy-dark-content: var(--base-content);     /* 文字颜色 */
--daisy-dark-hover: rgba(209, 213, 219, 0.5);  /* 悬停效果 */
```

**深色主题变量（.theme-dark）：**
```css
--daisy-dark-base-100: var(--base-100);        /* 深色主背景 */
--daisy-dark-base-200: var(--base-200);        /* 深色次级背景 */
--daisy-dark-base-300: var(--base-300);        /* 深色边框 */
--daisy-dark-base-400: #475569;                /* 更深的深色边框/按钮 */
--daisy-dark-content: var(--base-content);     /* 深色主题文字 */
--daisy-dark-hover: rgba(71, 85, 105, 0.5);    /* 深色悬停效果 */
```

### 3. 替换的颜色映射

| 原硬编码值 | 新变量 | 用途 |
|-----------|--------|------|
| `#2a2a2a` | `var(--daisy-dark-base-200)` | 深色主题容器背景 |
| `#1f1f1f` | `var(--daisy-dark-base-100)` | 深色主题内容区背景 |
| `#404040` | `var(--daisy-dark-base-300)` | 深色主题边框和按钮背景 |
| `#505050` | `var(--daisy-dark-base-400)` | 深色主题按钮悬停背景 |
| `#ffffff` | `var(--daisy-dark-content)` | 深色主题文字颜色 |

## 优势

### 1. 主题一致性
- 所有颜色值现在都通过 DaisyUI 的统一主题系统管理
- 确保与其他 DaisyUI 组件的颜色协调一致

### 2. 可维护性
- 颜色修改只需在主题文件中进行，无需修改组件样式
- 减少了硬编码值，降低了维护成本

### 3. 扩展性
- 支持未来添加更多主题变体
- 变量命名规范，便于理解和使用

### 4. 响应式主题
- 自动适应系统深色/浅色模式偏好
- 支持动态主题切换

## 使用方法

### 应用深色主题
```css
.my-tree-container {
    /* 应用深色主题类 */
}
```

### 自定义主题变量
如需自定义颜色，可以在自定义主题中重写变量：
```css
.my-custom-theme {
    --daisy-dark-base-100: #1a1a1a;
    --daisy-dark-base-200: #2d2d2d;
    --daisy-dark-base-300: #404040;
    --daisy-dark-content: #e0e0e0;
}
```

## 测试

使用 `DaisyTreeThemeTest.uss` 文件可以测试主题变量是否正确工作。

## 兼容性

- 完全向后兼容现有的 DaisyTree 组件使用方式
- 不影响现有的功能和行为
- 仅改进了主题系统的实现方式
