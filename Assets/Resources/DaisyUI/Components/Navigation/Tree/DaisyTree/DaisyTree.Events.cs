using UnityEngine.UIElements;

namespace BlastingDesign.UI.DaisyUI.Components.Navigation.Tree
{
    public partial class DaisyTree
    {
        #region Event Management (Simplified for TreeView)
        // TreeView事件处理已经在Core.cs中处理
        // 这个文件保留用于向后兼容和扩展

        private void OnTreeKeyDown(KeyDownEvent evt)
        {
            switch (evt.keyCode)
            {
                case UnityEngine.KeyCode.LeftArrow:
                    // 收起选中的项目
                    var selectedItems = GetSelectedItems();
                    foreach (var item in selectedItems)
                    {
                        if (item.IsExpanded)
                        {
                            CollapseItem(item.Id);
                        }
                    }
                    break;

                case UnityEngine.KeyCode.RightArrow:
                    // 展开选中的项目
                    selectedItems = GetSelectedItems();
                    foreach (var item in selectedItems)
                    {
                        if (item.HasChildren && !item.IsExpanded)
                        {
                            ExpandItem(item.Id);
                        }
                    }
                    break;

                case UnityEngine.KeyCode.Space:
                    // 切换展开/收起
                    selectedItems = GetSelectedItems();
                    foreach (var item in selectedItems)
                    {
                        if (item.HasChildren)
                        {
                            ToggleItem(item.Id);
                        }
                    }
                    evt.PreventDefault();
                    break;

                case UnityEngine.KeyCode.A:
                    if (evt.ctrlKey || evt.commandKey)
                    {
                        // 全选
                        if (MultiSelect)
                        {
                            SelectAll();
                            evt.PreventDefault();
                        }
                    }
                    break;

                case UnityEngine.KeyCode.Escape:
                    // 取消选择
                    DeselectAll();
                    break;
            }
        }
        #endregion
    }
}