using System.Collections.Generic;
using System.Linq;
using UnityEngine.UIElements;

namespace BlastingDesign.UI.DaisyUI.Components.Navigation.Tree
{
    public partial class DaisyTree
    {
        #region Search Functionality
        private void OnSearchValueChanged(string newValue)
        {
            CurrentSearchQuery = newValue;

            // 使用简单的防抖机制：只在输入长度达到一定值时才搜索
            // 这样可以减少频繁的小字符串搜索
            if (CurrentSearchQuery.Length == 0 || CurrentSearchQuery.Length >= 2)
            {
                FilterData();
                OnSearchChanged?.Invoke(CurrentSearchQuery);
            }
        }

        private void OnSearchKeyDown(KeyDownEvent evt)
        {
            if (evt.keyCode == UnityEngine.KeyCode.Return || evt.keyCode == UnityEngine.KeyCode.KeypadEnter)
            {
                OnSearchButtonClicked();
            }
            else if (evt.keyCode == UnityEngine.KeyCode.Escape)
            {
                ClearSearch();
            }
        }

        private void OnSearchButtonClicked()
        {
            if (_searchInput != null)
            {
                CurrentSearchQuery = _searchInput.Value;
                FilterData();
                OnSearchChanged?.Invoke(CurrentSearchQuery);
            }
        }

        private void OnClearSearchButtonClicked()
        {
            ClearSearch();
        }

        public DaisyTree Search(string query)
        {
            CurrentSearchQuery = query ?? string.Empty;
            if (_searchInput != null)
            {
                _searchInput.SetValue(CurrentSearchQuery);
            }
            FilterData();
            OnSearchChanged?.Invoke(CurrentSearchQuery);
            return this;
        }

        public DaisyTree ClearSearch()
        {
            CurrentSearchQuery = string.Empty;
            if (_searchInput != null)
            {
                _searchInput.SetValue(string.Empty);
            }
            FilterData();
            OnSearchChanged?.Invoke(CurrentSearchQuery);
            return this;
        }

        private void FilterData()
        {
            if (string.IsNullOrWhiteSpace(CurrentSearchQuery))
            {
                FilteredData = new List<DaisyTreeData>(TreeData);
            }
            else
            {
                FilteredData = FilterTreeData(TreeData, CurrentSearchQuery.ToLower()).ToList();
            }

            if (_treeView != null)
            {
                var itemDataList = FilteredData.ConvertAll(item => CreateTreeViewItemData(item));
                _treeView.SetRootItems(itemDataList);
                // 移除Rebuild()调用，SetRootItems已经会触发必要的更新
                // _treeView.Rebuild(); // 这行导致了内存分配问题
            }

            UpdateEmptyState();
        }

        private IEnumerable<DaisyTreeData> FilterTreeData(List<DaisyTreeData> items, string query)
        {
            foreach (var item in items)
            {
                bool matches = item.Text?.ToLower().Contains(query) == true ||
                              item.Icon?.ToLower().Contains(query) == true;

                // 检查子项
                List<DaisyTreeData> filteredChildren = null;
                if (item.Children != null && item.Children.Count > 0)
                {
                    var childResults = FilterTreeData(item.Children, query);
                    var childList = childResults.ToList();
                    if (childList.Count > 0)
                    {
                        filteredChildren = childList;
                    }
                }

                // 如果当前项匹配或有匹配的子项，则包含此项
                if (matches || filteredChildren != null)
                {
                    // 减少对象创建：只在必要时创建新对象
                    var filteredItem = new DaisyTreeData(item.Id, item.Text, item.Icon)
                    {
                        Parent = item.Parent,
                        IsExpanded = item.IsExpanded || (filteredChildren != null), // 如果有匹配的子项则展开
                        IsSelected = item.IsSelected,
                        Actions = item.Actions,
                        Children = filteredChildren ?? new List<DaisyTreeData>()
                    };
                    yield return filteredItem;
                }
            }
        }

        public DaisyTree ExpandSearchResults()
        {
            if (string.IsNullOrWhiteSpace(CurrentSearchQuery)) return this;

            ExpandMatchingItems(FilteredData);

            // 移除Rebuild调用，避免不必要的内存分配
            // TreeView会在需要时自动更新

            return this;
        }

        private void ExpandMatchingItems(List<DaisyTreeData> items)
        {
            foreach (var item in items)
            {
                if (item.HasChildren)
                {
                    item.IsExpanded = true;
                    if (item.Children != null)
                    {
                        ExpandMatchingItems(item.Children);
                    }
                }
            }
        }

        public DaisyTree CollapseSearchResults()
        {
            CollapseItems(FilteredData);

            if (_treeView != null)
            {
                _treeView.Rebuild();
            }

            return this;
        }

        private void CollapseItems(List<DaisyTreeData> items)
        {
            foreach (var item in items)
            {
                item.IsExpanded = false;
                if (item.Children != null)
                {
                    CollapseItems(item.Children);
                }
            }
        }
        #endregion
    }
}