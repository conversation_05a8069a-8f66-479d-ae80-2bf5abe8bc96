using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace BlastingDesign.UI.DaisyUI.Components.Navigation.Tree
{
    public partial class DaisyTree
    {
        #region Fluent API
        public DaisyTree SetMultiSelect(bool multiSelect)
        {
            MultiSelect = multiSelect;
            return this;
        }

        public DaisyTree SetShowLines(bool showLines)
        {
            ShowLines = showLines;
            return this;
        }

        public DaisyTree SetShowIcons(bool showIcons)
        {
            ShowIcons = showIcons;
            return this;
        }

        public DaisyTree SetShowActions(bool showActions)
        {
            ShowActions = showActions;
            return this;
        }

        public DaisyTree SetAllowSearch(bool allowSearch)
        {
            AllowSearch = allowSearch;
            return this;
        }

        public DaisyTree SetSearchPlaceholder(string placeholder)
        {
            SearchPlaceholder = placeholder;
            return this;
        }

        public DaisyTree OnItemClick(Action<DaisyTreeData> callback)
        {
            OnItemClicked += callback;
            return this;
        }

        public DaisyTree OnItemSelect(Action<DaisyTreeData> callback)
        {
            OnItemSelected += callback;
            return this;
        }

        public DaisyTree OnItemDeselect(Action<DaisyTreeData> callback)
        {
            OnItemDeselected += callback;
            return this;
        }

        public DaisyTree OnItemExpand(Action<DaisyTreeData> callback)
        {
            OnItemExpanded += callback;
            return this;
        }

        public DaisyTree OnItemCollapse(Action<DaisyTreeData> callback)
        {
            OnItemCollapsed += callback;
            return this;
        }

        public DaisyTree OnAction(Action<DaisyTreeData, string> callback)
        {
            OnActionTriggered += callback;
            return this;
        }

        public DaisyTree OnSearch(Action<string> callback)
        {
            OnSearchChanged += callback;
            return this;
        }
        #endregion

        #region Tree Operations
        public DaisyTree ExpandItem(string id)
        {
            var item = FindItem(id);
            if (item != null)
            {
                item.IsExpanded = true;
                if (_treeView != null)
                {
                    var itemId = item.Id.GetHashCode();
                    Debug.Log($"[DaisyTree] Expanding item {id} with TreeView ID {itemId}");
                    _treeView.ExpandItem(itemId);
                }
                OnItemExpanded?.Invoke(item);
            }
            return this;
        }

        public DaisyTree CollapseItem(string id)
        {
            var item = FindItem(id);
            if (item != null)
            {
                item.IsExpanded = false;
                if (_treeView != null)
                {
                    var itemId = item.Id.GetHashCode();
                    Debug.Log($"[DaisyTree] Collapsing item {id} with TreeView ID {itemId}");
                    _treeView.CollapseItem(itemId);
                }
                OnItemCollapsed?.Invoke(item);
            }
            return this;
        }

        public DaisyTree ToggleItem(string id)
        {
            Debug.Log($"[DaisyTree] ToggleItem called for: {id}");
            var item = FindItem(id);
            if (item != null)
            {
                Debug.Log($"[DaisyTree] Found item {id}, IsExpanded: {item.IsExpanded}");
                if (item.IsExpanded)
                {
                    Debug.Log($"[DaisyTree] Collapsing {id}");
                    CollapseItem(id);
                }
                else
                {
                    Debug.Log($"[DaisyTree] Expanding {id}");
                    ExpandItem(id);
                }
            }
            else
            {
                Debug.Log($"[DaisyTree] Item not found: {id}");
            }
            return this;
        }

        public DaisyTree ExpandAll()
        {
            ExpandAllRecursive(TreeData);
            if (_treeView != null)
            {
                _treeView.ExpandAll();
            }
            return this;
        }

        public DaisyTree CollapseAll()
        {
            CollapseAllRecursive(TreeData);
            if (_treeView != null)
            {
                _treeView.CollapseAll();
            }
            return this;
        }

        private void ExpandAllRecursive(List<DaisyTreeData> items)
        {
            foreach (var item in items)
            {
                if (item.HasChildren)
                {
                    item.IsExpanded = true;
                    OnItemExpanded?.Invoke(item);
                    if (item.Children != null)
                    {
                        ExpandAllRecursive(item.Children);
                    }
                }
            }
        }

        private void CollapseAllRecursive(List<DaisyTreeData> items)
        {
            foreach (var item in items)
            {
                if (item.HasChildren)
                {
                    item.IsExpanded = false;
                    OnItemCollapsed?.Invoke(item);
                    if (item.Children != null)
                    {
                        CollapseAllRecursive(item.Children);
                    }
                }
            }
        }

        public DaisyTree SelectItem(string id)
        {
            var item = FindItem(id);
            if (item != null)
            {
                if (!MultiSelect)
                {
                    // 单选模式：清除其他选择
                    ClearAllSelections();
                }

                item.IsSelected = true;
                if (_treeView != null)
                {
                    _treeView.SetSelection(new[] { item.Id.GetHashCode() });
                }
                OnItemSelected?.Invoke(item);
            }
            return this;
        }

        public DaisyTree DeselectItem(string id)
        {
            var item = FindItem(id);
            if (item != null)
            {
                item.IsSelected = false;
                if (_treeView != null)
                {
                    var selectedItems = _treeView.selectedItems.Where(i => i != item);
                    _treeView.SetSelection(selectedItems.Cast<int>());
                }
                OnItemDeselected?.Invoke(item);
            }
            return this;
        }

        public DaisyTree SelectAll()
        {
            if (!MultiSelect) return this;

            SelectAllRecursive(TreeData);
            if (_treeView != null)
            {
                var allItemIds = GetAllItems().Select(item => item.Id.GetHashCode());
                _treeView.SetSelection(allItemIds);
            }
            return this;
        }

        public DaisyTree DeselectAll()
        {
            ClearAllSelections();
            if (_treeView != null)
            {
                _treeView.ClearSelection();
            }
            return this;
        }

        private void SelectAllRecursive(List<DaisyTreeData> items)
        {
            foreach (var item in items)
            {
                item.IsSelected = true;
                OnItemSelected?.Invoke(item);
                if (item.Children != null)
                {
                    SelectAllRecursive(item.Children);
                }
            }
        }

        private void ClearAllSelections()
        {
            ClearSelectionsRecursive(TreeData);
        }

        private void ClearSelectionsRecursive(List<DaisyTreeData> items)
        {
            foreach (var item in items)
            {
                if (item.IsSelected)
                {
                    item.IsSelected = false;
                    OnItemDeselected?.Invoke(item);
                }
                if (item.Children != null)
                {
                    ClearSelectionsRecursive(item.Children);
                }
            }
        }

        public List<DaisyTreeData> GetSelectedItems()
        {
            return GetSelectedItemsRecursive(TreeData).ToList();
        }

        private IEnumerable<DaisyTreeData> GetSelectedItemsRecursive(List<DaisyTreeData> items)
        {
            foreach (var item in items)
            {
                if (item.IsSelected)
                {
                    yield return item;
                }
                if (item.Children != null)
                {
                    foreach (var child in GetSelectedItemsRecursive(item.Children))
                    {
                        yield return child;
                    }
                }
            }
        }

        public List<DaisyTreeData> GetAllItems()
        {
            return GetAllItemsRecursive(TreeData).ToList();
        }

        private IEnumerable<DaisyTreeData> GetAllItemsRecursive(List<DaisyTreeData> items)
        {
            foreach (var item in items)
            {
                yield return item;
                if (item.Children != null)
                {
                    foreach (var child in GetAllItemsRecursive(item.Children))
                    {
                        yield return child;
                    }
                }
            }
        }
        #endregion
    }
}