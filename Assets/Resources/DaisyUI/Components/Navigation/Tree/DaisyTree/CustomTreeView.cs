using System;
using UnityEngine.UIElements;

namespace BlastingDesign.UI.DaisyUI.Components.Navigation.Tree
{
    /// <summary>
    /// 自定义TreeView，支持单击选中、双击展开/折叠
    /// </summary>
    public class CustomTreeView : TreeView
    {
        public event Action<object> OnItemSingleClicked;
        public event Action<object> OnItemDoubleClicked;
        
        private float lastClickTime = 0f;
        private object lastClickedItem = null;
        private const float DOUBLE_CLICK_TIME = 0.3f; // 300ms双击间隔
        
        public CustomTreeView()
        {
            // 注册点击事件
            RegisterCallback<PointerDownEvent>(OnPointerDown, TrickleDown.TrickleDown);
        }
        
        private void OnPointerDown(PointerDownEvent evt)
        {
            UnityEngine.Debug.Log($"[CustomTreeView] PointerDown on: {evt.target?.GetType().Name}");
            
            // 查找被点击的项目
            var clickedItem = GetItemFromElement(evt.target as VisualElement);
            if (clickedItem != null)
            {
                UnityEngine.Debug.Log($"[CustomTreeView] Found clicked item: {clickedItem}");
                var currentTime = UnityEngine.Time.unscaledTime;
                
                // 检查是否是双击
                if (lastClickedItem == clickedItem && (currentTime - lastClickTime) < DOUBLE_CLICK_TIME)
                {
                    // 这是双击，触发双击事件
                    UnityEngine.Debug.Log("[CustomTreeView] Double click detected");
                    OnItemDoubleClicked?.Invoke(clickedItem);
                    
                    // 重置状态
                    lastClickedItem = null;
                    lastClickTime = 0f;
                }
                else
                {
                    // 这是单击，立即触发选中事件
                    lastClickedItem = clickedItem;
                    lastClickTime = currentTime;
                    
                    UnityEngine.Debug.Log("[CustomTreeView] Single click detected, triggering selection");
                    OnItemSingleClicked?.Invoke(clickedItem);
                }
            }
            else
            {
                UnityEngine.Debug.Log("[CustomTreeView] No item found for click");
            }
        }
        
        private object GetItemFromElement(VisualElement element)
        {
            if (element == null) return null;
            
            // 向上查找包含数据的元素，寻找树项容器
            var current = element;
            while (current != null)
            {
                // 检查是否是树项容器
                if (current.ClassListContains("daisy-tree-item") && current.userData != null)
                {
                    return current.userData;
                }
                current = current.parent;
            }
            
            return null;
        }
    }
}