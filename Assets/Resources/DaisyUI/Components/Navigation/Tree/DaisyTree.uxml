<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements">
    <ui:VisualElement name="container" class="daisy-tree-container">
        <ui:VisualElement name="header" class="daisy-tree-header">
            <ui:VisualElement name="search-container" class="daisy-tree-search-container">
                <ui:VisualElement name="search-input-placeholder" class="daisy-tree-search-input" />
                <ui:Button name="search-button" class="daisy-tree-search-button" text="🔍" />
                <ui:Button name="clear-search-button" class="daisy-tree-clear-search-button" text="✕" />
            </ui:VisualElement>
        </ui:VisualElement>
        
        <ui:VisualElement name="tree-container" class="daisy-tree-content">
            <ui:TreeView name="tree-view" class="daisy-tree-view" />
        </ui:VisualElement>
        
        <ui:VisualElement name="empty-state" class="daisy-tree-empty-state">
            <ui:Label name="empty-label" text="No items to display" class="daisy-tree-empty-label" />
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>