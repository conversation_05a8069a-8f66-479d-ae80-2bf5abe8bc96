# DaisyTree 树状菜单组件

DaisyTree 是一个功能完整的树状菜单组件，支持层级结构显示、展开/收起、搜索、选择等功能。

## 快速开始

### 基础使用

```csharp
using BlastingDesign.UI.DaisyUI.Components.Navigation.Tree;
using BlastingDesign.UI.DaisyUI.Builders;

// 创建基础树组件
var tree = DaisyTree.Create("my-tree")
    .SetDarkTheme(true)
    .SetAllowSearch(true)
    .SetSearchPlaceholder("搜索...");

// 添加节点和项目
var node1 = tree.AddNode("node1", "城区1", "🏙️");
node1.AddChildItem("item1", "测量点", "📍")
    .AddAction("edit", "编辑", "✏️")
    .AddAction("delete", "删除", "🗑️");

// 使用构建器创建
var fileTree = DaisyBuilder.FileTree("file-browser");
var projectTree = DaisyBuilder.ProjectTree("project-structure");
```

### 数据绑定

```csharp
// 创建数据结构
var rootData = new DaisyTreeData("root", "根节点", "🌳");
var childData = rootData.AddChild("child1", "子节点", "📁");
childData.AddChild("item1", "叶子项", "📄");

// 绑定数据
var tree = DaisyTree.Create(rootData)
    .SetDarkTheme(true)
    .SetMultiSelect(true);
```

## 主要特性

### 🌳 层级结构
- 支持无限层级的树状结构
- 节点可以包含子节点和叶子项
- 自动处理层级缩进和连接线

### 🔍 搜索功能
- 实时搜索过滤
- 高亮匹配结果
- 自动展开包含匹配项的节点

### 🎯 选择模式
- 单选模式（默认）
- 多选模式
- 支持全选/取消全选

### 🎨 主题支持
- 深色主题
- 浅色主题
- 自定义主题

### ⚙️ 操作按钮
- 每个项目可添加自定义操作按钮
- 支持图标和提示信息
- 灵活的事件处理

### 📱 响应式设计
- 移动设备友好
- 触摸操作支持
- 自适应布局

## API 文档

### DaisyTree 主要方法

#### 创建方法
```csharp
// 静态工厂方法
DaisyTree.Create(string id)
DaisyTree.Create(DaisyTreeData rootData)
```

#### 配置方法
```csharp
.SetDarkTheme(bool dark = true)
.SetMultiSelect(bool multiSelect = true)
.SetAllowSearch(bool allowSearch = true)
.SetSearchPlaceholder(string placeholder)
.SetShowLines(bool showLines = true)
.SetShowIcons(bool showIcons = true)
.SetShowActions(bool showActions = true)
```

#### 节点管理
```csharp
AddNode(string id, string text, string icon = null)
AddItem(string id, string text, string icon = null)
RemoveNode(string id)
RemoveItem(string id)
FindNode(string id)
FindItem(string id)
```

#### 选择管理
```csharp
SelectItem(string itemId)
DeselectItem(string itemId)
SelectAll()
DeselectAll()
IsSelected(string itemId)
GetSelectedItems()
```

#### 展开管理
```csharp
ExpandNode(string nodeId)
CollapseNode(string nodeId)
ToggleNode(string nodeId)
ExpandAll()
CollapseAll()
```

#### 事件处理
```csharp
OnNodeClick(Action<string> callback)
OnItemSelect(Action<string> callback)
OnNodeExpand(Action<string> callback)
OnAction(Action<string, string> callback)
OnSearch(Action<string> callback)
```

### DaisyBuilder 构建器

#### 预定义树类型
```csharp
DaisyBuilder.Tree(string id)               // 基础树
DaisyBuilder.FileTree(string id)           // 文件浏览器树
DaisyBuilder.ProjectTree(string id)        // 项目结构树
DaisyBuilder.SettingsTree(string id)       // 设置树
DaisyBuilder.SearchTree(string id)         // 搜索树
DaisyBuilder.MultiSelectTree(string id)    // 多选树
DaisyBuilder.DarkTree(string id)           // 深色主题树
DaisyBuilder.CompactTree(string id)        // 紧凑树
```

## 样式定制

### 主题变体
- `.dark` - 深色主题
- `.light` - 浅色主题（默认）

### 尺寸变体
- `.xs` - 超小尺寸
- `.sm` - 小尺寸
- `.md` - 中等尺寸（默认）
- `.lg` - 大尺寸
- `.xl` - 超大尺寸

### 修饰符
- `.compact` - 紧凑布局
- `.bordered` - 边框样式
- `.rounded` - 圆角样式
- `.shadow` - 阴影效果

### 显示选项
- `.show-lines` - 显示连接线
- `.hide-lines` - 隐藏连接线
- `.show-icons` - 显示图标
- `.hide-icons` - 隐藏图标
- `.show-actions` - 显示操作按钮
- `.hide-actions` - 隐藏操作按钮

## 使用示例

### 1. 基础文件浏览器
```csharp
var fileTree = DaisyBuilder.FileTree("file-browser")
    .SetSearchPlaceholder("搜索文件...")
    .OnAction((itemId, actionId) => {
        if (actionId == "open") {
            OpenFile(itemId);
        }
    });

// 添加文件夹
var folder = fileTree.AddNode("folder1", "我的文档", "📁");
folder.AddChildItem("doc1.txt", "文档1", "📄")
    .AddAction("open", "打开", "📂")
    .AddAction("edit", "编辑", "✏️")
    .AddAction("delete", "删除", "🗑️");
```

### 2. 项目结构树
```csharp
var projectTree = DaisyBuilder.ProjectTree("project-structure")
    .SetMultiSelect(true)
    .OnItemSelect((itemId) => {
        ShowItemDetails(itemId);
    });

// 使用数据绑定
var projectData = new DaisyTreeData("project", "我的项目", "🏗️");
var srcFolder = projectData.AddChild("src", "源代码", "📂");
srcFolder.AddChild("main.cs", "主程序", "📄");
srcFolder.AddChild("utils.cs", "工具类", "📄");

projectTree.BindData(projectData);
```

### 3. 设置菜单
```csharp
var settingsTree = DaisyBuilder.SettingsTree("settings")
    .SetShowLines(false)
    .OnItemSelect((itemId) => {
        ShowSettingPanel(itemId);
    });

var general = settingsTree.AddNode("general", "常规设置", "⚙️");
general.AddChildItem("theme", "主题", "🎨");
general.AddChildItem("language", "语言", "🌐");
general.AddChildItem("notifications", "通知", "🔔");
```

## 命名空间

所有Tree组件都位于 `BlastingDesign.UI.DaisyUI.Components.Navigation.Tree` 命名空间中：

- `DaisyTree` - 主树容器组件
- `DaisyTreeNode` - 树节点组件
- `DaisyTreeItem` - 树项组件
- `DaisyTreeData` - 数据模型
- `DaisyTreeAction` - 操作按钮模型

## 依赖项

- `BlastingDesign.UI.DaisyUI.Core` - 核心组件基类
- `UnityEngine.UIElements` - Unity UI系统
- `System.Collections.Generic` - 集合类型

## 注意事项

1. 确保在使用前已经正确设置了UIDocument
2. 大数据集建议使用分页或虚拟滚动
3. 操作按钮的图标建议使用emoji或图标字体
4. 深色主题需要配合相应的CSS变量使用

## 更多示例

查看 `DaisyTreeExample.cs` 文件获取更多完整的使用示例。