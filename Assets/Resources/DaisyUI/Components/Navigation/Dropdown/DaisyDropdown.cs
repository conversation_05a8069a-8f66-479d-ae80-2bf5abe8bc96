using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;
using BlastingDesign.Utils;
using System;
using System.Collections.Generic;

namespace BlastingDesign.UI.DaisyUI.Components.Navigation
{
    /// <summary>
    /// DaisyUI下拉菜单组件
    /// 提供灵活的下拉菜单功能，支持按钮触发和悬停触发
    /// </summary>
    public class DaisyDropdown : DaisyComponent
    {
        #region 私有字段

        private const string TEMPLATE_PATH = "DaisyUI/Components/Navigation/Dropdown/DaisyDropdown";
        
        private VisualElement _dropdownTrigger;
        private VisualElement _dropdownContent;
        private VisualElement _dropdownMenu;
        private bool _isOpen = false;
        private bool _hoverTrigger = false;
        private bool _clickTrigger = true;
        private bool _closeOnClickOutside = true;
        private List<VisualElement> _menuItems;
        private string _position = "bottom";

        #endregion

        #region 属性

        /// <summary>
        /// 下拉菜单是否打开
        /// </summary>
        public bool IsOpen
        {
            get => _isOpen;
            set
            {
                _isOpen = value;
                UpdateVisibility();
            }
        }

        /// <summary>
        /// 是否悬停触发
        /// </summary>
        public bool HoverTrigger
        {
            get => _hoverTrigger;
            set => _hoverTrigger = value;
        }

        /// <summary>
        /// 是否点击触发
        /// </summary>
        public bool ClickTrigger
        {
            get => _clickTrigger;
            set => _clickTrigger = value;
        }

        /// <summary>
        /// 是否点击外部关闭
        /// </summary>
        public bool CloseOnClickOutside
        {
            get => _closeOnClickOutside;
            set => _closeOnClickOutside = value;
        }

        /// <summary>
        /// 下拉菜单位置
        /// </summary>
        public string Position
        {
            get => _position;
            set
            {
                _position = value ?? "bottom";
                UpdatePosition();
            }
        }

        /// <summary>
        /// 菜单项列表
        /// </summary>
        public List<VisualElement> MenuItems => _menuItems ?? new List<VisualElement>();

        /// <summary>
        /// 触发器元素
        /// </summary>
        public VisualElement Trigger => _dropdownTrigger;

        /// <summary>
        /// 菜单内容区域
        /// </summary>
        public VisualElement MenuContent => _dropdownMenu;

        /// <summary>
        /// 模板路径
        /// </summary>
        protected override string TemplatePath => TEMPLATE_PATH;

        #endregion

        #region 事件

        /// <summary>
        /// 下拉菜单打开时触发
        /// </summary>
        public event Action OnOpen;

        /// <summary>
        /// 下拉菜单关闭时触发
        /// </summary>
        public event Action OnClose;

        /// <summary>
        /// 菜单项点击时触发
        /// </summary>
        public event Action<VisualElement> OnItemClick;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public DaisyDropdown() : base("dropdown")
        {
            _menuItems = new List<VisualElement>();
            CreateDropdownStructure();
            SetupEventHandlers();
        }

        #endregion

        #region 静态工厂方法

        /// <summary>
        /// 创建下拉菜单组件
        /// </summary>
        /// <returns>DaisyDropdown实例</returns>
        public static DaisyDropdown Create()
        {
            return new DaisyDropdown();
        }

        /// <summary>
        /// 创建带触发器的下拉菜单
        /// </summary>
        /// <param name="triggerElement">触发器元素</param>
        /// <returns>DaisyDropdown实例</returns>
        public static DaisyDropdown Create(VisualElement triggerElement)
        {
            var dropdown = new DaisyDropdown();
            dropdown.SetTrigger(triggerElement);
            return dropdown;
        }

        /// <summary>
        /// 创建带按钮触发器的下拉菜单
        /// </summary>
        /// <param name="buttonText">按钮文本</param>
        /// <returns>DaisyDropdown实例</returns>
        public static DaisyDropdown CreateWithButton(string buttonText)
        {
            var dropdown = new DaisyDropdown();
            var button = new Button() { text = buttonText };
            button.AddToClassList("daisy-btn");
            button.AddToClassList("daisy-btn-primary");
            dropdown.SetTrigger(button);
            return dropdown;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 创建下拉菜单结构
        /// </summary>
        private void CreateDropdownStructure()
        {
            // 创建触发器容器
            _dropdownTrigger = new VisualElement();
            _dropdownTrigger.AddToClassList("daisy-dropdown-trigger");
            Add(_dropdownTrigger);

            // 创建内容容器
            _dropdownContent = new VisualElement();
            _dropdownContent.AddToClassList("daisy-dropdown-content");
            _dropdownContent.AddToClassList("daisy-hidden"); // 默认隐藏
            Add(_dropdownContent);

            // 创建菜单容器
            _dropdownMenu = new VisualElement();
            _dropdownMenu.AddToClassList("daisy-dropdown-menu");
            _dropdownContent.Add(_dropdownMenu);

            // 设置默认位置
            UpdatePosition();
        }

        /// <summary>
        /// 设置事件处理
        /// </summary>
        private void SetupEventHandlers()
        {
            // 点击触发器事件
            if (_dropdownTrigger != null)
            {
                _dropdownTrigger.RegisterCallback<ClickEvent>(OnTriggerClick);
                _dropdownTrigger.RegisterCallback<MouseEnterEvent>(OnTriggerMouseEnter);
                _dropdownTrigger.RegisterCallback<MouseLeaveEvent>(OnTriggerMouseLeave);
            }

            // 内容区域事件
            if (_dropdownContent != null)
            {
                _dropdownContent.RegisterCallback<MouseEnterEvent>(OnContentMouseEnter);
                _dropdownContent.RegisterCallback<MouseLeaveEvent>(OnContentMouseLeave);
            }

            // 全局点击事件处理
            RegisterCallback<PointerDownEvent>(OnGlobalClick, TrickleDown.TrickleDown);
        }

        /// <summary>
        /// 更新可见性
        /// </summary>
        private void UpdateVisibility()
        {
            if (_isOpen)
            {
                _dropdownContent.RemoveFromClassList("daisy-hidden");
                _dropdownContent.AddToClassList("daisy-visible");
                AddToClassList("daisy-dropdown-open");
                OnOpen?.Invoke();
            }
            else
            {
                _dropdownContent.AddToClassList("daisy-hidden");
                _dropdownContent.RemoveFromClassList("daisy-visible");
                RemoveFromClassList("daisy-dropdown-open");
                OnClose?.Invoke();
            }
        }

        /// <summary>
        /// 更新位置
        /// </summary>
        private void UpdatePosition()
        {
            if (_dropdownContent != null)
            {
                // 移除所有位置类
                _dropdownContent.RemoveFromClassList("daisy-dropdown-top");
                _dropdownContent.RemoveFromClassList("daisy-dropdown-bottom");
                _dropdownContent.RemoveFromClassList("daisy-dropdown-left");
                _dropdownContent.RemoveFromClassList("daisy-dropdown-right");
                _dropdownContent.RemoveFromClassList("daisy-dropdown-end");

                // 添加当前位置类
                _dropdownContent.AddToClassList($"daisy-dropdown-{_position}");
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 触发器点击事件
        /// </summary>
        private void OnTriggerClick(ClickEvent evt)
        {
            if (_clickTrigger)
            {
                Toggle();
                evt.StopPropagation();
            }
        }

        /// <summary>
        /// 触发器鼠标进入事件
        /// </summary>
        private void OnTriggerMouseEnter(MouseEnterEvent evt)
        {
            if (_hoverTrigger)
            {
                Open();
            }
        }

        /// <summary>
        /// 触发器鼠标离开事件
        /// </summary>
        private void OnTriggerMouseLeave(MouseLeaveEvent evt)
        {
            if (_hoverTrigger)
            {
                // 延迟关闭，给用户时间移动到菜单
                schedule.Execute(() =>
                {
                    if (!_dropdownContent.ContainsPoint(_dropdownContent.WorldToLocal(evt.mousePosition)))
                    {
                        Close();
                    }
                }).StartingIn(100);
            }
        }

        /// <summary>
        /// 内容鼠标进入事件
        /// </summary>
        private void OnContentMouseEnter(MouseEnterEvent evt)
        {
            // 阻止悬停模式下的自动关闭
        }

        /// <summary>
        /// 内容鼠标离开事件
        /// </summary>
        private void OnContentMouseLeave(MouseLeaveEvent evt)
        {
            if (_hoverTrigger)
            {
                Close();
            }
        }

        /// <summary>
        /// 全局点击事件
        /// </summary>
        private void OnGlobalClick(PointerDownEvent evt)
        {
            if (_closeOnClickOutside && _isOpen)
            {
                if (!this.ContainsPoint(this.WorldToLocal(evt.position)))
                {
                    Close();
                }
            }
        }

        /// <summary>
        /// 菜单项点击事件
        /// </summary>
        private void OnMenuItemClick(ClickEvent evt)
        {
            var target = evt.target as VisualElement;
            OnItemClick?.Invoke(target);
            
            // 点击菜单项后关闭下拉菜单
            Close();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 打开下拉菜单
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown Open()
        {
            IsOpen = true;
            return this;
        }

        /// <summary>
        /// 关闭下拉菜单
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown Close()
        {
            IsOpen = false;
            return this;
        }

        /// <summary>
        /// 切换下拉菜单开启状态
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown Toggle()
        {
            IsOpen = !IsOpen;
            return this;
        }

        /// <summary>
        /// 设置触发器
        /// </summary>
        /// <param name="trigger">触发器元素</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown SetTrigger(VisualElement trigger)
        {
            if (trigger != null)
            {
                _dropdownTrigger.Clear();
                _dropdownTrigger.Add(trigger);
            }
            return this;
        }

        /// <summary>
        /// 添加菜单项
        /// </summary>
        /// <param name="item">菜单项元素</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown AddMenuItem(VisualElement item)
        {
            if (item != null)
            {
                item.AddToClassList("daisy-dropdown-item");
                item.RegisterCallback<ClickEvent>(OnMenuItemClick);
                _menuItems.Add(item);
                _dropdownMenu.Add(item);
            }
            return this;
        }

        /// <summary>
        /// 添加文本菜单项
        /// </summary>
        /// <param name="text">菜单项文本</param>
        /// <param name="onClick">点击回调</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown AddMenuItem(string text, Action onClick = null)
        {
            var item = new Button(() => onClick?.Invoke())
            {
                text = text
            };
            item.AddToClassList("daisy-dropdown-item-button");
            return AddMenuItem(item);
        }

        /// <summary>
        /// 添加分隔线
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown AddSeparator()
        {
            var separator = new VisualElement();
            separator.AddToClassList("daisy-dropdown-separator");
            _dropdownMenu.Add(separator);
            return this;
        }

        /// <summary>
        /// 清空菜单项
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown ClearMenuItems()
        {
            _menuItems.Clear();
            _dropdownMenu.Clear();
            return this;
        }

        /// <summary>
        /// 设置触发方式
        /// </summary>
        /// <param name="clickTrigger">点击触发</param>
        /// <param name="hoverTrigger">悬停触发</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown SetTriggerMode(bool clickTrigger = true, bool hoverTrigger = false)
        {
            ClickTrigger = clickTrigger;
            HoverTrigger = hoverTrigger;
            return this;
        }

        /// <summary>
        /// 设置位置
        /// </summary>
        /// <param name="position">位置名称（top, bottom, left, right, end）</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown SetPosition(string position)
        {
            Position = position;
            return this;
        }

        /// <summary>
        /// 注册打开事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown OnOpenEvent(Action callback)
        {
            OnOpen += callback;
            return this;
        }

        /// <summary>
        /// 注册关闭事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown OnCloseEvent(Action callback)
        {
            OnClose += callback;
            return this;
        }

        /// <summary>
        /// 注册菜单项点击事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown OnItemClickEvent(Action<VisualElement> callback)
        {
            OnItemClick += callback;
            return this;
        }

        #endregion

        #region 修饰符方法

        /// <summary>
        /// 设置悬停触发
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown SetHover()
        {
            HoverTrigger = true;
            ClickTrigger = false;
            return this;
        }

        /// <summary>
        /// 设置右侧对齐
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown SetEnd()
        {
            Position = "end";
            return this;
        }

        /// <summary>
        /// 设置顶部显示
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown SetTop()
        {
            Position = "top";
            return this;
        }

        /// <summary>
        /// 设置底部显示
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown SetBottom()
        {
            Position = "bottom";
            return this;
        }

        /// <summary>
        /// 设置左侧显示
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown SetLeft()
        {
            Position = "left";
            return this;
        }

        /// <summary>
        /// 设置右侧显示
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyDropdown SetRight()
        {
            Position = "right";
            return this;
        }

        #endregion

        #region 重写方法

        /// <summary>
        /// 组件特定的初始化
        /// </summary>
        protected override void OnInitialize()
        {
            Logging.LogInfo("DaisyDropdown", "下拉菜单组件初始化完成");
        }

        /// <summary>
        /// 清理事件
        /// </summary>
        public void Dispose()
        {
            OnOpen = null;
            OnClose = null;
            OnItemClick = null;
            _menuItems.Clear();
        }

        #endregion
    }
}