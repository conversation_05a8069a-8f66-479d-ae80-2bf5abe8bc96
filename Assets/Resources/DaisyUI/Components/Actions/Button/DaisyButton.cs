using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.DaisyUI.Components.Actions
{
    /// <summary>
    /// DaisyUI按钮组件
    /// 基于Unity Button实现，提供DaisyUI样式和链式调用API
    /// </summary>
    [UxmlElement("DaisyButton")]
    public partial class DaisyButton : DaisyComponent
    {
        #region 私有字段

        private const string TEMPLATE_PATH = "DaisyUI/Components/Actions/Button/DaisyButton";
        
        private Button _button;
        private string _text = "Button";
        private bool _isLoading = false;
        private VisualElement _loadingIndicator;

        #endregion

        #region 属性

        /// <summary>
        /// 按钮文本
        /// </summary>
        [UxmlAttribute]
        public string Text
        {
            get => _text;
            set
            {
                _text = value ?? string.Empty;
                if (_button != null)
                {
                    _button.text = _text;
                }
            }
        }

        /// <summary>
        /// 是否正在加载
        /// </summary>
        [UxmlAttribute]
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                UpdateLoadingState();
            }
        }

        /// <summary>
        /// 底层Button元素
        /// </summary>
        public Button Button => _button;

        /// <summary>
        /// 模板路径
        /// </summary>
        protected override string TemplatePath => TEMPLATE_PATH;

        #endregion

        #region 构造函数

        /// <summary>
        /// 无参数构造函数（用于UxmlElement）
        /// </summary>
        public DaisyButton() : base(DaisyUtilities.ComponentTypes.Button)
        {
            _text = "Button";
            CreateButton();
        }

        /// <summary>
        /// 带文本的构造函数
        /// </summary>
        /// <param name="text">按钮文本</param>
        public DaisyButton(string text) : base(DaisyUtilities.ComponentTypes.Button)
        {
            _text = text ?? "Button";
            CreateButton();
        }

        #endregion

        #region 静态工厂方法

        /// <summary>
        /// 创建按钮
        /// </summary>
        /// <param name="text">按钮文本</param>
        /// <returns>DaisyButton实例</returns>
        public static DaisyButton Create(string text = "Button")
        {
            return new DaisyButton(text);
        }

        /// <summary>
        /// 创建主要按钮
        /// </summary>
        /// <param name="text">按钮文本</param>
        /// <returns>DaisyButton实例</returns>
        public static DaisyButton Primary(string text = "Button")
        {
            return Create(text).SetPrimary();
        }

        /// <summary>
        /// 创建次要按钮
        /// </summary>
        /// <param name="text">按钮文本</param>
        /// <returns>DaisyButton实例</returns>
        public static DaisyButton Secondary(string text = "Button")
        {
            return Create(text).SetSecondary();
        }

        /// <summary>
        /// 创建强调按钮
        /// </summary>
        /// <param name="text">按钮文本</param>
        /// <returns>DaisyButton实例</returns>
        public static DaisyButton Accent(string text = "Button")
        {
            return Create(text).SetAccent();
        }

        /// <summary>
        /// 创建幽灵按钮
        /// </summary>
        /// <param name="text">按钮文本</param>
        /// <returns>DaisyButton实例</returns>
        public static DaisyButton Ghost(string text = "Button")
        {
            return Create(text).SetGhost();
        }

        /// <summary>
        /// 创建链接按钮
        /// </summary>
        /// <param name="text">按钮文本</param>
        /// <returns>DaisyButton实例</returns>
        public static DaisyButton Link(string text = "Button")
        {
            return Create(text).SetLink();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 创建按钮元素
        /// </summary>
        private void CreateButton()
        {
            // 首先尝试从模板中查找按钮元素
            _button = this.Q<Button>("daisy-button");
            
            if (_button == null)
            {
                // 如果模板中没有找到，则手动创建（回退机制）
                _button = new Button();
                _button.name = "daisy-button";
                Add(_button);
                
                if (DaisyUtilities.IsDebugMode)
                {
                    Logging.LogInfo("DaisyButton", "模板中未找到按钮元素，使用手动创建");
                }
            }
            else
            {
                if (DaisyUtilities.IsDebugMode)
                {
                    Logging.LogInfo("DaisyButton", "从模板中找到按钮元素");
                }
            }

            // 设置按钮文本
            _button.text = _text;

            // 移除Unity默认样式
            _button.RemoveFromClassList("unity-button");

            if (DaisyUtilities.IsDebugMode)
            {
                Logging.LogInfo("DaisyButton", $"按钮创建完成: {_text}");
            }
        }

        protected override void OnInitialize()
        {
            base.OnInitialize();

            // 确保按钮已创建
            if (_button == null)
            {
                CreateButton();
            }
        }

        #endregion

        #region 变体方法

        /// <summary>
        /// 设置为主要按钮
        /// </summary>
        public DaisyButton SetPrimary()
        {
            return (DaisyButton)WithVariant(DaisyUtilities.Variants.Primary);
        }

        /// <summary>
        /// 设置为次要按钮
        /// </summary>
        public DaisyButton SetSecondary()
        {
            return (DaisyButton)WithVariant(DaisyUtilities.Variants.Secondary);
        }

        /// <summary>
        /// 设置为强调按钮
        /// </summary>
        public DaisyButton SetAccent()
        {
            return (DaisyButton)WithVariant(DaisyUtilities.Variants.Accent);
        }

        /// <summary>
        /// 设置为中性按钮
        /// </summary>
        public DaisyButton SetNeutral()
        {
            return (DaisyButton)WithVariant(DaisyUtilities.Variants.Neutral);
        }

        /// <summary>
        /// 设置为幽灵按钮
        /// </summary>
        public DaisyButton SetGhost()
        {
            return (DaisyButton)WithVariant(DaisyUtilities.Variants.Ghost);
        }

        /// <summary>
        /// 设置为链接按钮
        /// </summary>
        public DaisyButton SetLink()
        {
            return (DaisyButton)WithVariant(DaisyUtilities.Variants.Link);
        }

        /// <summary>
        /// 设置为信息按钮
        /// </summary>
        public DaisyButton SetInfo()
        {
            return (DaisyButton)WithVariant(DaisyUtilities.Variants.Info);
        }

        /// <summary>
        /// 设置为成功按钮
        /// </summary>
        public DaisyButton SetSuccess()
        {
            return (DaisyButton)WithVariant(DaisyUtilities.Variants.Success);
        }

        /// <summary>
        /// 设置为警告按钮
        /// </summary>
        public DaisyButton SetWarning()
        {
            return (DaisyButton)WithVariant(DaisyUtilities.Variants.Warning);
        }

        /// <summary>
        /// 设置为错误按钮
        /// </summary>
        public DaisyButton SetError()
        {
            return (DaisyButton)WithVariant(DaisyUtilities.Variants.Error);
        }

        #endregion

        #region 尺寸方法

        /// <summary>
        /// 设置为超小尺寸
        /// </summary>
        public DaisyButton SetExtraSmall()
        {
            return (DaisyButton)WithSize(DaisyUtilities.Sizes.ExtraSmall);
        }

        /// <summary>
        /// 设置为小尺寸
        /// </summary>
        public DaisyButton SetSmall()
        {
            return (DaisyButton)WithSize(DaisyUtilities.Sizes.Small);
        }

        /// <summary>
        /// 设置为中等尺寸
        /// </summary>
        public DaisyButton SetMedium()
        {
            return (DaisyButton)WithSize(DaisyUtilities.Sizes.Medium);
        }

        /// <summary>
        /// 设置为大尺寸
        /// </summary>
        public DaisyButton SetLarge()
        {
            return (DaisyButton)WithSize(DaisyUtilities.Sizes.Large);
        }

        /// <summary>
        /// 设置为超大尺寸
        /// </summary>
        public DaisyButton SetExtraLarge()
        {
            return (DaisyButton)WithSize(DaisyUtilities.Sizes.ExtraLarge);
        }

        #endregion

        #region 修饰符方法

        /// <summary>
        /// 设置为轮廓样式
        /// </summary>
        public DaisyButton SetOutline()
        {
            return (DaisyButton)SetModifier(DaisyUtilities.Modifiers.Outline);
        }

        /// <summary>
        /// 设置为宽按钮
        /// </summary>
        public DaisyButton SetWide()
        {
            return (DaisyButton)SetModifier(DaisyUtilities.Modifiers.Wide);
        }

        /// <summary>
        /// 设置为块级按钮
        /// </summary>
        public DaisyButton SetBlock()
        {
            return (DaisyButton)SetModifier(DaisyUtilities.Modifiers.Block);
        }

        /// <summary>
        /// 设置为圆形按钮
        /// </summary>
        public DaisyButton SetCircle()
        {
            return (DaisyButton)SetModifier(DaisyUtilities.Modifiers.Circle);
        }

        /// <summary>
        /// 设置为方形按钮
        /// </summary>
        public DaisyButton SetSquare()
        {
            return (DaisyButton)SetModifier(DaisyUtilities.Modifiers.Square);
        }

        /// <summary>
        /// 设置为加载状态
        /// </summary>
        public DaisyButton SetLoading(bool loading = true)
        {
            IsLoading = loading;
            return this;
        }

        /// <summary>
        /// 设置为活跃状态
        /// </summary>
        public DaisyButton SetActive(bool active = true)
        {
            return (DaisyButton)SetModifier(DaisyUtilities.Modifiers.Active, active);
        }

        #endregion

        #region 事件方法

        /// <summary>
        /// 添加点击事件
        /// </summary>
        /// <param name="callback">回调函数</param>
        public DaisyButton OnClick(System.Action callback)
        {
            if (_button != null && callback != null)
            {
                _button.clicked += callback;
            }
            return this;
        }

        /// <summary>
        /// 移除所有点击事件
        /// </summary>
        public DaisyButton ClearClickEvents()
        {
            if (_button != null)
            {
                // Unity UI Toolkit 不提供直接清除事件的方法
                // 需要重新创建按钮
                var oldText = _button.text;
                Remove(_button);
                _button = null;
                CreateButton();
                _button.text = oldText;
            }
            return this;
        }

        #endregion

        #region 文本方法

        /// <summary>
        /// 设置按钮文本
        /// </summary>
        /// <param name="text">文本内容</param>
        public DaisyButton SetText(string text)
        {
            Text = text;
            return this;
        }

        /// <summary>
        /// 清空按钮文本
        /// </summary>
        public DaisyButton ClearText()
        {
            Text = string.Empty;
            return this;
        }

        #endregion

        #region 加载状态方法

        /// <summary>
        /// 更新加载状态
        /// </summary>
        private void UpdateLoadingState()
        {
            SetModifier(DaisyUtilities.Modifiers.Loading, _isLoading);

            if (_isLoading)
            {
                CreateLoadingIndicator();
                SetDisabled(true);
            }
            else
            {
                RemoveLoadingIndicator();
                SetDisabled(false);
            }
        }

        /// <summary>
        /// 创建加载指示器
        /// </summary>
        private void CreateLoadingIndicator()
        {
            if (_loadingIndicator == null && _button != null)
            {
                _loadingIndicator = new VisualElement();
                _loadingIndicator.AddToClassList("daisy-loading-spinner");
                _button.Add(_loadingIndicator);

                if (DaisyUtilities.IsDebugMode)
                {
                    Logging.LogInfo("DaisyButton", "加载指示器已创建");
                }
            }
        }

        /// <summary>
        /// 移除加载指示器
        /// </summary>
        private void RemoveLoadingIndicator()
        {
            if (_loadingIndicator != null)
            {
                _loadingIndicator.RemoveFromHierarchy();
                _loadingIndicator = null;

                if (DaisyUtilities.IsDebugMode)
                {
                    Logging.LogInfo("DaisyButton", "加载指示器已移除");
                }
            }
        }

        #endregion

        #region 重写方法

        public override DaisyComponent SetDisabled(bool disabled = true)
        {
            base.SetDisabled(disabled);

            if (_button != null)
            {
                _button.SetEnabled(!disabled);
            }

            return this;
        }

        public override void Reset()
        {
            base.Reset();

            // 重置按钮特定状态
            _text = "Button";
            _isLoading = false;
            RemoveLoadingIndicator();

            if (_button != null)
            {
                _button.text = _text;
                _button.SetEnabled(true);
            }
        }

        public override string GetDebugInfo()
        {
            var baseInfo = base.GetDebugInfo();
            return $"{baseInfo}, Text: '{_text}', IsLoading: {_isLoading}";
        }

        public override bool ValidateComponent()
        {
            if (!base.ValidateComponent())
                return false;

            if (_button == null)
            {
                Logging.LogError("DaisyButton", "Button 元素为 null");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 设置尺寸（重写基类方法以返回正确类型）
        /// </summary>
        /// <param name="size">尺寸名称</param>
        /// <returns>当前实例，支持链式调用</returns>
        public new DaisyButton WithSize(string size)
        {
            base.WithSize(size);
            return this;
        }

        #endregion
    }
}