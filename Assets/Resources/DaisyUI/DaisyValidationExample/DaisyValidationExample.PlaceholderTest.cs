using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Components.DataInput;
using BlastingDesign.UI.DaisyUI.Builders;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.DaisyUI
{
    /// <summary>
    /// DaisyUI验证示例 - DaisyInput placeholder 专项测试
    /// 专门测试 DaisyInput 组件的占位符功能
    /// </summary>
    public partial class DaisyValidationExample : MonoBehaviour
    {
        #region DaisyInput Placeholder 专项测试

        /// <summary>
        /// 测试 DaisyInput 占位符功能
        /// </summary>
        private void TestDaisyInputPlaceholder()
        {
            LogTest("DaisyInput Placeholder 专项测试");

            try
            {
                // 创建测试容器
                var placeholderContainer = new VisualElement();
                placeholderContainer.AddToClassList("daisy-placeholder-test");
                placeholderContainer.style.paddingTop = 20;
                placeholderContainer.style.paddingBottom = 20;
                placeholderContainer.style.paddingLeft = 20;
                placeholderContainer.style.paddingRight = 20;
                placeholderContainer.style.marginBottom = 20;
                placeholderContainer.style.backgroundColor = new Color(0.96f, 0.98f, 1f, 1f);
                placeholderContainer.style.borderTopLeftRadius = 8;
                placeholderContainer.style.borderTopRightRadius = 8;
                placeholderContainer.style.borderBottomLeftRadius = 8;
                placeholderContainer.style.borderBottomRightRadius = 8;

                var heading = new Label("DaisyInput Placeholder 专项测试");
                heading.style.fontSize = 18;
                heading.style.unityFontStyleAndWeight = FontStyle.Bold;
                heading.style.marginBottom = 10;
                heading.style.color = Color.black;
                placeholderContainer.Add(heading);

                var description = new Label("测试输入框占位符的显示、隐藏和交互行为");
                description.style.fontSize = 12;
                description.style.marginBottom = 15;
                description.style.color = new Color(0.5f, 0.5f, 0.5f, 1f);
                placeholderContainer.Add(description);

                // 测试1：基础占位符功能
                var basicSection = new Label("1. 基础占位符显示:");
                basicSection.style.fontSize = 14;
                basicSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                basicSection.style.marginBottom = 10;
                placeholderContainer.Add(basicSection);

                var basicInput = DaisyInput.Create("请输入您的姓名");
                basicInput.SetLabel("姓名");
                placeholderContainer.Add(basicInput);

                // 测试2：不同类型输入框的占位符
                var typeSection = new Label("2. 不同类型输入框的占位符:");
                typeSection.style.fontSize = 14;
                typeSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                typeSection.style.marginBottom = 10;
                typeSection.style.marginTop = 15;
                placeholderContainer.Add(typeSection);

                var typeGrid = DaisyBuilder.Grid(2);
                typeGrid.AddToClassList("daisy-gap-4");

                var emailInput = DaisyInput.Create("请输入邮箱地址")
                    .SetLabel("邮箱")
                    .SetType("email");
                typeGrid.Add(emailInput);

                var passwordInput = DaisyInput.Create("请输入密码")
                    .SetLabel("密码")
                    .SetType("password");
                typeGrid.Add(passwordInput);

                var numberInput = DaisyInput.Create("请输入数字")
                    .SetLabel("数字")
                    .SetType("number");
                typeGrid.Add(numberInput);

                var phoneInput = DaisyInput.Create("请输入手机号码")
                    .SetLabel("手机号");
                typeGrid.Add(phoneInput);

                placeholderContainer.Add(typeGrid);

                // 测试3：带初始值的输入框（占位符应该隐藏）
                var initialValueSection = new Label("3. 带初始值的输入框 (占位符应该隐藏):");
                initialValueSection.style.fontSize = 14;
                initialValueSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                initialValueSection.style.marginBottom = 10;
                initialValueSection.style.marginTop = 15;
                placeholderContainer.Add(initialValueSection);

                var prefilledInput = DaisyInput.Create("这个占位符应该被隐藏")
                    .SetLabel("预填充内容")
                    .SetValue("张三")
                    .SetHelperText("这个输入框有初始值，占位符应该隐藏");
                placeholderContainer.Add(prefilledInput);

                // 测试4：不同尺寸的占位符
                var sizeSection = new Label("4. 不同尺寸的占位符:");
                sizeSection.style.fontSize = 14;
                sizeSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                sizeSection.style.marginBottom = 10;
                sizeSection.style.marginTop = 15;
                placeholderContainer.Add(sizeSection);

                var sizeContainer = new VisualElement();
                sizeContainer.AddToClassList("daisy-gap-2");

                var sizes = new[] { "xs", "sm", "md", "lg", "xl" };
                foreach (var size in sizes)
                {
                    var sizeInput = DaisyInput.Create($"请输入{size.ToUpper()}尺寸内容")
                        .SetLabel($"{size.ToUpper()}尺寸")
                        .WithSize(size);
                    sizeContainer.Add(sizeInput);
                }

                placeholderContainer.Add(sizeContainer);

                // 测试5：不同状态的占位符
                var stateSection = new Label("5. 不同状态的占位符:");
                stateSection.style.fontSize = 14;
                stateSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                stateSection.style.marginBottom = 10;
                stateSection.style.marginTop = 15;
                placeholderContainer.Add(stateSection);

                var stateGrid = DaisyBuilder.Grid(2);
                stateGrid.AddToClassList("daisy-gap-4");

                var errorInput = DaisyInput.Create("请输入正确的邮箱格式")
                    .SetLabel("错误状态")
                    .SetError()
                    .SetHelperText("邮箱格式不正确");
                stateGrid.Add(errorInput);

                var successInput = DaisyInput.Create("请输入用户名")
                    .SetLabel("成功状态")
                    .SetSuccess()
                    .SetHelperText("用户名可用");
                stateGrid.Add(successInput);

                var warningInput = DaisyInput.Create("请输入强密码")
                    .SetLabel("警告状态")
                    .SetWarning()
                    .SetHelperText("密码强度较弱");
                stateGrid.Add(warningInput);

                var infoInput = DaisyInput.Create("请输入个人简介")
                    .SetLabel("信息状态")
                    .SetInfo()
                    .SetHelperText("可选填写");
                stateGrid.Add(infoInput);

                placeholderContainer.Add(stateGrid);

                // 测试6：禁用和只读状态
                var specialStateSection = new Label("6. 特殊状态的占位符:");
                specialStateSection.style.fontSize = 14;
                specialStateSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                specialStateSection.style.marginBottom = 10;
                specialStateSection.style.marginTop = 15;
                placeholderContainer.Add(specialStateSection);

                var specialGrid = DaisyBuilder.Grid(2);
                specialGrid.AddToClassList("daisy-gap-4");

                var disabledInput = DaisyInput.Create("此输入框已禁用")
                    .SetLabel("禁用状态")
                    .SetDisabled()
                    .SetHelperText("输入框已禁用");
                specialGrid.Add(disabledInput);

                var readonlyInput = DaisyInput.Create("此输入框为只读")
                    .SetLabel("只读状态")
                    .SetReadOnly()
                    .SetValue("只读内容")
                    .SetHelperText("输入框为只读");
                specialGrid.Add(readonlyInput);

                placeholderContainer.Add(specialGrid);

                // 测试7：长占位符文本
                var longPlaceholderSection = new Label("7. 长占位符文本测试:");
                longPlaceholderSection.style.fontSize = 14;
                longPlaceholderSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                longPlaceholderSection.style.marginBottom = 10;
                longPlaceholderSection.style.marginTop = 15;
                placeholderContainer.Add(longPlaceholderSection);

                var longInput = DaisyInput.Create("请输入详细的个人信息包括姓名、年龄、职业、联系方式等详细信息")
                    .SetLabel("长占位符文本")
                    .SetHelperText("测试长占位符文本的显示效果");
                placeholderContainer.Add(longInput);

                // 测试8：动态修改占位符
                var dynamicSection = new Label("8. 动态修改占位符:");
                dynamicSection.style.fontSize = 14;
                dynamicSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                dynamicSection.style.marginBottom = 10;
                dynamicSection.style.marginTop = 15;
                placeholderContainer.Add(dynamicSection);

                var dynamicInput = DaisyInput.Create("初始占位符文本")
                    .SetLabel("动态占位符")
                    .SetHelperText("点击下方按钮修改占位符文本");
                placeholderContainer.Add(dynamicInput);

                // 添加按钮来动态修改占位符
                var buttonContainer = new VisualElement();
                buttonContainer.style.flexDirection = FlexDirection.Row;
                buttonContainer.style.marginTop = 5;

                var changeButton1 = new Button(() =>
                {
                    dynamicInput.SetPlaceholder("占位符已更改为新文本");
                    if (logDetailedResults)
                    {
                        Logging.LogInfo("DaisyValidation", "占位符已更改");
                    }
                });
                changeButton1.text = "更改占位符";
                changeButton1.style.marginRight = 5;
                buttonContainer.Add(changeButton1);

                var changeButton2 = new Button(() =>
                {
                    dynamicInput.SetPlaceholder("请输入更新后的内容");
                    if (logDetailedResults)
                    {
                        Logging.LogInfo("DaisyValidation", "占位符已再次更改");
                    }
                });
                changeButton2.text = "再次更改";
                changeButton2.style.marginRight = 5;
                buttonContainer.Add(changeButton2);

                var resetButton = new Button(() =>
                {
                    dynamicInput.SetPlaceholder("初始占位符文本");
                    if (logDetailedResults)
                    {
                        Logging.LogInfo("DaisyValidation", "占位符已重置");
                    }
                });
                resetButton.text = "重置";
                buttonContainer.Add(resetButton);

                placeholderContainer.Add(buttonContainer);

                // 添加交互说明
                var interactionSection = new Label("9. 交互测试说明:");
                interactionSection.style.fontSize = 14;
                interactionSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                interactionSection.style.marginBottom = 10;
                interactionSection.style.marginTop = 15;
                placeholderContainer.Add(interactionSection);

                var instructions = new Label("• 点击任意输入框，占位符应该消失\n• 在输入框中输入内容，占位符应该保持隐藏\n• 清空内容并失去焦点，占位符应该重新显示\n• 预填充内容的输入框，占位符应该始终隐藏");
                instructions.style.fontSize = 12;
                instructions.style.color = new Color(0.4f, 0.4f, 0.4f, 1f);
                instructions.style.whiteSpace = WhiteSpace.Normal;
                placeholderContainer.Add(instructions);

                root.Add(placeholderContainer);

                // 验证占位符功能
                VerifyPlaceholderFunctionality(basicInput, emailInput, prefilledInput);

                LogTestPass("DaisyInput Placeholder 专项测试通过");
            }
            catch (System.Exception ex)
            {
                LogTestFail($"DaisyInput Placeholder 专项测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证占位符功能的正确性
        /// </summary>
        private void VerifyPlaceholderFunctionality(params DaisyInput[] inputs)
        {
            foreach (var input in inputs)
            {
                if (input == null) continue;

                var placeholderLabel = input.Q<Label>("placeholder-label");
                var textField = input.Q<TextField>("input-field");

                if (placeholderLabel == null || textField == null)
                {
                    LogTestFail($"输入框 '{input.Label}' 缺少必要的占位符元素");
                    continue;
                }

                // 验证占位符文本
                if (string.IsNullOrEmpty(placeholderLabel.text))
                {
                    LogTestFail($"输入框 '{input.Label}' 的占位符文本为空");
                    continue;
                }

                // 验证占位符样式类
                if (!placeholderLabel.ClassListContains("daisy-input-placeholder-label"))
                {
                    LogTestFail($"输入框 '{input.Label}' 的占位符缺少样式类");
                    continue;
                }

                // 验证占位符显示逻辑
                if (string.IsNullOrEmpty(textField.value))
                {
                    // 空输入框，占位符应该显示
                    if (placeholderLabel.style.display == DisplayStyle.None)
                    {
                        LogTestFail($"输入框 '{input.Label}' 在空值时占位符应该显示");
                        continue;
                    }
                }
                else
                {
                    // 有值输入框，占位符应该隐藏
                    if (placeholderLabel.style.display != DisplayStyle.None)
                    {
                        LogTestFail($"输入框 '{input.Label}' 在有值时占位符应该隐藏");
                        continue;
                    }
                }

                if (logDetailedResults)
                {
                    Logging.LogInfo("DaisyValidation", $"输入框 '{input.Label}' 的占位符功能验证通过");
                }
            }
        }

        /// <summary>
        /// 运行占位符专项测试
        /// </summary>
        [System.Obsolete("使用 TestDaisyInputPlaceholder() 代替")]
        public void RunPlaceholderTest()
        {
            TestDaisyInputPlaceholder();
        }

        #endregion
    }
}