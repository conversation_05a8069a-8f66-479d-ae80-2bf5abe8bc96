using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Components.Actions;
using BlastingDesign.UI.DaisyUI.Components.DataDisplay;
using BlastingDesign.UI.DaisyUI.Builders;

namespace BlastingDesign.UI.DaisyUI
{
    /// <summary>
    /// DaisyUI验证示例 - DaisyCard组件测试
    /// 测试DaisyCard组件的所有功能
    /// </summary>
    public partial class DaisyValidationExample : MonoBehaviour
    {
        #region DaisyCard组件测试

        /// <summary>
        /// 测试DaisyCard组件
        /// </summary>
        private void TestDaisyCard()
        {
            LogTest("DaisyCard 组件测试");

            try
            {
                // 测试基础卡片创建
                var basicCard = DaisyCard.Create();
                if (basicCard == null)
                {
                    throw new System.Exception("基础卡片创建失败");
                }

                // 测试带标题的卡片
                var titleCard = DaisyCard.Create("测试标题");
                if (titleCard == null || titleCard.Title != "测试标题")
                {
                    throw new System.Exception("带标题卡片创建失败");
                }

                // 测试卡片内容设置
                var contentCard = DaisyCard.Create()
                    .SetTitle("内容测试")
                    .SetContent("这是测试内容，用于验证卡片的内容显示功能。");

                if (contentCard.Title != "内容测试")
                {
                    throw new System.Exception("卡片标题设置失败");
                }

                // 测试卡片操作按钮
                var button1 = new DaisyButton("确定").SetPrimary();
                var button2 = new DaisyButton("取消").SetGhost();

                var actionCard = DaisyCard.Create()
                    .SetTitle("操作测试")
                    .SetContent("测试操作按钮功能")
                    .AddActions(button1, button2);

                if (actionCard.CardActions == null || actionCard.CardActions.childCount != 2)
                {
                    throw new System.Exception("卡片操作按钮添加失败");
                }

                // 测试修饰符
                var modifierCard = DaisyCard.Create()
                    .SetTitle("修饰符测试")
                    .SetContent("测试各种修饰符效果")
                    .SetCompact()
                    .SetBordered()
                    .SetShadow();

                if (!modifierCard.ClassListContains("daisy-card-compact") ||
                    !modifierCard.ClassListContains("daisy-card-bordered") ||
                    !modifierCard.ClassListContains("daisy-card-shadow"))
                {
                    throw new System.Exception("卡片修饰符设置失败");
                }

                // 测试玻璃效果卡片
                var glassCard = DaisyCard.Create()
                    .SetTitle("玻璃效果")
                    .SetContent("测试玻璃效果修饰符")
                    .SetGlass();

                if (!glassCard.ClassListContains("daisy-card-glass"))
                {
                    throw new System.Exception("玻璃效果设置失败");
                }

                // 测试侧边布局卡片
                var sideCard = DaisyCard.Create()
                    .SetTitle("侧边布局")
                    .SetContent("测试侧边布局效果，图片和内容并排显示")
                    .SetSide();

                if (!sideCard.ClassListContains("daisy-card-side"))
                {
                    throw new System.Exception("侧边布局设置失败");
                }

                // 测试复杂卡片组合
                var complexCard = DaisyCard.Create()
                    .SetTitle("复杂卡片示例")
                    .SetContent("这是一个包含多种功能的复杂卡片示例，展示了标题、内容、操作按钮等完整功能。")
                    .AddActions(
                        DaisyButton.Primary("主要操作"),
                        DaisyButton.Secondary("次要操作"),
                        DaisyButton.Ghost("其他操作")
                    )
                    .SetBordered()
                    .SetShadow();

                // 测试卡片内容的动态修改
                var dynamicCard = DaisyCard.Create("动态内容");
                dynamicCard.SetContent("初始内容");
                dynamicCard.SetContent("修改后的内容");

                // 测试添加自定义内容元素
                var customContentCard = DaisyCard.Create()
                    .SetTitle("自定义内容");

                var customElement = new VisualElement();
                customElement.style.height = 50;
                customElement.style.backgroundColor = new Color(0.9f, 0.9f, 1f, 1f);
                customElement.style.borderTopLeftRadius = 4;
                customElement.style.borderTopRightRadius = 4;
                customElement.style.borderBottomLeftRadius = 4;
                customElement.style.borderBottomRightRadius = 4;
                customElement.style.marginTop = 10;

                var customLabel = new Label("这是自定义的内容元素");
                customLabel.style.unityTextAlign = TextAnchor.MiddleCenter;
                customLabel.style.color = new Color(0.3f, 0.3f, 0.7f, 1f);
                customElement.Add(customLabel);

                customContentCard.AddContent(customElement);

                // 添加到UI中进行视觉验证
                var cardContainer = new VisualElement();
                cardContainer.AddToClassList("daisy-card-test");
                cardContainer.style.paddingTop = 20;
                cardContainer.style.paddingBottom = 20;
                cardContainer.style.paddingLeft = 20;
                cardContainer.style.paddingRight = 20;
                cardContainer.style.marginBottom = 20;
                cardContainer.style.backgroundColor = new Color(0.98f, 0.98f, 0.98f, 1f);
                cardContainer.style.borderTopLeftRadius = 8;
                cardContainer.style.borderTopRightRadius = 8;
                cardContainer.style.borderBottomLeftRadius = 8;
                cardContainer.style.borderBottomRightRadius = 8;

                var heading = new Label("DaisyCard 组件测试");
                heading.style.fontSize = 18;
                heading.style.unityFontStyleAndWeight = FontStyle.Bold;
                heading.style.marginBottom = 10;
                heading.style.color = Color.black;
                cardContainer.Add(heading);

                var description = new Label("测试卡片组件的标题、内容、操作区域和各种修饰符");
                description.style.fontSize = 12;
                description.style.marginBottom = 15;
                description.style.color = new Color(0.5f, 0.5f, 0.5f, 1f);
                cardContainer.Add(description);

                // 使用DaisyBuilder创建网格布局
                var cardGrid = DaisyBuilder.Grid(2);
                cardGrid.AddToClassList("daisy-gap-4");
                cardGrid.style.marginBottom = 20;

                // 第一行：基础功能
                cardGrid.Add(basicCard.SetContent("基础卡片，无标题"));
                cardGrid.Add(titleCard.SetContent("带标题的卡片"));

                // 第二行：内容和操作
                cardGrid.Add(contentCard);
                cardGrid.Add(actionCard);

                // 第三行：修饰符测试
                cardGrid.Add(modifierCard);
                cardGrid.Add(glassCard);

                cardContainer.Add(cardGrid);

                // 单独展示特殊布局
                var specialSection = new VisualElement();
                specialSection.style.marginTop = 20;

                var specialTitle = new Label("特殊布局测试:");
                specialTitle.style.fontSize = 14;
                specialTitle.style.unityFontStyleAndWeight = FontStyle.Bold;
                specialTitle.style.marginBottom = 10;
                specialSection.Add(specialTitle);

                specialSection.Add(sideCard);
                specialSection.Add(complexCard);
                specialSection.Add(customContentCard);

                cardContainer.Add(specialSection);

                // 添加功能说明
                var featureList = new VisualElement();
                featureList.style.marginTop = 20;
                featureList.style.paddingTop = 15;
                featureList.style.borderTopWidth = 1;
                featureList.style.borderTopColor = new Color(0.9f, 0.9f, 0.9f, 1f);

                var featureTitle = new Label("已验证功能：");
                featureTitle.style.fontSize = 14;
                featureTitle.style.unityFontStyleAndWeight = FontStyle.Bold;
                featureTitle.style.marginBottom = 8;
                featureList.Add(featureTitle);

                var features = new string[]
                {
                    "✓ 基础卡片创建",
                    "✓ 标题设置和显示",
                    "✓ 内容设置和显示",
                    "✓ 操作按钮添加",
                    "✓ 修饰符：紧凑、边框、阴影",
                    "✓ 玻璃效果",
                    "✓ 侧边布局",
                    "✓ 自定义内容元素",
                    "✓ 动态内容修改"
                };

                foreach (var feature in features)
                {
                    var featureLabel = new Label(feature);
                    featureLabel.style.fontSize = 11;
                    featureLabel.style.marginBottom = 2;
                    featureLabel.style.color = new Color(0.3f, 0.6f, 0.3f, 1f);
                    featureList.Add(featureLabel);
                }

                cardContainer.Add(featureList);
                root.Add(cardContainer);

                LogTestPass("DaisyCard 组件测试通过");
            }
            catch (System.Exception ex)
            {
                LogTestFail($"DaisyCard 组件测试失败: {ex.Message}");
            }
        }

        #endregion
    }
}
