using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Components.DataInput;
using BlastingDesign.UI.DaisyUI.Builders;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.DaisyUI
{
    /// <summary>
    /// DaisyUI验证示例 - DaisyInput组件测试
    /// 测试DaisyInput组件的所有功能
    /// </summary>
    public partial class DaisyValidationExample : MonoBehaviour
    {
        #region DaisyInput组件测试

        /// <summary>
        /// 测试DaisyInput组件
        /// </summary>
        private void TestDaisyInput()
        {
            LogTest("DaisyInput 组件测试");

            try
            {
                // 测试基础输入框创建
                var basicInput = DaisyInput.Create("请输入内容");
                if (basicInput == null)
                {
                    throw new System.Exception("基础输入框创建失败");
                }

                // 测试带标签的输入框
                var labelInput = DaisyInput.Create("用户名", "请输入用户名");
                if (labelInput == null || labelInput.Label != "用户名")
                {
                    throw new System.Exception("带标签输入框创建失败");
                }

                // 测试不同类型的输入框
                var passwordInput = DaisyInput.Create("请输入密码").SetType("password");
                var emailInput = DaisyInput.Create("请输入邮箱").SetType("email");
                var numberInput = DaisyInput.Create("请输入数字").SetType("number");

                if (passwordInput.InputType != "password" ||
                    emailInput.InputType != "email" ||
                    numberInput.InputType != "number")
                {
                    throw new System.Exception("输入框类型设置失败");
                }

                // 测试输入框状态
                var errorInput = DaisyInput.Create("错误状态")
                    .SetError()
                    .SetHelperText("这是错误提示信息");

                var successInput = DaisyInput.Create("成功状态")
                    .SetSuccess()
                    .SetHelperText("这是成功提示信息");

                var warningInput = DaisyInput.Create("警告状态")
                    .SetWarning()
                    .SetHelperText("这是警告提示信息");

                var infoInput = DaisyInput.Create("信息状态")
                    .SetInfo()
                    .SetHelperText("这是信息提示");

                if (!errorInput.ClassListContains("daisy-input-error") ||
                    !successInput.ClassListContains("daisy-input-success") ||
                    !warningInput.ClassListContains("daisy-input-warning") ||
                    !infoInput.ClassListContains("daisy-input-info"))
                {
                    throw new System.Exception("输入框状态设置失败");
                }

                // 测试修饰符
                var borderedInput = DaisyInput.Create("边框样式")
                    .SetBordered()
                    .SetLabel("边框输入框");

                var ghostInput = DaisyInput.Create("幽灵样式")
                    .SetGhost()
                    .SetLabel("幽灵输入框");

                if (!borderedInput.ClassListContains("daisy-input-bordered") ||
                    !ghostInput.ClassListContains("daisy-input-ghost"))
                {
                    throw new System.Exception("输入框修饰符设置失败");
                }

                // 测试值设置和回调
                var callbackInput = DaisyInput.Create("回调测试")
                    .SetValue("初始值")
                    .SetLabel("值变化监听")
                    .OnValueChanged(value => Logging.LogInfo("DaisyInput", $"值改变: {value}"));

                if (callbackInput.Value != "初始值")
                {
                    throw new System.Exception("输入框值设置失败");
                }

                // 测试禁用和只读状态
                var disabledInput = DaisyInput.Create("禁用状态")
                    .SetDisabled()
                    .SetLabel("禁用输入框");

                var readonlyInput = DaisyInput.Create("只读状态")
                    .SetReadOnly()
                    .SetValue("只读内容")
                    .SetLabel("只读输入框");

                if (!disabledInput.ClassListContains("daisy-input-disabled") ||
                    !readonlyInput.ClassListContains("daisy-input-readonly"))
                {
                    throw new System.Exception("输入框状态设置失败");
                }

                // 测试尺寸变体
                var largeInput = DaisyInput.Create("大尺寸输入框").WithSize("lg");
                var smallInput = DaisyInput.Create("小尺寸输入框").WithSize("sm");
                var extraSmallInput = DaisyInput.Create("超小输入框").WithSize("xs");

                if (!largeInput.ClassListContains("daisy-input-lg") ||
                    !smallInput.ClassListContains("daisy-input-sm") ||
                    !extraSmallInput.ClassListContains("daisy-input-xs"))
                {
                    throw new System.Exception("输入框尺寸设置失败");
                }

                // 测试最大长度限制
                var maxLengthInput = DaisyInput.Create("最大长度测试")
                    .SetLabel("最大10个字符")
                    .SetHelperText("最多输入10个字符");
                maxLengthInput.MaxLength = 10;

                // 添加到UI中进行视觉验证
                var inputContainer = new VisualElement();
                inputContainer.AddToClassList("daisy-input-test");
                inputContainer.style.paddingTop = 20;
                inputContainer.style.paddingBottom = 20;
                inputContainer.style.paddingLeft = 20;
                inputContainer.style.paddingRight = 20;
                inputContainer.style.marginBottom = 20;
                inputContainer.style.backgroundColor = new Color(0.98f, 0.98f, 0.98f, 1f);
                inputContainer.style.borderTopLeftRadius = 8;
                inputContainer.style.borderTopRightRadius = 8;
                inputContainer.style.borderBottomLeftRadius = 8;
                inputContainer.style.borderBottomRightRadius = 8;

                var heading = new Label("DaisyInput 组件测试");
                heading.style.fontSize = 18;
                heading.style.unityFontStyleAndWeight = FontStyle.Bold;
                heading.style.marginBottom = 10;
                heading.style.color = Color.black;
                inputContainer.Add(heading);

                var description = new Label("测试输入框组件的类型、状态、修饰符和交互功能");
                description.style.fontSize = 12;
                description.style.marginBottom = 15;
                description.style.color = new Color(0.5f, 0.5f, 0.5f, 1f);
                inputContainer.Add(description);

                // 使用DaisyBuilder创建表单布局
                var inputGrid = DaisyBuilder.Grid(2);
                inputGrid.AddToClassList("daisy-gap-4");

                // 基础功能测试
                var basicSection = new Label("基础功能:");
                basicSection.style.fontSize = 14;
                basicSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                basicSection.style.marginBottom = 10;
                inputContainer.Add(basicSection);

                inputGrid.Add(basicInput);
                inputGrid.Add(labelInput);
                inputContainer.Add(inputGrid);

                // 类型测试
                var typeSection = new Label("输入类型:");
                typeSection.style.fontSize = 14;
                typeSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                typeSection.style.marginBottom = 10;
                typeSection.style.marginTop = 15;
                inputContainer.Add(typeSection);

                var typeGrid = DaisyBuilder.Grid(2);
                typeGrid.AddToClassList("daisy-gap-4");
                typeGrid.Add(passwordInput.SetLabel("密码输入"));
                typeGrid.Add(emailInput.SetLabel("邮箱输入"));
                typeGrid.Add(numberInput.SetLabel("数字输入"));
                typeGrid.Add(maxLengthInput);
                inputContainer.Add(typeGrid);

                // 状态测试
                var stateSection = new Label("状态测试:");
                stateSection.style.fontSize = 14;
                stateSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                stateSection.style.marginBottom = 10;
                stateSection.style.marginTop = 15;
                inputContainer.Add(stateSection);

                var stateGrid = DaisyBuilder.Grid(2);
                stateGrid.AddToClassList("daisy-gap-4");
                stateGrid.Add(errorInput);
                stateGrid.Add(successInput);
                stateGrid.Add(warningInput);
                stateGrid.Add(infoInput);
                inputContainer.Add(stateGrid);

                // 修饰符和特殊状态
                var modifierSection = new Label("修饰符和特殊状态:");
                modifierSection.style.fontSize = 14;
                modifierSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                modifierSection.style.marginBottom = 10;
                modifierSection.style.marginTop = 15;
                inputContainer.Add(modifierSection);

                var modifierGrid = DaisyBuilder.Grid(2);
                modifierGrid.AddToClassList("daisy-gap-4");
                modifierGrid.Add(borderedInput);
                modifierGrid.Add(ghostInput);
                modifierGrid.Add(disabledInput);
                modifierGrid.Add(readonlyInput);
                modifierGrid.Add(callbackInput);
                inputContainer.Add(modifierGrid);

                // 尺寸测试
                var sizeSection = new Label("尺寸测试:");
                sizeSection.style.fontSize = 14;
                sizeSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                sizeSection.style.marginBottom = 10;
                sizeSection.style.marginTop = 15;
                inputContainer.Add(sizeSection);

                var sizeContainer = new VisualElement();
                sizeContainer.AddToClassList("daisy-gap-2");
                sizeContainer.Add(extraSmallInput.SetLabel("超小尺寸"));
                sizeContainer.Add(smallInput.SetLabel("小尺寸"));
                sizeContainer.Add(DaisyInput.Create("默认尺寸").SetLabel("默认尺寸"));
                sizeContainer.Add(largeInput.SetLabel("大尺寸"));
                inputContainer.Add(sizeContainer);

                root.Add(inputContainer);

                LogTestPass("DaisyInput 组件测试通过");
            }
            catch (System.Exception ex)
            {
                LogTestFail($"DaisyInput 组件测试失败: {ex.Message}");
            }
        }

        #endregion
    }
}
