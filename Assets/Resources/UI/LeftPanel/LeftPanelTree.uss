/* LeftPanel树组件样式 */

.blasting-tree-container {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: auto;
    min-height: 200px;
    overflow: hidden;
}

.blasting-design-tree {
    flex-grow: 1;
    background-color: var(--unity-colors-default-background);
    border-width: 1px;
    border-color: var(--unity-colors-default-border);
    border-radius: 4px;
    padding: 4px;
}

.blasting-design-tree.dark {
    background-color: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

.fallback-tree-view {
    background-color: var(--unity-colors-default-background);
    border-width: 1px;
    border-color: var(--unity-colors-default-border);
    border-radius: 4px;
}

.tree-placeholder {
    flex-grow: 1;
    justify-content: center;
    align-items: center;
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    -unity-text-align: middle-center;
    background-color: rgba(0, 0, 0, 0.2);
    border-width: 1px;
    border-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}