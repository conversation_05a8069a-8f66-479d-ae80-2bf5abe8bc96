<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="False">
    <Style src="project://database/Assets/Resources/UI/SettingsWindowStyles.uss" />
    
    <ui:VisualElement name="settings-window" class="settings-window">
        <!-- Header with Gradient Background -->
        <ui:VisualElement name="settings-header" class="settings-header">
            <ui:VisualElement name="header-content" class="header-content">
                <ui:VisualElement name="title-section" class="title-section">
                    <ui:Label text="⚙" class="settings-icon" />
                    <ui:Label text="应用程序设置" class="settings-title" />
                </ui:VisualElement>
                <ui:VisualElement name="search-section" class="search-section">
                    <ui:VisualElement name="search-container" class="search-container">
                        <ui:Label text="🔍" class="search-icon" />
                        <ui:TextField name="search-field" placeholder-text="搜索设置..." class="settings-search" />
                    </ui:VisualElement>
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
        
        <!-- Main Content Split View with Shadow -->
        <ui:VisualElement name="settings-body" class="settings-body">
            <!-- Left Sidebar Navigation with Enhanced Design -->
            <ui:VisualElement name="settings-sidebar" class="settings-sidebar">
                <ui:VisualElement name="sidebar-header" class="sidebar-header">
                    <ui:Label text="设置分类" class="sidebar-title" />
                </ui:VisualElement>
                <ui:ScrollView name="category-list" class="category-list">
                    <!-- Categories will be populated dynamically -->
                </ui:ScrollView>
            </ui:VisualElement>
            
            <!-- Vertical Separator -->
            <ui:VisualElement name="sidebar-separator" class="sidebar-separator" />
            
            <!-- Right Content Area with Better Organization -->
            <ui:VisualElement name="settings-content-area" class="settings-content-area">
                <!-- Content Header -->
                <ui:VisualElement name="content-header" class="content-header">
                    <ui:Label name="current-category-title" text="" class="current-category-title" />
                    <ui:Label name="current-category-description" text="" class="current-category-description" />
                </ui:VisualElement>
                
                <!-- Content Body with Scroll -->
                <ui:ScrollView name="settings-content" class="settings-content">
                    <!-- Content will be loaded dynamically based on selected category -->
                </ui:ScrollView>
            </ui:VisualElement>
        </ui:VisualElement>
        
        <!-- Enhanced Footer with Status -->
        <ui:VisualElement name="settings-footer" class="settings-footer">
            <ui:VisualElement name="footer-left" class="footer-left">
                <ui:Label name="status-indicator" text="● 就绪" class="status-indicator status-ready" />
                <ui:Label name="status-message" text="" class="status-message" />
            </ui:VisualElement>
            <ui:VisualElement name="footer-right" class="footer-right">
                <ui:Button name="reset-button" text="🔄 重置默认" class="settings-button settings-button-secondary" />
                <ui:Button name="cancel-button" text="✖ 取消" class="settings-button settings-button-secondary" />
                <ui:Button name="apply-button" text="✓ 应用" class="settings-button settings-button-primary" />
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>