<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/Resources/TailwindUss/index.uss" />
    <Style src="project://database/Assets/Resources/Window/ComponentLibraryWindow.uss" />
    
    <ui:VisualElement name="component-library-content" class="component-library-content theme-dark bg-base-100 p-6 grow">
        <!-- Header Section -->
        <ui:VisualElement name="header-container" class="mb-4 shrink-0">
            <ui:VisualElement name="title-row" class="flex-row items-center justify-between mb-2">
                <ui:Label name="title-label" text="DaisyUI 组件库" class="text-base-content text-2xl font-bold" />
                <ui:Button name="theme-toggle" text="🌙" class="theme-toggle-btn" />
            </ui:VisualElement>
        </ui:VisualElement>
        
        <!-- Main Content -->
        <ui:VisualElement name="main-container" class="flex-row grow">
            <!-- Left Sidebar -->
            <ui:VisualElement name="sidebar" class="sidebar bg-base-200 border border-base-300 rounded-lg p-4 mr-4">
                <ui:Label name="category-title" text="组件分类" class="text-base-content text-sm font-bold mb-3" />
                <ui:ScrollView name="category-list" class="grow">
                    <!-- Actions -->
                    <ui:VisualElement name="actions-category" class="category-group mb-4">
                        <ui:Label name="actions-header" text="Actions" class="category-header text-base-content font-semibold mb-2" />
                        <ui:Button name="buttons-item" text="Buttons" class="category-item text-left" />
                        <ui:Button name="dropdown-item" text="Dropdown" class="category-item text-left" />
                        <ui:Button name="modal-item" text="Modal" class="category-item text-left" />
                        <ui:Button name="swap-item" text="Swap" class="category-item text-left" />
                        <ui:Button name="theme-controller-item" text="Theme controller" class="category-item text-left" />
                    </ui:VisualElement>
                    
                    <!-- Data Display -->
                    <ui:VisualElement name="data-display-category" class="category-group mb-4">
                        <ui:Label name="data-display-header" text="Data Display" class="category-header text-base-content font-semibold mb-2" />
                        <ui:Button name="accordion-item" text="Accordion" class="category-item text-left" />
                        <ui:Button name="avatar-item" text="Avatar" class="category-item text-left" />
                        <ui:Button name="badge-item" text="Badge" class="category-item text-left" />
                        <ui:Button name="card-item" text="Card" class="category-item text-left" />
                        <ui:Button name="carousel-item" text="Carousel" class="category-item text-left" />
                        <ui:Button name="collapse-item" text="Collapse" class="category-item text-left" />
                        <ui:Button name="countdown-item" text="Countdown" class="category-item text-left" />
                        <ui:Button name="diff-item" text="Diff" class="category-item text-left" />
                        <ui:Button name="kbd-item" text="Kbd" class="category-item text-left" />
                        <ui:Button name="stat-item" text="Stat" class="category-item text-left" />
                        <ui:Button name="table-item" text="Table" class="category-item text-left" />
                        <ui:Button name="timeline-item" text="Timeline" class="category-item text-left" />
                    </ui:VisualElement>
                    
                    <!-- Data Input -->
                    <ui:VisualElement name="data-input-category" class="category-group mb-4">
                        <ui:Label name="data-input-header" text="Data Input" class="category-header text-base-content font-semibold mb-2" />
                        <ui:Button name="checkbox-item" text="Checkbox" class="category-item text-left" />
                        <ui:Button name="file-input-item" text="File input" class="category-item text-left" />
                        <ui:Button name="input-item" text="Input" class="category-item text-left" />
                        <ui:Button name="radio-item" text="Radio" class="category-item text-left" />
                        <ui:Button name="range-item" text="Range" class="category-item text-left" />
                        <ui:Button name="rating-item" text="Rating" class="category-item text-left" />
                        <ui:Button name="select-item" text="Select" class="category-item text-left" />
                        <ui:Button name="textarea-item" text="Textarea" class="category-item text-left" />
                        <ui:Button name="toggle-item" text="Toggle" class="category-item text-left" />
                    </ui:VisualElement>
                    
                    <!-- Layout -->
                    <ui:VisualElement name="layout-category" class="category-group mb-4">
                        <ui:Label name="layout-header" text="Layout" class="category-header text-base-content font-semibold mb-2" />
                        <ui:Button name="artboard-item" text="Artboard" class="category-item text-left" />
                        <ui:Button name="divider-item" text="Divider" class="category-item text-left" />
                        <ui:Button name="drawer-item" text="Drawer" class="category-item text-left" />
                        <ui:Button name="footer-item" text="Footer" class="category-item text-left" />
                        <ui:Button name="hero-item" text="Hero" class="category-item text-left" />
                        <ui:Button name="indicator-item" text="Indicator" class="category-item text-left" />
                        <ui:Button name="join-item" text="Join" class="category-item text-left" />
                        <ui:Button name="mask-item" text="Mask" class="category-item text-left" />
                        <ui:Button name="stack-item" text="Stack" class="category-item text-left" />
                    </ui:VisualElement>
                    
                    <!-- Navigation -->
                    <ui:VisualElement name="navigation-category" class="category-group mb-4">
                        <ui:Label name="navigation-header" text="Navigation" class="category-header text-base-content font-semibold mb-2" />
                        <ui:Button name="breadcrumbs-item" text="Breadcrumbs" class="category-item text-left" />
                        <ui:Button name="bottom-navigation-item" text="Bottom navigation" class="category-item text-left" />
                        <ui:Button name="link-item" text="Link" class="category-item text-left" />
                        <ui:Button name="menu-item" text="Menu" class="category-item text-left" />
                        <ui:Button name="navbar-item" text="Navbar" class="category-item text-left" />
                        <ui:Button name="pagination-item" text="Pagination" class="category-item text-left" />
                        <ui:Button name="steps-item" text="Steps" class="category-item text-left" />
                        <ui:Button name="tab-item" text="Tab" class="category-item text-left" />
                        <ui:Button name="tree-item" text="Tree" class="category-item text-left" />
                    </ui:VisualElement>
                    
                    <!-- Feedback -->
                    <ui:VisualElement name="feedback-category" class="category-group mb-4">
                        <ui:Label name="feedback-header" text="Feedback" class="category-header text-base-content font-semibold mb-2" />
                        <ui:Button name="alert-item" text="Alert" class="category-item text-left" />
                        <ui:Button name="loading-item" text="Loading" class="category-item text-left" />
                        <ui:Button name="progress-item" text="Progress" class="category-item text-left" />
                        <ui:Button name="radial-progress-item" text="Radial progress" class="category-item text-left" />
                        <ui:Button name="skeleton-item" text="Skeleton" class="category-item text-left" />
                        <ui:Button name="toast-item" text="Toast" class="category-item text-left" />
                        <ui:Button name="tooltip-item" text="Tooltip" class="category-item text-left" />
                    </ui:VisualElement>
                </ui:ScrollView>
            </ui:VisualElement>
            
            <!-- Right Content Area -->
            <ui:VisualElement name="content-area" class="content-area grow">
                <!-- Content Header -->
                <ui:VisualElement name="content-header" class="component-header self-start shrink-0">
                    <ui:VisualElement name="header-content" class="header-content">
                        <ui:Label name="component-name" text="选择一个组件" class="component-title" />
                        <ui:Label name="component-description" text="从左侧选择组件查看详细信息和使用示例" class="component-subtitle" />
                    </ui:VisualElement>
                </ui:VisualElement>
                
                <!-- Content Body -->
                <ui:ScrollView name="content-body" class="component-body">
                    <!-- Welcome Content -->
                    <ui:VisualElement name="welcome-content" class="welcome-section">
                        <ui:VisualElement name="welcome-header" class="welcome-header">
                            <ui:Label text="🎨" class="welcome-icon" />
                            <ui:Label text="DaisyUI 组件库" class="welcome-title" />
                            <ui:Label text="现代化的Unity UI组件库，提供丰富的组件和主题支持" class="welcome-subtitle" />
                        </ui:VisualElement>
                        
                        <!-- Component Examples Grid -->
                        <ui:VisualElement name="examples-section" class="examples-section items-start">
                            <ui:Label text="组件预览" class="section-title" />
                            <ui:VisualElement name="examples-grid" class="examples-grid">
                                <!-- Button Example -->
                                <ui:VisualElement name="example-card" class="example-card">
                                    <ui:VisualElement name="example-header" class="example-header">
                                        <ui:Label text="Button" class="example-title" />
                                        <ui:Label text="按钮组件" class="example-description" />
                                    </ui:VisualElement>
                                    <ui:VisualElement name="example-preview" class="example-preview">
                                        <ui:Button text="Primary" class="btn btn-primary mr-2" />
                                        <ui:Button text="Secondary" class="btn btn-secondary mr-2" />
                                        <ui:Button text="Outline" class="btn btn-outline" />
                                    </ui:VisualElement>
                                </ui:VisualElement>
                                
                                <!-- Input Example -->
                                <ui:VisualElement name="example-card" class="example-card">
                                    <ui:VisualElement name="example-header" class="example-header">
                                        <ui:Label text="Input" class="example-title" />
                                        <ui:Label text="输入框组件" class="example-description" />
                                    </ui:VisualElement>
                                    <ui:VisualElement name="example-preview" class="example-preview">
                                        <ui:TextField placeholder-text="请输入内容..." class="input input-bordered w-full" />
                                    </ui:VisualElement>
                                </ui:VisualElement>
                                
                                <!-- Card Example -->
                                <ui:VisualElement name="example-card" class="example-card">
                                    <ui:VisualElement name="example-header" class="example-header">
                                        <ui:Label text="Card" class="example-title" />
                                        <ui:Label text="卡片组件" class="example-description" />
                                    </ui:VisualElement>
                                    <ui:VisualElement name="example-preview" class="example-preview">
                                        <ui:VisualElement name="card" class="card bg-base-100 shadow">
                                            <ui:VisualElement name="card-body" class="card-body">
                                                <ui:Label text="Card Title" class="card-title" />
                                                <ui:Label text="This is a sample card component." class="text-base-content opacity-70" />
                                            </ui:VisualElement>
                                        </ui:VisualElement>
                                    </ui:VisualElement>
                                </ui:VisualElement>
                            </ui:VisualElement>
                        </ui:VisualElement>
                        
                        <!-- Features Section -->
                        <ui:VisualElement name="features-section" class="features-section items-start">
                            <ui:Label text="特性亮点" class="section-title" />
                            <ui:VisualElement name="features-grid" class="features-grid">
                                <ui:VisualElement name="feature-card" class="feature-card">
                                    <ui:Label text="🎯" class="feature-icon" />
                                    <ui:Label text="实时预览" class="feature-title" />
                                    <ui:Label text="查看组件在当前主题下的实际效果，支持实时主题切换" class="feature-description" />
                                </ui:VisualElement>
                                
                                <ui:VisualElement name="feature-card" class="feature-card">
                                    <ui:Label text="📝" class="feature-icon" />
                                    <ui:Label text="代码示例" class="feature-title" />
                                    <ui:Label text="提供完整的C#代码示例和详细说明文档" class="feature-description" />
                                </ui:VisualElement>
                                
                                <ui:VisualElement name="feature-card" class="feature-card">
                                    <ui:Label text="🎨" class="feature-icon" />
                                    <ui:Label text="主题支持" class="feature-title" />
                                    <ui:Label text="内置浅色/深色主题，支持自定义主题配色" class="feature-description" />
                                </ui:VisualElement>
                                
                                <ui:VisualElement name="feature-card" class="feature-card">
                                    <ui:Label text="⚡" class="feature-icon" />
                                    <ui:Label text="高性能" class="feature-title" />
                                    <ui:Label text="基于Unity UI Toolkit，提供原生性能和无缝集成" class="feature-description" />
                                </ui:VisualElement>
                            </ui:VisualElement>
                        </ui:VisualElement>
                    </ui:VisualElement>
                </ui:ScrollView>
            </ui:VisualElement>
        </ui:VisualElement>
        
        <!-- Footer -->
        <ui:VisualElement name="footer-container" class="flex-row justify-between items-center pt-4 shrink-0">
            <ui:Label name="status-label" text="就绪" class="text-base-content text-xs opacity-50" />
            <ui:VisualElement name="footer-buttons" class="flex-row">
                <ui:Button name="refresh-button" text="刷新" class="btn btn-sm btn-ghost mr-2" />
                <ui:Button name="close-button" text="关闭" class="btn btn-sm btn-neutral" />
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>